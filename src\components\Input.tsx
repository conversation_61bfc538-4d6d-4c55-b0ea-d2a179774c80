import { Component, JSX, splitProps, Show } from 'solid-js';
import { css, cx } from '../../styled-system/css';

interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: JSX.Element;
  rightIcon?: JSX.Element;
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

const Input: Component<InputProps> = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'leftIcon',
    'rightIcon',
    'size',
    'fullWidth',
    'class'
  ]);

  const containerStyles = css({
    display: 'flex',
    flexDirection: 'column',
    gap: '6px',
  });

  const fullWidthStyle = css({
    width: '100%',
  });

  const labelStyles = css({
    fontSize: '14px',
    fontWeight: '500',
    color: 'gray.700',
  });

  const inputWrapperStyles = css({
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
  });

  const baseInputStyles = css({
    width: '100%',
    border: '1px solid',
    borderColor: 'gray.300',
    borderRadius: '8px',
    transition: 'all 0.2s',
    backgroundColor: 'white',
    color: 'gray.900',
    _focus: {
      outline: 'none',
      borderColor: 'blue.500',
      boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
    },
    _disabled: {
      backgroundColor: 'gray.100',
      color: 'gray.500',
      cursor: 'not-allowed',
    },
    _placeholder: {
      color: 'gray.400',
    },
  });

  const errorInputStyles = css({
    borderColor: 'red.500',
    _focus: {
      borderColor: 'red.500',
      boxShadow: '0 0 0 3px rgba(239, 68, 68, 0.1)',
    },
  });

  const sizeStyles = {
    sm: css({
      padding: '8px 12px',
      fontSize: '14px',
      minHeight: '36px',
    }),
    md: css({
      padding: '10px 14px',
      fontSize: '14px',
      minHeight: '40px',
    }),
    lg: css({
      padding: '12px 16px',
      fontSize: '16px',
      minHeight: '48px',
    }),
  };

  const iconStyles = css({
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    color: 'gray.400',
    pointerEvents: 'none',
    zIndex: 1,
  });

  const leftIconStyles = css({
    left: '12px',
  });

  const rightIconStyles = css({
    right: '12px',
  });

  const inputWithLeftIconStyles = css({
    paddingLeft: '40px',
  });

  const inputWithRightIconStyles = css({
    paddingRight: '40px',
  });

  const helperTextStyles = css({
    fontSize: '12px',
    color: 'gray.600',
  });

  const errorTextStyles = css({
    fontSize: '12px',
    color: 'red.600',
  });

  const size = local.size || 'md';

  return (
    <div 
      class={cx(
        containerStyles,
        local.fullWidth && fullWidthStyle
      )}
    >
      {/* Label */}
      <Show when={local.label}>
        <label class={labelStyles}>
          {local.label}
        </label>
      </Show>

      {/* Input wrapper */}
      <div class={inputWrapperStyles}>
        {/* Left icon */}
        <Show when={local.leftIcon}>
          <div class={cx(iconStyles, leftIconStyles)}>
            {local.leftIcon}
          </div>
        </Show>

        {/* Input */}
        <input
          class={cx(
            baseInputStyles,
            sizeStyles[size],
            local.error && errorInputStyles,
            local.leftIcon && inputWithLeftIconStyles,
            local.rightIcon && inputWithRightIconStyles,
            local.class
          )}
          {...others}
        />

        {/* Right icon */}
        <Show when={local.rightIcon}>
          <div class={cx(iconStyles, rightIconStyles)}>
            {local.rightIcon}
          </div>
        </Show>
      </div>

      {/* Helper text or error */}
      <Show when={local.error || local.helperText}>
        <div class={local.error ? errorTextStyles : helperTextStyles}>
          {local.error || local.helperText}
        </div>
      </Show>
    </div>
  );
};

export default Input;
