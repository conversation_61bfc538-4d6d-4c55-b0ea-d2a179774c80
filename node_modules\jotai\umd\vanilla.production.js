!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jotai/vanilla/internals")):"function"==typeof define&&define.amd?define(["exports","jotai/vanilla/internals"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jotaiVanilla={},t.jotaiVanillaInternals)}(this,function(t,e){"use strict";var n,i,o=0;function r(t){return t(this)}function a(t,e,n){return e(this,"function"==typeof n?n(t(this)):n)}function f(){return n?n():e.INTERNAL_buildStoreRev1()}t.INTERNAL_overrideCreateStore=function(t){n=t(n)},t.atom=function(t,e){var n="atom"+ ++o,i={toString:function(){return n}};return"function"==typeof t?i.read=t:(i.init=t,i.read=r,i.write=a),e&&(i.write=e),i},t.createStore=f,t.getDefaultStore=function(){return i||(i=f()),i}});
