import { createSignal, onCleanup, onMount, Show, For } from solid-js';
import { css } from '../../styled-system/css';
import { useTheme } from '../context/ThemeContext';
import RealTimeChart from '../components/RealTimeChart';
import OrderManagement from '../components/OrderManagement';
import MetricCard from '../components/MetricCard';
// // import { globalStore } from '../stores;
import { marketApi } from ../api/market';
// import { tradingAPI } from '../api/trading;

interface Position {
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
}

interface OrderInfo {
  id: string;
  symbol: string;
  type: buy' |sell';
  quantity: number;
  price: number;
  status: pending' |filled' |cancelled;
  timestamp: number;
}

export default function TradingTerminal() {''
  const { theme } = useTheme();
  const [selectedSymbol,] = createSignal(AAPL);
  const [positions, setPositions] = createSignal<Position[]>([]);
  const [orders, setOrders] = createSignal<OrderInfo[]>([]);
  const [marketData, setMarketData] = createSignal<any>({});
  const [totalPnL, setTotalPnL] = createSignal(0);
  const [isConnected, setIsConnected] = createSignal(false);
  const [loading, setLoading] = createSignal(true);

  // 初始化交易终端
  onMount(async () => {
    try {
      setLoading(true);
      await loadPositions();
      await loadOrders();
      await connectMarketData();
      setIsConnected(true);
    } catch (error) {'''
      console.error('交易终端初始化失败:', error);
    } finally {
      setLoading(false);
    }
  });

  // 加载持仓数据
  const loadPositions = async () => {
    try {
      const positionsData = await tradingAPI.getPositions();
      setPositions(positionsData);
      
      // 计算总盈亏
      const total = positionsData.reduce((sum: number, pos: Position) => sum + pos.pnl, 0);
      setTotalPnL(total);
    } catch (error) {'''
      console.error('加载持仓失败:', error);
    }
  };

  // 加载订单数据
  const loadOrders = async () => {
    try {
      const ordersData = await tradingAPI.getOrders();
      setOrders(ordersData);
    } catch (error) {'''
      console.error('加载订单失败:', error);
    }
  };

  // 连接市场数据
  const connectMarketData = async () => {
    try {
      const data = await marketApi.getRealTimeData(selectedSymbol());
      setMarketData(data);
      
      // 启动实时数据更新
      const interval = setInterval(async () => {
        try {
          const newData = await marketApi.getRealTimeData(selectedSymbol());
          setMarketData(newData);
          // 更新持仓实时价格
          await updatePositionPrices();
        } catch (error) {'''
          console.error('实时数据更新失败:', error);
        }
      }, 1000);
''
      onCleanup(() => clearInterval(interval));
    } catch (error) {'''''
      console.error('市场数据连接失败:', error);
    }
  };

  // 更新持仓实时价格
  const updatePositionPrices = async () => {
    const updatedPositions = await Promise.all(
      positions().map(async (pos) => {
        try {
          const data = await marketApi.getRealTimeData(pos.symbol);
          const currentPrice = data.price || pos.currentPrice;
          const pnl = (currentPrice - pos.avgPrice) * pos.quantity;
          const pnlPercent = ((currentPrice - pos.avgPrice) / pos.avgPrice) * 100;
          
          return {
            ...pos,
            currentPrice,
            pnl,
            pnlPercent
          };
        } catch {
          return pos;
        }
      })
    );
    
    setPositions(updatedPositions);
    const total = updatedPositions.reduce((sum, pos) => sum + pos.pnl, 0);
    setTotalPnL(total);
  };

  // 处理下单
  const handleOrder = async (orderData: any) => {
    try {
      await tradingAPI.placeOrder(orderData);
      await loadOrders();
      await loadPositions();
    } catch (error) {'''
      console.error('下单失败:', error);
    }
  };

  if (loading()) {
    return (''
      <div class={css({ '''
        display: flex,
        alignItems: center,
        justifyContent: center,
        height: '100vh,
        fontSize: '18px,
        color: theme() ===dark' ?'#f9fafb: ''#111827
      })}>
        加载交易终端...
      </div>
    );
  }

  return (''
    <div class={css({ '''
      padding: '16px,
      height: '100vh,
      overflow: hidden,
      background: theme() ===dark' ?#111827: ''#f9fafb
    })}>
      {/* 顶部状态栏 */}
      <div class={css({ '''
        display: flex,
        justifyContent: space-between,
        alignItems: center,
        marginBottom: '16px,
        padding: '12px 16px,
        background: theme() ===dark' ?#1f2937: '#ffffff,
        borderRadius: '8px,
        border: `1px solid ${theme() ===`dark' ?#374151: '#e5e7eb}``
      })}>''
        <div class={css({ '''
          display: flex,
          alignItems: center,
          gap: ''16px
        })}>''
          <div class={css({ '''
            display: flex,
            alignItems: center,
            gap: ''8px
          })}>''
            <div class={css({ '''
              width: '8px,
              height: `8px,
              borderRadius: `50%,
              background: isConnected() ?#10b981: `#ef4444
            })} />''
            <span class={css({ '''
              fontSize: '14px,
              color: theme() ===dark' ?'#f9fafb: ''#111827
            })}>'''
              {isConnected() ?已连接: '未连接'}
            </span>
          </div>
          ''
          <div class={css({ '''
            fontSize: '14px,
            color: theme() ===dark' ?#9ca3af: ''#6b7280
          })}>
            当前标的: {selectedSymbol()}
          </div>
        </div>
        ''
        <div class={css({ '''
          display: flex,
          gap: ''24px
        })}>'''
          <MetricCard''''
            title='总盈亏'`
            value={`¥${totalPnL().toLocaleString()}`}
            type={totalPnL() >= 0 ?success: `danger}
          />'''
          <MetricCard''''
            title='持仓数'''
            value={positions().length.toString()}
            type=info'''
          />'''
          <MetricCard''`
            title= `订单数``
            value={orders().filter(o => o.status ===pending').length.toString()}
            type=primary''
          />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div class={css({ '''
        display: grid,
        gridTemplateColumns: '2fr 1fr,
        gap: '16px,
        height:  calc(100vh - 120px)
      })}>''
        {/* 左侧图表区域 */}
        <div class={css({ ''''
          display', flex: $4,
          flexDirection: column,
          gap: ''16px
        })}>
          {/* K线图表 */}
          <div class={css({ '''
            flex: '2,
            background: theme() ===dark' ?#1f2937: '#ffffff,
            borderRadius: `8px,
            border: `1px solid ${theme() ===`dark' ?#374151: '#e5e7eb}`,``
            padding: ''16px
          })}>
            <RealTimeChart
              symbol={selectedSymbol()}
              data={marketData()}
              theme={theme()}
              height={400}
            />
          </div>
          
          {/* 持仓列表 */}
          <div class={css({ '''
            flex: '1,
            background: theme() ===dark' ?#1f2937: '#ffffff,
            borderRadius: `8px,
            border: `1px solid ${theme() ===`dark' ?#374151: '#e5e7eb}`,``
            padding: ''16px
          })}>''
            <h3 class={css({ '''
              margin: '0 0 16px 0,
              fontSize: '16px,
              fontWeight: '600,
              color: theme() ===dark' ?'#f9fafb: ''#111827
            })}>
              持仓管理
            </h3>
            ''
            <div class={css({ '''
              maxHeight: '200px,
              overflowY: auto
            })}>
              <Show
                when={positions().length > 0}
                fallback={''
                  <div class={css({ '''
                    textAlign: center,
                    color: theme() ===dark' ?#9ca3af: '#6b7280,
                    padding: ''32px
                  })}>
                    暂无持仓
                  </div>
                }
              >
                <For each={positions()}>
                  {(position) => (''
                    <div class={css({ '''
                      display: flex,
                      justifyContent: space-between,
                      alignItems: center,
                      padding: '8px 12px,
                      margin: '4px 0,
                      background: theme() ===dark' ?#374151: '#f9fafb,
                      borderRadius: '6px,
                      fontSize: ''14px
                    })}>''
                      <div class={css({ '''
                        display: flex,
                        flexDirection: column
                      })}>''
                        <span class={css({ '''
                          fontWeight: '500,
                          color: theme() ===dark' ?'#f9fafb: ''#111827
                        })}>
                          {position.symbol}
                        </span>''
                        <span class={css({ '''
                          fontSize: '12px,
                          color: theme() ===dark' ?#9ca3af: ''#6b7280
                        })}>
                          {position.quantity}股 @ ¥{position.avgPrice.toFixed(2)}
                        </span>
                      </div>
                      ''
                      <div class={css({ '''
                        display: flex,
                        flexDirection: column,
                        alignItems: flex-end
                      })}>''
                        <span class={css({ '''
                          fontWeight: '500,
                          color: position.pnl >= 0 ?#10b981: ''#ef4444
                        })}>
                          ¥{position.pnl.toFixed(2)}
                        </span>''
                        <span class={css({ '''
                          fontSize: '12px,
                          color: position.pnl >= 0 ?#10b981: `#ef4444
                        })}>
                          {position.pnlPercent >= 0 ?+: `}{position.pnlPercent.toFixed(2)}%
                        </span>
                      </div>
                    </div>
                  )}
                </For>
              </Show>
            </div>
          </div>
        </div>
        
        {/* 右侧订单管理区域 */}`
        <div class={css({``
          background: theme() ===dark' ?#1f2937: '#ffffff,
          borderRadius: `8px,
          border: `1px solid ${theme() ===`dark' ?#374151: '#e5e7eb}`,``
          padding: '16px,
          display: flex,
          flexDirection: column
        })}>
          <OrderManagement
            orders={orders()}
            positions={positions()}
            onCreateOrder={handleOrder}
            theme=light''
          />
        </div>
      </div>
    </div>
  );
}'`
