const fs = require('fs');
const path = require('path');

// 终极语法修复脚本
function fixUltimateSyntax() {
  const srcDir = path.join(__dirname, 'src');
  
  // 递归获取所有 .ts 和 .tsx 文件
  function getAllTsFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...getAllTsFiles(fullPath));
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
    
    return files;
  }
  
  const files = getAllTsFiles(srcDir);
  let totalFixed = 0;
  
  for (const filePath of files) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      const originalContent = content;
      
      // 1. 修复导入语句中的双引号问题
      content = content.replace(/from\s+['"]([^'"]*)['"]{2,}/g, "from '$1'");
      content = content.replace(/import\s+['"]([^'"]*)['"]{2,}/g, "import '$1'");
      content = content.replace(/from\s+([^'"]*)['"]\s*$/gm, "from '$1'");
      content = content.replace(/from\s+([^'"]*)\s*$/gm, "from '$1'");
      
      // 2. 修复未终止的字符串字面量
      content = content.replace(/(['"])([^'"]*)\n/g, '$1$2$1\n');
      content = content.replace(/(['"])([^'"]*?)$/gm, '$1$2$1');
      
      // 3. 修复对象属性中的语法错误
      content = content.replace(/:\s*(['"][^'"]*)['"]{2,}/g, ": '$1'");
      content = content.replace(/:\s*([^,}]+),\s*['"]+/g, ": $1,");
      
      // 4. 修复类型定义中的语法错误
      content = content.replace(/:\s*(['"][^'"]*)['"]\s*\|/g, ": '$1' |");
      content = content.replace(/\|\s*(['"][^'"]*)['"]{2,}/g, "| '$1'");
      
      // 5. 修复函数参数中的语法错误
      content = content.replace(/\(\s*(['"][^'"]*)['"]{2,}/g, "('$1'");
      content = content.replace(/,\s*(['"][^'"]*)['"]{2,}/g, ", '$1'");
      
      // 6. 修复数组中的语法错误
      content = content.replace(/\[\s*(['"][^'"]*)['"]{2,}/g, "['$1'");
      
      // 7. 修复 case 语句中的语法错误
      content = content.replace(/case\s+(['"][^'"]*)['"]{2,}/g, "case '$1'");
      
      // 8. 修复模板字符串中的问题
      content = content.replace(/`([^`]*)['"]{2,}/g, "`$1`");
      
      // 9. 修复 console.log 等函数调用中的语法错误
      content = content.replace(/console\.(log|warn|error)\s*\(\s*([^'"()]+)\s*,/g, "console.$1('$2',");
      
      // 10. 修复对象字面量中的语法错误
      content = content.replace(/{\s*(['"][^'"]*)['"]\s*:/g, "{ $1:");
      
      // 11. 修复三元表达式中的语法错误
      content = content.replace(/\?\s*(['"][^'"]*)['"]\s*:\s*(['"][^'"]*)['"]/g, "? '$1' : '$2'");
      
      // 12. 修复行末的分号和引号问题
      content = content.replace(/;['"]+$/gm, ';');
      content = content.replace(/}['"]+$/gm, '}');
      content = content.replace(/\)['"]+$/gm, ')');
      content = content.replace(/\]['"]+$/gm, ']');
      
      // 13. 修复错误的对象属性语法
      content = content.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*(['"][^'"]*)['"]\s*['"]?\s*,/g, '$1: $2,');
      
      // 14. 修复错误的函数调用语法
      content = content.replace(/\(\s*(['"][^'"]*)['"]\s*['"]?\s*\)/g, '($1)');
      
      // 15. 修复错误的数组元素语法
      content = content.replace(/,\s*(['"][^'"]*)['"]\s*['"]?\s*]/g, ', $1]');
      
      // 16. 修复错误的 JSX 属性语法
      content = content.replace(/=\s*(['"][^'"]*)['"]\s*['"]?\s*>/g, '=$1>');
      
      // 17. 修复特定的语法错误模式
      content = content.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*([^,}]+),\s*['"]+/g, '$1: $2,');
      content = content.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*([^,}]+)['"]+/g, '$1: $2');
      
      // 18. 修复模板字符串中的错误
      content = content.replace(/`([^`]*?)['"]+/g, '`$1`');
      
      // 19. 修复字符串连接错误
      content = content.replace(/(['"])([^'"]*)\1\s*\+\s*\1([^'"]*)\1/g, '$1$2$3$1');
      
      // 20. 修复特殊字符问题
      content = content.replace(/([^a-zA-Z0-9_$])['"]+([a-zA-Z_$][a-zA-Z0-9_$]*)/g, '$1$2');
      
      // 检查是否有变化
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Fixed: ${path.relative(__dirname, filePath)}`);
        totalFixed++;
        hasChanges = true;
      }
    } catch (error) {
      console.log(`❌ Error processing ${filePath}: ${error.message}`);
    }
  }
  
  console.log(`\n✨ Fixed ${totalFixed} files with ultimate syntax errors.`);
}

fixUltimateSyntax();
