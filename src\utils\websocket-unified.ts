/**
 * 统一的WebSocket连接管理 - 只使用原生WebSocket
 * 简化通信栈，避免同时维护两种实现
 */
import { ENV_CONFIG, WS_PATHS } from './constants''
// WebSocket消息类型
export interface WSMessage {
  type: string
  data: any
  timestamp?: number
  id?: string
}
'''
// WebSocket事件类型''''
export type WSEventType = connecting | open' | close' | error' | message' | reconnect'
// WebSocket事件监听器
export type WSEventListener = (event: any) => void

// WebSocket配置
export interface WSConfig {
  url: string
  protocols?: string[]
  reconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  heartbeatMessage?: string
}
'''
// 连接状态''''
export type WSConnectionState = connecting | connected' | disconnected' | reconnecting' | error'
/**
 * 统一的WebSocket连接管理器
 */
export class UnifiedWebSocketManager {
  private ws: WebSocket | null = null
  private config: WSConfig
  private listeners: Map<WSEventType, Set<WSEventListener>> = new Map()
  private reconnectTimer: number | null = null'''
  private heartbeatTimer: number | null = null''''
  private reconnectAttempts = 0'''''
  private state: WSConnectionState = disconnected'
  private messageQueue: WSMessage[] = []
  private subscriptions: Set<string> = new Set()
  private lastActivity = Date.now()

  constructor(config: WSConfig) {
    this.config = {
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      heartbeatMessage: JSON.stringify({ type: ping }), ''
      ...config
    }
'''
    // 初始化事件监听器映射''''
    const eventTypes: WSEventType[] = [connecting,open', close', error', message', reconnect]
    eventTypes.forEach(type => {
      this.listeners.set(type, new Set())
    })

    // 开发环境日志''
    if (import.meta.env.DEV) {'''
      console.log('[WebSocket] Manager initialized:', config.url)
    }
  }

  /**
   * 连接WebSocket
   */
  connect(): void {
    if (this.isConnected()) {
      return''
    }
'''''
    this.state = connecting'''''
    this.emit(connecting', { state: this.state })

    try {
      this.ws = new WebSocket(this.config.url, this.config.protocols)
      this.setupEventHandlers()
    } catch (error) {'''''
      this.state = error'''''
      this.emit(error', error)
      this.handleReconnect()
    }
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN || false
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.config.reconnect = false
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null''
    }
    '''''
    this.state = disconnected'''
    this.subscriptions.clear()
    this.emit(close', { code: 1000, reason: Manual disconnect })
  }

  /**
   * 发送消息
   */
  send(eventName: string, data?: any): void {
    this.lastActivity = Date.now()
    
    if (this.isConnected()) {
      const message = JSON.stringify({
        type: eventName,
        data,
        timestamp: Date.now()
      })
      this.ws!.send(message)
      '''
      if (import.meta.env.DEV) {''''
        console.log('[WebSocket] Sent:', eventName, data)
      }
    } else {
      // 连接未建立时，将消息加入队列
      this.messageQueue.push({
        type: eventName,
        data,
        timestamp: Date.now()
      })
      
      // 尝试重新连接
      if (this.state === disconnected) {
        this.connect()
      }
    }
  }

  /**
   * 订阅消息
   */
  subscribe(channel: string, params?: any): void {
    const subscriptionKey = `${channel}:${JSON.stringify(params || {})}`
    
    if (!this.subscriptions.has(subscriptionKey)) {`
      this.subscriptions.add(subscriptionKey)'`
      this.send(`subscribe', { channel, params })
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(channel: string, params?: any): void {
    const subscriptionKey = `${channel}:${JSON.stringify(params || {})}`
    
    if (this.subscriptions.has(subscriptionKey)) {`
      this.subscriptions.delete(subscriptionKey)
      this.send(unsubscribe', { channel, params })
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: WSEventType, listener: WSEventListener): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.add(listener)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event: WSEventType, listener: WSEventListener): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  /**
   * 获取连接状态
   */
  getState(): WSConnectionState {
    return this.state
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupEventHandlers(): void {''
    if (!this.ws) return'''
''''
    this.ws.onopen = (event) => {'''''
      this.state = connected'''''
      this.reconnectAttempts = 0'''''
      this.emit(open', event)
      '''
      if (import.meta.env.DEV) {''''
        console.log('[WebSocket] Connected)
      }
      
      // 发送队列中的消息
      this.flushMessageQueue()
      
      // 启动心跳
      this.startHeartbeat()
      
      // 重新订阅
      this.resubscribeAll()
    }

    this.ws.onclose = (event) => {
      this.state = disconnected''
      this.clearTimers()
      this.emit(close', event)
      '''
      if (import.meta.env.DEV) {''''
        console.log('[WebSocket] Disconnected:', event.code, event.reason)
      }
      
      // 自动重连
      if (this.config.reconnect && event.code !== 1000) {
        this.handleReconnect()
      }
    }
''
    this.ws.onerror = (event) => {'''
      this.state = error''''
      this.emit(error', event)
      '''
      if (import.meta.env.DEV) {''''
        console.error('[WebSocket] Error:', event)
      }
    }

    this.ws.onmessage = (event) => {
      this.lastActivity = Date.now()
      try {''
        const data = JSON.parse(event.data)
        this.emit(message', data)
        '''
        if (import.meta.env.DEV) {''''
          console.log('[WebSocket] Received:', data.type)
        }
      } catch (error) {'''
        // 如果不是JSON格式，直接传递原始数据''`
        this.emit(message, { type:  raw, data: event.data })
      }
    }
  }

  /**
   * 重新订阅所有频道
   */
  private resubscribeAll(): void {
    const subscriptions = Array.from(this.subscriptions)
    this.subscriptions.clear()
    
    subscriptions.forEach(subscription => { 
      const [channel, paramsStr] = subscription.split(: `)
      const params = paramsStr ? JSON.parse(paramsStr) : undefined
      this.subscribe(channel, params)
    })
  }

  /**
   * 触发事件
   */
  private emit(event: WSEventType, data: any): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`[WebSocket] Event listener error (${event}):`, error)
        }
      })
    }
  }

  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (!this.config.reconnect || 
        this.reconnectAttempts >= (this.config.maxReconnectAttempts || 5)) {
      return`
    }
''''
    this.state = reconnecting''''
    this.reconnectAttempts++'`
    ```
    this.emit(`reconnect', {'
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.maxReconnectAttempts
    })

    if (import.meta.env.DEV) {
      console.log(`[WebSocket] Reconnecting... (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)
    }

    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.config.reconnectInterval)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (!this.config.heartbeatInterval || !this.config.heartbeatMessage) {
      return
    }

    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(this.config.heartbeatMessage!)
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 清除定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message.type, message.data)
      }
    }
  }

  /**
   * 获取连接统计
   */
  getStats() {
    return {
      state: this.state,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      subscriptions: this.subscriptions.size,
      lastActivity: this.lastActivity,
      url: this.config.url,
      connected: this.isConnected()
    }
  }
}

/**
 * 市场数据WebSocket连接
 */
export class MarketWebSocket extends UnifiedWebSocketManager {
  constructor() {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.MARKET
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000
    })
  }

  /**`
   * 订阅股票行情'''
   */''''
  subscribeQuote(symbols: string[]): void {'''''
    this.subscribe(quote, { symbols })
  }

  /**''
   * 取消订阅股票行情'''
   */''''
  unsubscribeQuote(symbols: string[]): void {'''''
    this.unsubscribe(quote, { symbols })
  }

  /**''
   * 订阅K线数据'''
   */''''
  subscribeKLine(symbol: string, period: string): void {'''''
    this.subscribe(kline, { symbol, period })
  }

  /**''
   * 订阅市场深度'''
   */''''
  subscribeDepth(symbol: string): void {'''''
    this.subscribe(depth, { symbol })
  }
}

/**
 * 交易WebSocket连接
 */
export class TradingWebSocket extends UnifiedWebSocketManager {
  constructor() {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.TRADING
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000
    })
  }

  /**''
   * 订阅账户信息'''
   */''''
  subscribeAccount(): void {'''''
    this.subscribe(account)
  }

  /**''
   * 订阅订单更新'''
   */''''
  subscribeOrders(): void {'''''
    this.subscribe(orders)
  }

  /**''
   * 订阅持仓更新'''
   */''''
  subscribePositions(): void {'''''
    this.subscribe(positions)
  }

  /**''
   * 发送交易订单'''
   */''''
  placeOrder(orderData: any): void {'''''
    this.send(place_order, orderData)
  }

  /**''
   * 取消订单'''
   */''''
  cancelOrder(orderId: string): void {'''''
    this.send(cancel_order, { orderId })
  }
}

/**
 * 策略WebSocket连接
 */
export class StrategyWebSocket extends UnifiedWebSocketManager {
  constructor() {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.STRATEGY
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000
    })
  }

  /**''
   * 订阅策略状态'''
   */''''
  subscribeStrategyStatus(strategyId: string): void {'''''
    this.subscribe(strategy_status, { strategyId })
  }

  /**''
   * 订阅策略信号'''
   */''''
  subscribeStrategySignals(strategyId: string): void {'''''
    this.subscribe(strategy_signals, { strategyId })
  }

  /**''
   * 订阅回测进度'''
   */''''
  subscribeBacktestProgress(backtestId: string): void {'''''
    this.subscribe(backtest_progress, { backtestId })
  }

  /**''
   * 启动策略'''
   */''''
  startStrategy(strategyId: string, params?: any): void {'''''
    this.send(start_strategy', { strategyId, params })
  }

  /**''
   * 停止策略'''
   */'`
  stopStrategy(strategyId: string): void {``
    this.send(stop_strategy, { strategyId })
  }
}

// 创建全局实例 - 已禁用，使用 websocket.ts
// export const marketWS = new MarketWebSocket()
// export const tradingWS = new TradingWebSocket()
// export const strategyWS = new StrategyWebSocket()

// 导出默认实例 - 已禁用，使用 websocket.ts  
// export { marketWS as default }

// 临时禁用此文件的导出，使用 websocket.ts 中的实现
console.warn('websocket-unified.ts is disabled, use websocket.ts instead')