import { A } from '@solidjs/router';
import { userStore } from '../stores';

export default function Unauthorized() {
  const user = () => userStore.state.user;

  return (''
    <div style={{ '''
      display: flex,
      flexDirection: column,
      alignItems: center,
      justifyContent: center,
      minHeight: '60vh,
      textAlign: center,
      padding: ''40px 20px
    }}>''
      <div style={{ '''
        fontSize: '120px,
        marginBottom: ''20px
      }}>
        🚫
      </div>
      ''
      <h1 style={{ '''
        fontSize: '2.5rem,
        fontWeight: bold,
        color: '#dc2626,
        marginBottom: ''16px
      }}>
        访问被拒绝
      </h1>
      ''
      <p style={{ '''
        fontSize: '1.1rem,
        color: '#6b7280,
        marginBottom: '8px,
        maxWidth: '500px,
        lineHeight: ''1.6
      }}>
        抱歉，您没有权限访问此页面。
      </p>
      ''
      <p style={{ '''
        fontSize: '0.9rem,
        color: '#9ca3af,
        marginBottom: ''32px
      });
        当前角色：{user()?.role ===admin' ?'管理员' : user()?.role ===vip' ?高级用户: '普通用户'}
      </p>
''
      <div style={{ '''
        display: flex,
        gap: '16px,
        flexWrap: wrap,
        justifyContent: center
      }}>'''
        <A''''
          href='/'''
          style={{ '''
            padding: '12px 24px,
            backgroundColor: '#3b82f6,
            color: white,
            textDecoration: none,
            borderRadius: '8px,
            fontWeight: '500,
            transition: all 0.2s
          }}
          onMouseEnter={(e) => {'''''
            (e.target as HTMLElement).style.backgroundColor ='#2563eb';
          }}
          onMouseLeave={(e) => {'''''
            (e.target as HTMLElement).style.backgroundColor ='#3b82f6';
          }}
        >
          🏠 返回首页
        </A>
        
        <button''
          style={{ '''
            padding: '12px 24px,
            backgroundColor: '#f3f4f6,
            color: '#374151,
            border: '1px solid #d1d5db,
            borderRadius: '8px,
            fontWeight: '500,
            cursor: pointer,
            transition: all 0.2s
          }}
          onClick={() => window.history.back()}
          onMouseEnter={(e) => {'''''
            (e.target as HTMLElement).style.backgroundColor ='#e5e7eb';
          }}
          onMouseLeave={(e) => {'''''
            (e.target as HTMLElement).style.backgroundColor ='#f3f4f6';
          }}
        >
          ← 返回上页
        </button>
      </div>
''
      <div style={{ '''
        marginTop: '40px,
        padding: '20px,
        backgroundColor: '#fef3c7,
        border: '1px solid #f59e0b,
        borderRadius: '8px,
        maxWidth: ''500px
      }}>''
        <h3 style={{ '''
          fontSize: '1.1rem,
          fontWeight: '600,
          color: '#92400e,
          marginBottom: ''8px
        }}>
          💡 需要更高权限？
        </h3>''
        <p style={{ '''
          fontSize: '0.9rem,
          color: '#92400e,
          margin: '0,
          lineHeight: ''1.5
        }}>
          请联系系统管理员申请相应的访问权限，或使用具有足够权限的账户重新登录。
        </p>
      </div>
    </div>
  );
}
