{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "jsxImportSource": "solid-js", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/context/*": ["./src/context/*"], "@/workers/*": ["./src/workers/*"]}}, "include": ["src/index.tsx", "src/App.tsx", "src/pages/Dashboard.tsx", "src/pages/Login.tsx", "src/context/ThemeContext.tsx", "src/context/I18nContext.tsx", "src/context/JotaiProvider.tsx", "src/vite-env.d.ts", "styled-system/**/*"], "exclude": ["node_modules", "dist", "src_backup", "src/api", "src/utils", "src/stores", "src/workers", "src/i18n", "src/components", "src/EnhancedApp.tsx", "src/AppAdvanced.tsx", "src/App_backup.tsx", "src/TestApp.tsx"]}