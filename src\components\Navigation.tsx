import { Component, For } from 'solid-js';
import { A, useLocation } from '@solidjs/router';
import { css, cx } from '../../styled-system/css';
import { useTheme } from '../context/ThemeContext';
import { useI18n } from '../context/I18nContext';
import { Button } from './index';

interface NavItem {
  path: string;
  label: string;
  icon: string;
}

const Navigation: Component = () => {
  const { theme, toggleTheme } = useTheme();
  const { language, setLanguage, t } = useI18n();
  const location = useLocation();

  const navItems: NavItem[] = [
    { path: '/', label: t('nav.dashboard'), icon: '📊' },
    { path: '/trading', label: t('nav.trading'), icon: '💼' },
    { path: '/strategy', label: t('nav.strategy'), icon: '🤖' },
  ];

  const containerStyles = css({
    backgroundColor: theme() === 'dark' ? 'gray.800' : 'white',
    borderBottom: '1px solid',
    borderColor: theme() === 'dark' ? 'gray.700' : 'gray.200',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    position: 'sticky',
    top: 0,
    zIndex: 50,
  });

  const navStyles = css({
    maxWidth: '1400px',
    margin: '0 auto',
    padding: '0 24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '64px',
  });

  const logoStyles = css({
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    fontSize: '20px',
    fontWeight: 'bold',
    color: theme() === 'dark' ? 'gray.100' : 'gray.900',
    textDecoration: 'none',
  });

  const navLinksStyles = css({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    '@media (max-width: 768px)': {
      display: 'none',
    },
  });

  const navLinkStyles = css({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    borderRadius: '8px',
    fontSize: '14px',
    fontWeight: '500',
    textDecoration: 'none',
    transition: 'all 0.2s',
    color: theme() === 'dark' ? 'gray.300' : 'gray.600',
    _hover: {
      backgroundColor: theme() === 'dark' ? 'gray.700' : 'gray.100',
      color: theme() === 'dark' ? 'gray.100' : 'gray.900',
    },
  });

  const activeNavLinkStyles = css({
    backgroundColor: theme() === 'dark' ? 'blue.900' : 'blue.50',
    color: theme() === 'dark' ? 'blue.300' : 'blue.600',
    _hover: {
      backgroundColor: theme() === 'dark' ? 'blue.800' : 'blue.100',
      color: theme() === 'dark' ? 'blue.200' : 'blue.700',
    },
  });

  const toolsStyles = css({
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  });

  const toolButtonStyles = css({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '40px',
    height: '40px',
    borderRadius: '8px',
    border: 'none',
    backgroundColor: 'transparent',
    color: theme() === 'dark' ? 'gray.300' : 'gray.600',
    cursor: 'pointer',
    transition: 'all 0.2s',
    _hover: {
      backgroundColor: theme() === 'dark' ? 'gray.700' : 'gray.100',
      color: theme() === 'dark' ? 'gray.100' : 'gray.900',
    },
  });

  const languageButtonStyles = css({
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    padding: '6px 12px',
    borderRadius: '6px',
    border: 'none',
    backgroundColor: 'transparent',
    color: theme() === 'dark' ? 'gray.300' : 'gray.600',
    fontSize: '12px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'all 0.2s',
    _hover: {
      backgroundColor: theme() === 'dark' ? 'gray.700' : 'gray.100',
      color: theme() === 'dark' ? 'gray.100' : 'gray.900',
    },
  });

  const isActivePath = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <header class={containerStyles}>
      <nav class={navStyles}>
        {/* Logo */}
        <A href="/" class={logoStyles}>
          <span>⚡</span>
          <span>QuantTrade</span>
        </A>

        {/* Navigation Links */}
        <div class={navLinksStyles}>
          <For each={navItems}>
            {(item) => (
              <A
                href={item.path}
                class={cx(
                  navLinkStyles,
                  isActivePath(item.path) && activeNavLinkStyles
                )}
              >
                <span>{item.icon}</span>
                <span>{item.label}</span>
              </A>
            )}
          </For>
        </div>

        {/* Tools */}
        <div class={toolsStyles}>
          {/* Language Toggle */}
          <button
            type="button"
            onClick={() => setLanguage(language() === 'zh-CN' ? 'en-US' : 'zh-CN')}
            class={languageButtonStyles}
            title="切换语言 / Switch Language"
          >
            <span>🌐</span>
            <span>{language() === 'zh-CN' ? 'EN' : '中'}</span>
          </button>

          {/* Theme Toggle */}
          <button
            type="button"
            onClick={toggleTheme}
            class={toolButtonStyles}
            title={theme() === 'dark' ? '切换到亮色主题' : '切换到暗色主题'}
          >
            <span style={{ fontSize: '18px' }}>
              {theme() === 'dark' ? '☀️' : '🌙'}
            </span>
          </button>
        </div>
      </nav>
    </header>
  );
};

export default Navigation;
