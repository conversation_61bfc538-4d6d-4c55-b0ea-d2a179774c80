import { createContext, useContext, type ParentProps } from 'solid-js';
import { Provider } from 'jotai';

// Jotai Context for SolidJS integration
interface JotaiContextValue {
  isJotaiEnabled: boolean;
}

const JotaiContext = createContext<JotaiContextValue>();

export default function JotaiProvider(props: ParentProps) {
  const contextValue: JotaiContextValue = {
    isJotaiEnabled: true,
  };

  return (
    <JotaiContext.Provider value={contextValue}>
      <Provider>
        {props.children}
      </Provider>
    </JotaiContext.Provider>
  );
}

export function useJotaiContext() {
  const context = useContext(JotaiContext);
  if (!context) {
    throw new Error('useJotaiContext must be used within a JotaiProvider');
  }
  return context;
}

// Re-export Jotai hooks for convenience
export { atom, useAtom, useAtomValue, useSetAtom } from 'jotai';
