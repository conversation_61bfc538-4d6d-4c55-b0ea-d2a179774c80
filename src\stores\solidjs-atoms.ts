/**
 * SolidJS 原生状态管理
 * 替代 Jotai 的状态管理方案
 */

import { createStore } from 'solid-js/store';
import { createMemo } from 'solid-js';

// 用户相关状态
export interface User {
  id: string;
  username: string;
  email: string;
  nickname: string;
  avatar?: string;
  phone?: string;
  role: 'admin' | 'user' | 'trader';
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  lastLoginAt: string;
  preferences: {
    theme: 'light' | 'dark';
    language: 'zh-CN' | 'en-US';
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    trading: {
      confirmOrders: boolean;
      showRiskWarnings: boolean;
      defaultOrderType: 'market' | 'limit';
    };
  };
  profile: {
    realName: string;
    gender: 'male' | 'female';
    investmentExperience: 'beginner' | 'intermediate' | 'expert';
    riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  };
}

export interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// 用户状态 Store
export const [userStore, setUserStore] = createStore<UserState>({
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null
});

// 交易相关状态
export interface TradingState {
  account: {
    id: string;
    availableCash: number;
    totalAssets: number;
    dayPnl: number;
  } | null;
  orders: any[];
  positions: any[];
  trades: any[];
  isConnected: boolean;
  loading: boolean;
  error: string | null;
}

// 交易状态 Store
export const [tradingStore, setTradingStore] = createStore<TradingState>({
  account: null,
  orders: [],
  positions: [],
  trades: [],
  isConnected: false,
  loading: false,
  error: null
});

// 市场数据状态
export interface MarketDataState {
  realTime: Record<string, {
    symbol: string;
    price: number;
    change: number;
    changePercent: number;
    volume: number;
    timestamp: number;
  }>;
  charts: Record<string, any[]>;
  watchlist: string[];
  loading: boolean;
  error: string | null;
}

// 市场数据 Store
export const [marketStore, setMarketStore] = createStore<MarketDataState>({
  realTime: {},
  charts: {},
  watchlist: [],
  loading: false,
  error: null
});

// 通知相关状态
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
}

export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
}

export const [notificationStore, setNotificationStore] = createStore<NotificationState>({
  notifications: [],
  unreadCount: 0
});

// 策略相关状态
export interface Strategy {
  id: string;
  name: string;
  description: string;
  type: 'trend' | 'mean_reversion' | 'arbitrage' | 'custom';
  status: 'draft' | 'active' | 'paused' | 'stopped';
  code: string;
  parameters: Record<string, any>;
  performance: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface StrategyState {
  strategies: Strategy[];
  activeStrategy: Strategy | null;
  loading: boolean;
  error: string | null;
}

export const [strategyStore, setStrategyStore] = createStore<StrategyState>({
  strategies: [],
  activeStrategy: null,
  loading: false,
  error: null
});

// 回测相关状态
export interface BacktestResult {
  id: string;
  strategyId: string;
  status: 'running' | 'completed' | 'failed';
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalCapital: number;
  totalReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  trades: number;
  winRate: number;
  createdAt: string;
}

export interface BacktestState {
  results: BacktestResult[];
  currentResult: BacktestResult | null;
  isRunning: boolean;
  loading: boolean;
  error: string | null;
}

export const [backtestStore, setBacktestStore] = createStore<BacktestState>({
  results: [],
  currentResult: null,
  isRunning: false,
  loading: false,
  error: null
});

// 应用主题状态
export interface ThemeState {
  mode: 'light' | 'dark';
  primaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
}

export const [themeStore, setThemeStore] = createStore<ThemeState>({
  mode: 'light',
  primaryColor: '#409eff',
  fontSize: 'medium'
});

// WebSocket连接状态
export type WSConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface ConnectionState {
  trading: WSConnectionStatus;
  marketData: WSConnectionStatus;
  lastConnectedAt: string | null;
}

export const [connectionStore, setConnectionStore] = createStore<ConnectionState>({
  trading: 'disconnected',
  marketData: 'disconnected',
  lastConnectedAt: null
});

// 模态框状态
export interface ModalState {
  isOpen: boolean;
  type: string;
  data?: any;
}

export const [modalStore, setModalStore] = createStore<ModalState>({
  isOpen: false,
  type: '',
  data: null
});

// 计算属性 (使用 createMemo)
export const isUserLoggedIn = createMemo(() => userStore.isAuthenticated && userStore.user !== null);

export const totalAssets = createMemo(() => tradingStore.account?.totalAssets || 0);

export const unreadNotifications = createMemo(() => 
  notificationStore.notifications.filter(n => !n.read)
);

export const activeStrategies = createMemo(() => 
  strategyStore.strategies.filter(s => s.status === 'active')
);

// 动作函数
export const userActions = {
  login: (user: User) => {
    setUserStore({
      user,
      isAuthenticated: true,
      loading: false,
      error: null
    });
  },

  logout: () => {
    setUserStore({
      user: null,
      isAuthenticated: false,
      loading: false,
      error: null
    });
  },

  setLoading: (loading: boolean) => {
    setUserStore('loading', loading);
  },

  setError: (error: string | null) => {
    setUserStore('error', error);
  }
};

export const tradingActions = {
  setAccount: (account: TradingState['account']) => {
    setTradingStore('account', account);
  },

  addOrder: (order: any) => {
    setTradingStore('orders', orders => [...orders, order]);
  },

  updateOrder: (orderId: string, updates: any) => {
    setTradingStore('orders', order => order.id === orderId, updates);
  },

  setConnected: (connected: boolean) => {
    setTradingStore('isConnected', connected);
  }
};

export const marketActions = {
  updateRealTime: (symbol: string, data: MarketDataState['realTime'][string]) => {
    setMarketStore('realTime', symbol, data);
  },

  addToWatchlist: (symbol: string) => {
    setMarketStore('watchlist', watchlist => [...watchlist, symbol]);
  },

  removeFromWatchlist: (symbol: string) => {
    setMarketStore('watchlist', watchlist => watchlist.filter(s => s !== symbol));
  }
};

export const notificationActions = {
  add: (notification: Omit<Notification, 'id'>) => {
    const newNotification = {
      ...notification,
      id: `notif_${Date.now()}_${Math.random()}`
    };
    setNotificationStore('notifications', notifications => [...notifications, newNotification]);
    setNotificationStore('unreadCount', count => count + 1);
  },

  markAsRead: (id: string) => {
    setNotificationStore('notifications', notif => notif.id === id, 'read', true);
    setNotificationStore('unreadCount', count => Math.max(0, count - 1));
  },

  remove: (id: string) => {
    const notification = notificationStore.notifications.find(n => n.id === id);
    setNotificationStore('notifications', notifications => notifications.filter(n => n.id !== id));
    if (notification && !notification.read) {
      setNotificationStore('unreadCount', count => Math.max(0, count - 1));
    }
  }
};

export const themeActions = {
  setMode: (mode: 'light' | 'dark') => {
    setThemeStore('mode', mode);
  },

  setPrimaryColor: (color: string) => {
    setThemeStore('primaryColor', color);
  },

  setFontSize: (size: 'small' | 'medium' | 'large') => {
    setThemeStore('fontSize', size);
  }
};

export const modalActions = {
  open: (type: string, data?: any) => {
    setModalStore({
      isOpen: true,
      type,
      data
    });
  },

  close: () => {
    setModalStore({
      isOpen: false,
      type: '',
      data: null
    });
  }
};