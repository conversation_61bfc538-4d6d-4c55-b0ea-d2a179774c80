/**
 * WebSocket连接管理 (支持原生WebSocket和Socket.IO)
 */
import { ENV_CONFIG, WS_PATHS } from './constants'
import { io, Socket } from 'socket.io-client'

// WebSocket消息类型
export interface WSMessage {
  type: string
  data: any
  timestamp?: number
  id?: string
}

// WebSocket事件类型
export type WSEventType = 'connecting' | 'open' | 'close' | 'error' | 'message' | 'reconnect'

// WebSocket事件监听器
export type WSEventListener = (event: any) => void

// WebSocket配置
export interface WSConfig {
  url: string
  protocols?: string[]
  reconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  heartbeatMessage?: string
  useSocketIO?: boolean // 是否使用Socket.IO
  socketOptions?: any // Socket.IO选项
}

// 连接状态
export type WSConnectionState = 'connecting' | 'connected' | 'disconnected' | 'reconnecting' | 'error'

/**
 * WebSocket连接管理器 (支持原生WebSocket和Socket.IO)
 */
export class WebSocketManager {
  private ws: WebSocket | null = null
  private socket: Socket | null = null
  private config: WSConfig
  private listeners: Map<WSEventType, Set<WSEventListener>> = new Map()
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private reconnectAttempts = 0
  private state: WSConnectionState = 'disconnected'
  private messageQueue: WSMessage[] = []
  private subscriptions: Set<string> = new Set()
  private lastActivity = Date.now()

  constructor(config: WSConfig) {
    this.config = {
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      heartbeatMessage: JSON.stringify({ type: 'ping' }),
      useSocketIO: false,
      socketOptions: {
        transports: ['websocket'],
        upgrade: true,
        timeout: 10000
      },
      ...config
    }

    // 初始化事件监听器映射
    const eventTypes: WSEventType[] = ['connecting', 'open', 'close', 'error', 'message', 'reconnect']
    eventTypes.forEach(type => {
      this.listeners.set(type, new Set())
    })
  }

  /**
   * 连接WebSocket
   */
  connect(): void {
    if (this.isConnected()) {
      return
    }

    this.state = 'connecting'
    this.emit('connecting', { state: this.state })

    try {
      if (this.config.useSocketIO) {
        this.connectSocketIO()
      } else {
        this.connectWebSocket()
      }
    } catch (error) {
      this.state = 'error'
      this.emit('error', error)
      this.handleReconnect()
    }
  }

  /**
   * 连接原生WebSocket
   */
  private connectWebSocket(): void {
    this.ws = new WebSocket(this.config.url, this.config.protocols)
    this.setupWebSocketHandlers()
  }

  /**
   * 连接Socket.IO
   */
  private connectSocketIO(): void {
    this.socket = io(this.config.url, {
      autoConnect: false,
      ...this.config.socketOptions
    })
    this.setupSocketIOHandlers()
    this.socket.connect()
  }

  /**
   * 检查是否已连接
   */
  private isConnected(): boolean {
    if (this.config.useSocketIO) {
      return this.socket?.connected || false
    } else {
      return this.ws?.readyState === WebSocket.OPEN || false
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.config.reconnect = false
    this.clearTimers()
    
    if (this.config.useSocketIO && this.socket) {
      this.socket.disconnect()
      this.socket = null
    } else if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.state = 'disconnected'
    this.subscriptions.clear()
    this.emit('close', { code: 1000, reason: 'Manual disconnect' })
  }

  /**
   * 发送消息
   */
  send(eventName: string, data?: any): void {
    this.lastActivity = Date.now()
    
    if (this.isConnected()) {
      if (this.config.useSocketIO && this.socket) {
        this.socket.emit(eventName, data)
      } else if (this.ws) {
        const message = typeof data === 'string' ? data : JSON.stringify({
          type: eventName,
          data,
          timestamp: Date.now()
        })
        this.ws.send(message)
      }
    } else {
      // 连接未建立时，将消息加入队列
      this.messageQueue.push({
        type: eventName,
        data,
        timestamp: Date.now()
      })
      
      // 尝试重新连接
      if (this.state === 'disconnected') {
        this.connect()
      }
    }
  }

  /**
   * 订阅消息
   */
  subscribe(channel: string, params?: any): void {
    const subscriptionKey = `${channel}:${JSON.stringify(params || {})}`
    
    if (!this.subscriptions.has(subscriptionKey)) {
      this.subscriptions.add(subscriptionKey)
      this.send('subscribe', { channel, params })
      
      if (this.config.useSocketIO && this.socket) {
        // Socket.IO 特定订阅
        this.socket.on(channel, (data) => {
          this.emit('message', {
            type: channel,
            data,
            timestamp: Date.now()
          })
        })
      }
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(channel: string, params?: any): void {
    const subscriptionKey = `${channel}:${JSON.stringify(params || {})}`
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.subscriptions.delete(subscriptionKey)
      this.send('unsubscribe', { channel, params })
      
      if (this.config.useSocketIO && this.socket) {
        this.socket.off(channel)
      }
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: WSEventType, listener: WSEventListener): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.add(listener)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event: WSEventType, listener: WSEventListener): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  /**
   * 获取连接状态
   */
  getState(): WSConnectionState {
    return this.state
  }

  /**
   * 获取WebSocket实例
   */
  getWebSocket(): WebSocket | null {
    return this.ws
  }

  /**
   * 设置原生WebSocket事件处理器
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return

    this.ws.onopen = (event) => {
      this.state = 'connected'
      this.reconnectAttempts = 0
      this.emit('open', event)
      
      // 发送队列中的消息
      this.flushMessageQueue()
      
      // 启动心跳
      this.startHeartbeat()
      
      // 重新订阅
      this.resubscribeAll()
    }

    this.ws.onclose = (event) => {
      this.state = 'disconnected'
      this.clearTimers()
      this.emit('close', event)
      
      // 自动重连
      if (this.config.reconnect && event.code !== 1000) {
        this.handleReconnect()
      }
    }

    this.ws.onerror = (event) => {
      this.state = 'error'
      this.emit('error', event)
    }

    this.ws.onmessage = (event) => {
      this.lastActivity = Date.now()
      try {
        const data = JSON.parse(event.data)
        this.emit('message', data)
      } catch (error) {
        // 如果不是JSON格式，直接传递原始数据
        this.emit('message', { type: 'raw', data: event.data })
      }
    }
  }

  /**
   * 设置Socket.IO事件处理器
   */
  private setupSocketIOHandlers(): void {
    if (!this.socket) return

    this.socket.on('connect', () => {
      this.state = 'connected'
      this.reconnectAttempts = 0
      this.emit('open', { socket: this.socket })
      
      // 发送队列中的消息
      this.flushMessageQueue()
      
      // 启动心跳
      this.startHeartbeat()
      
      // 重新订阅
      this.resubscribeAll()
    })

    this.socket.on('disconnect', (reason) => {
      this.state = 'disconnected'
      this.clearTimers()
      this.emit('close', { reason })
      
      // 自动重连 (除非是主动断开)
      if (this.config.reconnect && reason !== 'io client disconnect') {
        this.handleReconnect()
      }
    })

    this.socket.on('connect_error', (error) => {
      this.state = 'error'
      this.emit('error', error)
      
      // 处理重连
      if (this.config.reconnect) {
        this.handleReconnect()
      }
    })

    this.socket.on('reconnect_attempt', (attempt) => {
      this.state = 'reconnecting'
      this.emit('reconnect', { attempt, maxAttempts: this.config.maxReconnectAttempts })
    })

    // 监听所有消息事件 (除内置事件)
    this.socket.onAny((eventName, ...args) => {
      if (['connect', 'disconnect', 'connect_error', 'reconnect_attempt'].includes(eventName)) {
        return
      }
      
      this.lastActivity = Date.now()
      this.emit('message', {
        type: eventName,
        data: args.length === 1 ? args[0] : args,
        timestamp: Date.now()
      })
    })
  }

  /**
   * 重新订阅所有频道
   */
  private resubscribeAll(): void {
    const subscriptions = Array.from(this.subscriptions)
    this.subscriptions.clear()
    
    subscriptions.forEach(subscription => {
      const [channel, paramsStr] = subscription.split(':')
      const params = paramsStr ? JSON.parse(paramsStr) : undefined
      this.subscribe(channel, params)
    })
  }

  /**
   * 触发事件
   */
  private emit(event: WSEventType, data: any): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`WebSocket事件监听器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (!this.config.reconnect || 
        this.reconnectAttempts >= (this.config.maxReconnectAttempts || 5)) {
      return
    }

    this.state = 'reconnecting'
    this.reconnectAttempts++
    
    this.emit('reconnect', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.maxReconnectAttempts
    })

    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.config.reconnectInterval)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (!this.config.heartbeatInterval || !this.config.heartbeatMessage) {
      return
    }

    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(this.config.heartbeatMessage!)
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 清除定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message.type, message.data)
      }
    }
  }

  /**
   * 获取连接统计
   */
  getStats() {
    return {
      state: this.state,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      subscriptions: this.subscriptions.size,
      lastActivity: this.lastActivity,
      url: this.config.url,
      useSocketIO: this.config.useSocketIO,
      connected: this.isConnected()
    }
  }

  /**
   * 检查连接活跃状态
   */
  isActive(): boolean {
    const inactiveThreshold = 60000 // 60秒
    return Date.now() - this.lastActivity < inactiveThreshold
  }

  /**
   * 获取订阅列表
   */
  getSubscriptions(): string[] {
    return Array.from(this.subscriptions)
  }
}

/**
 * 市场数据WebSocket连接
 */
export class MarketWebSocket extends WebSocketManager {
  constructor(useSocketIO = false) {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.MARKET
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      useSocketIO,
      socketOptions: {
        transports: ['websocket'],
        upgrade: true,
        timeout: 10000,
        forceNew: true
      }
    })
  }

  /**
   * 订阅股票行情
   */
  subscribeQuote(symbols: string[]): void {
    this.subscribe('quote', { symbols })
  }

  /**
   * 取消订阅股票行情
   */
  unsubscribeQuote(symbols: string[]): void {
    this.unsubscribe('quote', { symbols })
  }

  /**
   * 订阅K线数据
   */
  subscribeKLine(symbol: string, period: string): void {
    this.subscribe('kline', { symbol, period })
  }

  /**
   * 订阅市场深度
   */
  subscribeDepth(symbol: string): void {
    this.subscribe('depth', { symbol })
  }

  /**
   * 批量订阅
   */
  batchSubscribe(subscriptions: {
    quotes?: string[]
    klines?: { symbol: string, period: string }[]
    depths?: string[]
  }): void {
    if (subscriptions.quotes) {
      this.subscribeQuote(subscriptions.quotes)
    }
    
    if (subscriptions.klines) {
      subscriptions.klines.forEach(({ symbol, period }) => {
        this.subscribeKLine(symbol, period)
      })
    }
    
    if (subscriptions.depths) {
      subscriptions.depths.forEach(symbol => {
        this.subscribeDepth(symbol)
      })
    }
  }
}

/**
 * 交易WebSocket连接
 */
export class TradingWebSocket extends WebSocketManager {
  constructor(useSocketIO = false) {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.TRADING
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      useSocketIO,
      socketOptions: {
        transports: ['websocket'],
        upgrade: true,
        timeout: 10000
      }
    })
  }

  /**
   * 订阅账户信息
   */
  subscribeAccount(): void {
    this.subscribe('account')
  }

  /**
   * 订阅订单更新
   */
  subscribeOrders(): void {
    this.subscribe('orders')
  }

  /**
   * 订阅持仓更新
   */
  subscribePositions(): void {
    this.subscribe('positions')
  }

  /**
   * 发送交易订单
   */
  placeOrder(orderData: any): void {
    this.send('place_order', orderData)
  }

  /**
   * 取消订单
   */
  cancelOrder(orderId: string): void {
    this.send('cancel_order', { orderId })
  }
}

/**
 * 策略WebSocket连接
 */
export class StrategyWebSocket extends WebSocketManager {
  constructor(useSocketIO = false) {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.STRATEGY
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      useSocketIO,
      socketOptions: {
        transports: ['websocket'],
        upgrade: true,
        timeout: 10000
      }
    })
  }

  /**
   * 订阅策略状态
   */
  subscribeStrategyStatus(strategyId: string): void {
    this.subscribe('strategy_status', { strategyId })
  }

  /**
   * 订阅策略信号
   */
  subscribeStrategySignals(strategyId: string): void {
    this.subscribe('strategy_signals', { strategyId })
  }

  /**
   * 订阅回测进度
   */
  subscribeBacktestProgress(backtestId: string): void {
    this.subscribe('backtest_progress', { backtestId })
  }

  /**
   * 启动策略
   */
  startStrategy(strategyId: string, params?: any): void {
    this.send('start_strategy', { strategyId, params })
  }

  /**
   * 停止策略
   */
  stopStrategy(strategyId: string): void {
    this.send('stop_strategy', { strategyId })
  }
}

// 创建全局实例 (默认使用Socket.IO)
export const marketWS = new MarketWebSocket(true)
export const tradingWS = new TradingWebSocket(true)
export const strategyWS = new StrategyWebSocket(true)

// 创建原生WebSocket实例
export const nativeMarketWS = new MarketWebSocket(false)
export const nativeTradingWS = new TradingWebSocket(false)
export const nativeStrategyWS = new StrategyWebSocket(false)

// 导出默认实例
export { marketWS as default }