import{c as h,a as T,o as Br,t as C,i as _,b as w,R as W,d as Tr,e as R,f as l,g as Ir,s as Wr,r as Or}from"./vendor-solid-D13wdmyY.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))d(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const c of i.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&d(c)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function d(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();const[O,ye]=h({user:null,isAuthenticated:!1,loading:!1,error:null}),[zr,ke]=h({account:null,orders:[],positions:[],trades:[],isConnected:!1,loading:!1,error:null}),[Er,_e]=h({realTime:{},charts:{},watchlist:[],loading:!1,error:null}),[Ar,Ce]=h({notifications:[],unreadCount:0}),[Pr,we]=h({strategies:[],activeStrategy:null,loading:!1,error:null});h({results:[],currentResult:null,isRunning:!1,loading:!1,error:null});h({mode:"light",primaryColor:"#409eff",fontSize:"medium"});h({trading:"disconnected",marketData:"disconnected",lastConnectedAt:null});h({isOpen:!1,type:"",data:null});T(()=>O.isAuthenticated&&O.user!==null);T(()=>zr.account?.totalAssets||0);T(()=>Ar.notifications.filter(r=>!r.read));T(()=>Pr.strategies.filter(r=>r.status==="active"));function B(r){return typeof r=="object"&&r!=null&&!Array.isArray(r)}function Mr(r){return Object.fromEntries(Object.entries(r??{}).filter(([e,n])=>n!==void 0))}var jr=r=>r==="base";function Lr(r){return r.slice().filter(e=>!jr(e))}function gr(r){return String.fromCharCode(r+(r>25?39:97))}function Dr(r){let e="",n;for(n=Math.abs(r);n>52;n=n/52|0)e=gr(n%52)+e;return gr(n%52)+e}function Xr(r,e){let n=e.length;for(;n;)r=r*33^e.charCodeAt(--n);return r}function Fr(r){return Dr(Xr(5381,r)>>>0)}var mr=/\s*!(important)?/i;function Nr(r){return typeof r=="string"?mr.test(r):!1}function $r(r){return typeof r=="string"?r.replace(mr,"").trim():r}function fr(r){return typeof r=="string"?r.replaceAll(" ","_"):r}var z=r=>{const e=new Map;return(...d)=>{const o=JSON.stringify(d);if(e.has(o))return e.get(o);const i=r(...d);return e.set(o,i),i}};function hr(...r){return r.filter(Boolean).reduce((n,d)=>(Object.keys(d).forEach(o=>{const i=n[o],c=d[o];B(i)&&B(c)?n[o]=hr(i,c):n[o]=c}),n),{})}var Yr=r=>r!=null;function xr(r,e,n={}){const{stop:d,getKey:o}=n;function i(c,s=[]){if(B(c)||Array.isArray(c)){const b={};for(const[g,u]of Object.entries(c)){const p=o?.(g,u)??g,m=[...s,p];if(d?.(c,m))return e(c,s);const f=i(u,m);Yr(f)&&(b[p]=f)}return b}return e(c,s)}return i(r)}function Hr(r,e){return r.reduce((n,d,o)=>{const i=e[o];return d!=null&&(n[i]=d),n},{})}function vr(r,e,n=!0){const{utility:d,conditions:o}=e,{hasShorthand:i,resolveShorthand:c}=d;return xr(r,s=>Array.isArray(s)?Hr(s,o.breakpoints.keys):s,{stop:s=>Array.isArray(s),getKey:n?s=>i?c(s):s:void 0})}var qr={shift:r=>r,finalize:r=>r,breakpoints:{keys:[]}},Gr=r=>typeof r=="string"?r.replaceAll(/[\n\s]+/g," "):r;function Vr(r){const{utility:e,hash:n,conditions:d=qr}=r,o=c=>[e.prefix,c].filter(Boolean).join("-"),i=(c,s)=>{let b;if(n){const g=[...d.finalize(c),s];b=o(e.toHash(g,Fr))}else b=[...d.finalize(c),o(s)].join(":");return b};return z(({base:c,...s}={})=>{const b=Object.assign(s,c),g=vr(b,r),u=new Set;return xr(g,(p,m)=>{const f=Nr(p);if(p==null)return;const[S,...x]=d.shift(m),y=Lr(x),k=e.transform(S,$r(Gr(p)));let v=i(y,k.className);f&&(v=`${v}!`),u.add(v)}),Array.from(u).join(" ")})}function Kr(...r){return r.flat().filter(e=>B(e)&&Object.keys(Mr(e)).length>0)}function Ur(r){function e(o){const i=Kr(...o);return i.length===1?i:i.map(c=>vr(c,r))}function n(...o){return hr(...e(o))}function d(...o){return Object.assign({},...e(o))}return{mergeCss:z(n),assignCss:d}}var Zr=/([A-Z])/g,Jr=/^ms-/,Qr=z(r=>r.startsWith("--")?r:r.replace(Zr,"-$1").replace(Jr,"-ms-").toLowerCase()),re="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${re.split(",").join("|")}`;const ee="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Sr=new Set(ee.split(","));function br(r){return Sr.has(r)||/^@|&|&$/.test(r)}const oe=/^_/,te=/&|@/;function ie(r){return r.map(e=>Sr.has(e)?e.replace(oe,""):te.test(e)?`[${fr(e.trim())}]`:e)}function ne(r){return r.sort((e,n)=>{const d=br(e),o=br(n);return d&&!o?1:!d&&o?-1:0})}const ae="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",yr=new Map,kr=new Map;ae.split(",").forEach(r=>{const[e,n]=r.split(":"),[d,...o]=n.split("/");yr.set(e,d),o.length&&o.forEach(i=>{kr.set(i==="1"?d:i,e)})});const ur=r=>kr.get(r)||r,_r={conditions:{shift:ne,finalize:ie,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(r,e)=>{const n=ur(r);return{className:`${yr.get(n)||Qr(n)}_${fr(e)}`}},hasShorthand:!0,toHash:(r,e)=>e(r.join(":")),resolveShorthand:ur}},le=Vr(_r),a=(...r)=>le(Cr(...r));a.raw=(...r)=>Cr(...r);const{mergeCss:Cr}=Ur(_r);var de=C("<div><h2>📊 量化交易仪表板</h2><div><div><h3>账户总资产</h3><div>¥1,234,567.89</div></div><div><h3>今日盈亏</h3><div>-¥2,345.67</div></div><div><h3>持仓数量</h3><div>8</div></div></div><div><h3>📈 实时行情</h3><div><div><div>AAPL</div><div>$173.25</div><div>+1.25 (+0.72%)</div></div><div><div>TSLA</div><div>$245.67</div><div>-3.45 (-1.38%)</div></div><div><div>BTC-USD</div><div>$43,250.00</div><div>+1,250 (+2.98%)"),se=C("<div><h2>💼 交易中心</h2><div><div><h3>📋 委托订单</h3><p>暂无委托订单</p></div><div><h3>📊 持仓明细</h3><p>暂无持仓"),ce=C("<div><h2>🤖 策略管理</h2><div><h3>📈 策略列表</h3><p>暂无策略"),ge=C("<nav><div><div>🚀 量化交易平台</div><div>"),be=C("<a><span>"),ue=C("<main>"),pe=C("<div>");const me=()=>(()=>{var r=de(),e=r.firstChild,n=e.nextSibling,d=n.firstChild,o=d.firstChild,i=o.nextSibling,c=d.nextSibling,s=c.firstChild,b=s.nextSibling,g=c.nextSibling,u=g.firstChild,p=u.nextSibling,m=n.nextSibling,f=m.firstChild,S=f.nextSibling,x=S.firstChild,y=x.firstChild,k=y.nextSibling,v=k.nextSibling,I=x.nextSibling,E=I.firstChild,A=E.nextSibling,wr=A.nextSibling,P=I.nextSibling,M=P.firstChild,j=M.nextSibling,Rr=j.nextSibling;return R(t=>{var L=a({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),D=a({fontSize:"24px",fontWeight:"bold",color:"blue.600",marginBottom:"24px"}),X=a({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"16px",marginBottom:"24px"}),F=a({padding:"16px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),N=a({margin:"0 0 8px 0",color:"gray.700"}),$=a({fontSize:"24px",fontWeight:"bold",color:"green.600"}),Y=a({padding:"16px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),H=a({margin:"0 0 8px 0",color:"gray.700"}),q=a({fontSize:"24px",fontWeight:"bold",color:"red.600"}),G=a({padding:"16px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),V=a({margin:"0 0 8px 0",color:"gray.700"}),K=a({fontSize:"24px",fontWeight:"bold",color:"blue.600"}),U=a({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),Z=a({margin:"0 0 16px 0",color:"gray.700"}),J=a({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),Q=a({padding:"12px",background:"blue.50",borderRadius:"6px",border:"1px solid",borderColor:"blue.200"}),rr=a({fontWeight:"bold",color:"blue.800"}),er=a({fontSize:"18px",color:"green.600"}),or=a({fontSize:"12px",color:"green.600"}),tr=a({padding:"12px",background:"blue.50",borderRadius:"6px",border:"1px solid",borderColor:"blue.200"}),ir=a({fontWeight:"bold",color:"blue.800"}),nr=a({fontSize:"18px",color:"red.600"}),ar=a({fontSize:"12px",color:"red.600"}),lr=a({padding:"12px",background:"blue.50",borderRadius:"6px",border:"1px solid",borderColor:"blue.200"}),dr=a({fontWeight:"bold",color:"blue.800"}),sr=a({fontSize:"18px",color:"green.600"}),cr=a({fontSize:"12px",color:"green.600"});return L!==t.e&&l(r,t.e=L),D!==t.t&&l(e,t.t=D),X!==t.a&&l(n,t.a=X),F!==t.o&&l(d,t.o=F),N!==t.i&&l(o,t.i=N),$!==t.n&&l(i,t.n=$),Y!==t.s&&l(c,t.s=Y),H!==t.h&&l(s,t.h=H),q!==t.r&&l(b,t.r=q),G!==t.d&&l(g,t.d=G),V!==t.l&&l(u,t.l=V),K!==t.u&&l(p,t.u=K),U!==t.c&&l(m,t.c=U),Z!==t.w&&l(f,t.w=Z),J!==t.m&&l(S,t.m=J),Q!==t.f&&l(x,t.f=Q),rr!==t.y&&l(y,t.y=rr),er!==t.g&&l(k,t.g=er),or!==t.p&&l(v,t.p=or),tr!==t.b&&l(I,t.b=tr),ir!==t.T&&l(E,t.T=ir),nr!==t.A&&l(A,t.A=nr),ar!==t.O&&l(wr,t.O=ar),lr!==t.I&&l(P,t.I=lr),dr!==t.S&&l(M,t.S=dr),sr!==t.W&&l(j,t.W=sr),cr!==t.C&&l(Rr,t.C=cr),t},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0}),r})(),fe=()=>(()=>{var r=se(),e=r.firstChild,n=e.nextSibling,d=n.firstChild,o=d.firstChild,i=o.nextSibling,c=d.nextSibling,s=c.firstChild,b=s.nextSibling;return R(g=>{var u=a({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),p=a({fontSize:"24px",fontWeight:"bold",color:"blue.600",marginBottom:"24px"}),m=a({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"}),f=a({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),S=a({margin:"0 0 16px 0",color:"gray.700"}),x=a({color:"gray.500",margin:0}),y=a({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),k=a({margin:"0 0 16px 0",color:"gray.700"}),v=a({color:"gray.500",margin:0});return u!==g.e&&l(r,g.e=u),p!==g.t&&l(e,g.t=p),m!==g.a&&l(n,g.a=m),f!==g.o&&l(d,g.o=f),S!==g.i&&l(o,g.i=S),x!==g.n&&l(i,g.n=x),y!==g.s&&l(c,g.s=y),k!==g.h&&l(s,g.h=k),v!==g.r&&l(b,g.r=v),g},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0}),r})(),he=()=>(()=>{var r=ce(),e=r.firstChild,n=e.nextSibling,d=n.firstChild,o=d.nextSibling;return R(i=>{var c=a({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),s=a({fontSize:"24px",fontWeight:"bold",color:"blue.600",marginBottom:"24px"}),b=a({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}),g=a({margin:"0 0 16px 0",color:"gray.700"}),u=a({color:"gray.500",margin:0});return c!==i.e&&l(r,i.e=c),s!==i.t&&l(e,i.t=s),b!==i.a&&l(n,i.a=b),g!==i.o&&l(d,i.o=g),u!==i.i&&l(o,i.i=u),i},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),r})(),xe=()=>{const[r,e]=Ir(window.location.pathname),n=[{path:"/",label:"仪表板",icon:"📊"},{path:"/trading",label:"交易中心",icon:"💼"},{path:"/strategy",label:"策略管理",icon:"🤖"}];return(()=>{var d=ge(),o=d.firstChild,i=o.firstChild,c=i.nextSibling;return _(c,()=>n.map(s=>(()=>{var b=be(),g=b.firstChild;return _(g,()=>s.icon),_(b,()=>s.label,null),R(u=>{var p=s.path,m=a({color:r()===s.path?"blue.400":"gray.300",textDecoration:"none",padding:"0.5rem 1rem",borderRadius:"0.375rem",transition:"color 0.2s",display:"flex",alignItems:"center",gap:"0.5rem",_hover:{color:r()===s.path?"blue.400":"gray.100"}});return p!==u.e&&Wr(b,"href",u.e=p),m!==u.t&&l(b,u.t=m),u},{e:void 0,t:void 0}),b})())),R(s=>{var b=a({background:"gray.800",padding:"1rem 0",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"}),g=a({maxWidth:"1200px",margin:"0 auto",padding:"0 20px",display:"flex",justifyContent:"space-between",alignItems:"center"}),u=a({color:"white",fontSize:"20px",fontWeight:"bold"}),p=a({display:"flex",gap:"2rem"});return b!==s.e&&l(d,s.e=b),g!==s.t&&l(o,s.t=g),u!==s.a&&l(i,s.a=u),p!==s.o&&l(c,s.o=p),s},{e:void 0,t:void 0,a:void 0,o:void 0}),d})()},ve=()=>(Br(()=>{console.log("🚀 量化交易平台已启动"),console.log("用户状态:",O),console.log("市场数据:",Er)}),(()=>{var r=pe();return _(r,w(Tr,{get children(){return[w(xe,{}),(()=>{var e=ue();return _(e,w(W,{path:"/",component:me}),null),_(e,w(W,{path:"/trading",component:fe}),null),_(e,w(W,{path:"/strategy",component:he}),null),e})()]}})),R(()=>l(r,a({minHeight:"100vh",background:"gray.50",fontFamily:"Inter, system-ui, sans-serif"}))),r})()),pr=document.getElementById("root");pr&&Or(()=>w(ve,{}),pr);
