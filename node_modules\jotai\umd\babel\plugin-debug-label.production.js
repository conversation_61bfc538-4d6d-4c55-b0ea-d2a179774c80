!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@babel/template")):"function"==typeof define&&define.amd?define(["@babel/template"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).jotaiBabelPluginDebugLabel=t(e._templateBuilder)}(this,function(e){"use strict";function t(e,t,a){void 0===a&&(a=[]);var o=[].concat(i,a);if(e.isIdentifier(t)&&o.includes(t.name))return!0;if(e.isMemberExpression(t)){var n=t.property;if(e.isIdentifier(n)&&o.includes(n.name))return!0}return!1}var i=["atom","atomFamily","atomWithDefault","atomWithObservable","atomWithReducer","atomWithReset","atomWithStorage","freezeAtom","loadable","selectAtom","splitAtom","unwrap","atomWithMachine","atomWithImmer","atomWithProxy","atomWithQuery","atomWithMutation","atomWithSubscription","atomWithStore","atomWithHash","atomWithLocation","focusAtom","atomWithValidate","validateAtoms","atomWithCache","atomWithRecoilValue"],a=e.default||e;return function(e,i){var o=e.types;return{visitor:{ExportDefaultDeclaration:function(e,n){var r=e.node;if(o.isCallExpression(r.declaration)&&t(o,r.declaration.callee,null==i?void 0:i.customAtomNames)){var l=(n.filename||"unknown").replace(/\.\w+$/,""),m=l.split("/").pop();"index"===m&&(m=l.slice(0,-6).split("/").pop()||"unknown");var s=a("\n          const %%atomIdentifier%% = %%atom%%;\n          export default %%atomIdentifier%%\n          ")({atomIdentifier:o.identifier(m),atom:r.declaration});e.replaceWithMultiple(s)}},VariableDeclarator:function(e){o.isIdentifier(e.node.id)&&o.isCallExpression(e.node.init)&&t(o,e.node.init.callee,null==i?void 0:i.customAtomNames)&&e.parentPath.insertAfter(o.expressionStatement(o.assignmentExpression("=",o.memberExpression(o.identifier(e.node.id.name),o.identifier("debugLabel")),o.stringLiteral(e.node.id.name))))}}}}});
