!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react"),require("jotai/react"),require("jotai/vanilla/utils"),require("jotai/vanilla")):"function"==typeof define&&define.amd?define(["exports","react","jotai/react","jotai/vanilla/utils","jotai/vanilla"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jotaiReactUtils={},t.<PERSON>act,t.jotaiReact,t.jotaiVanillaUtils,t.jotaiVanilla)}(this,function(t,e,r,n,a){"use strict";function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function i(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return o(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(t,e):void 0}}(t))||e){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u=new WeakMap;var l=function(t){var e=u.get(t);return e||(e=new WeakSet,u.set(t,e)),e};t.useAtomCallback=function(t,n){var o=e.useMemo(function(){return a.atom(null,function(e,r){for(var n=arguments.length,a=new Array(n>2?n-2:0),o=2;o<n;o++)a[o-2]=arguments[o];return t.apply(void 0,[e,r].concat(a))})},[t]);return r.useSetAtom(o,n)},t.useHydrateAtoms=function(t,e){for(var n,a=r.useStore(e),o=l(a),u=i(t);!(n=u()).done;){var c=n.value,s=c[0],f=c.slice(1);(!o.has(s)||null!=e&&e.dangerouslyForceHydrate)&&(o.add(s),a.set.apply(a,[s].concat(f)))}},t.useReducerAtom=function(t,n,a){var o=r.useAtom(t,a),i=o[0],u=o[1];return[i,e.useCallback(function(t){u(function(e){return n(e,t)})},[u,n])]},t.useResetAtom=function(t,a){var o=r.useSetAtom(t,a);return e.useCallback(function(){return o(n.RESET)},[o])}});
