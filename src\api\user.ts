/**
 * 用户管理API
 */
import { httpClient } from '../utils/http'
import { API_PATHS, ENV_CONFIG } from '../utils/constants'
import type { ApiResponse } from '../utils/http'
// 用户数据类型
export interface UserData {
  id: string
  username: string
  email: string
  nickname?: string
  avatar?: string
  phone?: string
  role: 'user' | 'admin' | 'vip'
  status: 'active' | 'inactive' | 'banned'
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  preferences?: UserPreferences
  profile?: UserProfile
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  timezone: string
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  trading: {
    confirmOrders: boolean
    showRiskWarnings: boolean
    defaultOrderType: 'market' | 'limit'
  }
}

// 用户资料
export interface UserProfile {
  realName?: string
  idCard?: string
  birthday?: string
  gender?: 'male' | 'female' | 'other'
  address?: string
  occupation?: string
  investmentExperience?: 'beginner' | 'intermediate' | 'advanced' | 'professional'
  riskTolerance?: 'conservative' | 'moderate' | 'aggressive'
}

// 请求类型
export interface LoginRequest {
  username: string
  password: string
  captchaToken?: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  captchaToken?: string
  inviteCode?: string
}

export interface PasswordResetRequest {
  email: string
  captchaToken?: string
}

export interface PasswordChangeRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ProfileUpdateRequest {
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  profile?: Partial<UserProfile>
}

export interface PreferencesUpdateRequest {
  theme?: 'light' | 'dark' | 'auto'
  language?: 'zh-CN' | 'en-US'
  timezone?: string
  notifications?: Partial<UserPreferences['notifications']>
  trading?: Partial<UserPreferences['trading']>
}

// 响应类型
export interface LoginResponse {
  user: UserData
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface TokenRefreshResponse {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// 模拟数据生成
const generateMockUser = (username?: string, role?: 'admin' | 'user' | 'trader'): UserData => {
  const defaultUsername = username || 'demo_user';
  const defaultRole = role || 'user';

  // 根据用户名设置不同的显示信息
  const userInfo = {
    admin: { nickname: '系统管理员', realName: '管理员', avatar: 'admin' },
    trader: { nickname: '专业交易员', realName: '交易员', avatar: 'trader' },
    demo: { nickname: '演示用户', realName: '张三', avatar: 'demo' },
    test: { nickname: '测试用户', realName: '李四', avatar: 'test' }
  };

  const info = userInfo[defaultUsername as keyof typeof userInfo] || userInfo.demo;

  return {
    id: `user-${defaultUsername}-${Date.now()}`,
    username: defaultUsername,
    email: `${defaultUsername}@example.com`,
    nickname: info.nickname,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${info.avatar}`,
    phone: '138****8888',
    role: defaultRole === 'trader' ? 'vip' : (defaultRole as 'user' | 'admin' | 'vip'),
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
    preferences: {
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      notifications: {
        email: true,
        push: true,
        sms: false
      },
      trading: {
        confirmOrders: true,
        showRiskWarnings: true,
        defaultOrderType: 'limit'
      }
    },
    profile: {
      realName: info.realName,
      gender: 'male',
      investmentExperience: defaultRole === 'admin' ? 'professional': 'intermediate',
      riskTolerance: defaultRole === 'trader' ? 'aggressive': 'moderate'
    }
  };
};

/**
 * 用户管理API类
 */
export class UserAPI {
  private useMock = ENV_CONFIG.enableMock

  /**
   * 用户登录
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    if (this.useMock) {
      // 模拟登录验证 - 支持多个测试账户
      const validCredentials = [
        { username: 'demo', password: 'demo123', role: 'user' },
        { username: 'admin', password: '123456', role: 'admin' },
        { username: 'trader', password: 'trader123', role: 'trader' },
        { username: 'test', password: 'test123', role: 'user' }
      ];

      const validUser = validCredentials.find(
        cred => cred.username === data.username && cred.password === data.password
      );

      if (validUser) {
        const user = generateMockUser(validUser.username, validUser.role as any)
        const response: LoginResponse = {
          user,
          accessToken: 'mock-access-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expiresIn: 3600
        }

        // 保存到本地存储
        if (typeof window !== 'undefined') {
          localStorage.setItem('access_token', response.accessToken)
          localStorage.setItem('refresh_token', response.refreshToken)
          localStorage.setItem('user_info', JSON.stringify(user))
        }

        return response
      } else {
        throw new Error('用户名或密码错误')
      }
    }

    try {
      const response = await httpClient.post<LoginResponse>(API_PATHS.AUTH.LOGIN, data)
      
      // 保存token到本地存储
      if (response.data && typeof window !== 'undefined') {
        localStorage.setItem('access_token', response.data.accessToken)
        localStorage.setItem('refresh_token', response.data.refreshToken)
        localStorage.setItem('user_info', JSON.stringify(response.data.user))
      }
      
      return response.data!
    } catch (error) {
      console.warn('登录API调用失败:', error)
      throw error
    }
  }

  /**
   * 用户注册
   */
  async register(data: RegisterRequest): Promise<UserData> {
    if (this.useMock) {
      if (data.password !== data.confirmPassword) {
        throw new Error('两次输入的密码不一致')
      }
      
      return {
        ...generateMockUser(),
        username: data.username,
        email: data.email,
        createdAt: new Date().toISOString()
      }
    }

    try {
      const response = await httpClient.post<UserData>(API_PATHS.AUTH.REGISTER, data)
      return response.data!
    } catch (error) {
      console.warn('注册API调用失败:', error)
      throw error
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    if (this.useMock) {
      // 清除本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_info')
      }
      return Promise.resolve()
    }

    try {
      await httpClient.post(API_PATHS.AUTH.LOGOUT)
    } catch (error) {
      console.warn('登出API调用失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_info')
      }
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(): Promise<TokenRefreshResponse> {
    if (this.useMock) {
      return {
        accessToken: 'mock-new-access-token-' + Date.now(),
        refreshToken: 'mock-new-refresh-token-' + Date.now(),
        expiresIn: 3600
      }
    }

    try {
      const refreshToken = typeof window !== 'undefined' ? localStorage.getItem('refresh_token') : null
      const response = await httpClient.post<TokenRefreshResponse>(API_PATHS.AUTH.REFRESH, {
        refreshToken
      })
      
      // 更新本地存储
      if (response.data && typeof window !== 'undefined') {
        localStorage.setItem('access_token', response.data.accessToken)
        localStorage.setItem('refresh_token', response.data.refreshToken)
      }
      
      return response.data!
    } catch (error) {
      console.warn('刷新token API调用失败:', error)
      throw error
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<UserData> {
    if (this.useMock) {
      // 从本地存储获取用户信息
      if (typeof window !== 'undefined') {
        const userInfo = localStorage.getItem('user_info')
        if (userInfo) {
          return JSON.parse(userInfo)
        }
      }
      return generateMockUser()
    }

    try {
      const response = await httpClient.get<UserData>(API_PATHS.AUTH.PROFILE)
      return response.data!
    } catch (error) {
      console.warn('获取用户信息API调用失败，使用模拟数据:', error)
      return generateMockUser()
    }
  }

  /**
   * 更新用户资料
   */
  async updateProfile(data: ProfileUpdateRequest): Promise<UserData> {
    if (this.useMock) {
      const currentUser = await this.getUserInfo()
      const updatedUser = {
        ...currentUser,
        ...data,
        updatedAt: new Date().toISOString()
      }
      
      // 更新本地存储
      if (typeof window !== 'undefined') {
        localStorage.setItem('user_info', JSON.stringify(updatedUser))
      }
      
      return updatedUser
    }

    try {
      const response = await httpClient.put<UserData>(API_PATHS.AUTH.PROFILE, data)
      
      // 更新本地存储
      if (response.data && typeof window !== 'undefined') {
        localStorage.setItem('user_info', JSON.stringify(response.data))
      }
      
      return response.data!
    } catch (error) {
      console.warn('更新用户资料API调用失败:', error)
      throw error
    }
  }

  /**
   * 修改密码
   */
  async changePassword(data: PasswordChangeRequest): Promise<void> {
    if (this.useMock) {
      if (data.newPassword !== data.confirmPassword) {
        throw new Error('两次输入的新密码不一致')
      }
      return Promise.resolve()
    }

    try {
      await httpClient.post(API_PATHS.AUTH.CHANGE_PASSWORD, data)
    } catch (error) {
      console.warn('修改密码API调用失败: ', error)
      throw error
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(data: PasswordResetRequest): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.post(API_PATHS.AUTH.RESET_PASSWORD, data)
    } catch (error) {
      console.warn('重置密码API调用失败:', error)
      throw error
    }
  }

  /**
   * 上传头像
   */
  async uploadAvatar(file: File): Promise<{ url: string }> {
    if (this.useMock) {
      return {
        url: 'https://via.placeholder.com/100x100'
      }
    }

    try {
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await httpClient.post<{ url: string }>(API_PATHS.USER.AVATAR, formData, {
        'Content-Type': 'multipart/form-data'
      })
      
      return response.data!
    } catch (error) {
      console.warn('上传头像API调用失败:', error)
      throw error
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updatePreferences(data: PreferencesUpdateRequest): Promise<UserPreferences> {
    if (this.useMock) {
      const currentUser = await this.getUserInfo()
      const updatedPreferences = {
        ...currentUser.preferences,
        ...data
      }
      
      const updatedUser = {
        ...currentUser,
        preferences: updatedPreferences,
        updatedAt: new Date().toISOString()
      }
      
      // 更新本地存储
      if (typeof window !== 'undefined') {
        localStorage.setItem('user_info', JSON.stringify(updatedUser))
      }
      
      return updatedPreferences as UserPreferences
    }

    try {
      const response = await httpClient.put<UserPreferences>(API_PATHS.USER.PREFERENCES, data)
      return response.data!
    } catch (error) {
      console.warn('更新用户偏好设置API调用失败:', error)
      throw error
    }
  }
}

// 创建实例
export const userApi = new UserAPI()

// 导出默认实例
export default userApi