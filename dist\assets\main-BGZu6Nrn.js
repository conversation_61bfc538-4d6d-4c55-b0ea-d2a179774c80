import{c as oe,o as ue,t as A,i as u,a as T,b as t,d as xe,m as ge,e as Ke,f as ce,r as Ve}from"./vendor-solid-CZqAGBkN.js";(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))c(n);new MutationObserver(n=>{for(const l of n)if(l.type==="childList")for(const s of l.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&c(s)}).observe(document,{childList:!0,subtree:!0});function a(n){const l={};return n.integrity&&(l.integrity=n.integrity),n.referrerPolicy&&(l.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?l.credentials="include":n.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function c(n){if(n.ep)return;n.ep=!0;const l=a(n);fetch(n.href,l)}})();function pe(o){return typeof o=="object"&&o!=null&&!Array.isArray(o)}function Qe(o){return Object.fromEntries(Object.entries(o??{}).filter(([i,a])=>a!==void 0))}var Ze=o=>o==="base";function Je(o){return o.slice().filter(i=>!Ze(i))}function We(o){return String.fromCharCode(o+(o>25?39:97))}function et(o){let i="",a;for(a=Math.abs(o);a>52;a=a/52|0)i=We(a%52)+i;return We(a%52)+i}function tt(o,i){let a=i.length;for(;a;)o=o*33^i.charCodeAt(--a);return o}function rt(o){return et(tt(5381,o)>>>0)}var De=/\s*!(important)?/i;function ot(o){return typeof o=="string"?De.test(o):!1}function it(o){return typeof o=="string"?o.replace(De,"").trim():o}function Fe(o){return typeof o=="string"?o.replaceAll(" ","_"):o}var fe=o=>{const i=new Map;return(...c)=>{const n=JSON.stringify(c);if(i.has(n))return i.get(n);const l=o(...c);return i.set(n,l),l}};function je(...o){return o.filter(Boolean).reduce((a,c)=>(Object.keys(c).forEach(n=>{const l=a[n],s=c[n];pe(l)&&pe(s)?a[n]=je(l,s):a[n]=s}),a),{})}var nt=o=>o!=null;function Ne(o,i,a={}){const{stop:c,getKey:n}=a;function l(s,p=[]){if(pe(s)||Array.isArray(s)){const m={};for(const[E,k]of Object.entries(s)){const b=n?.(E,k)??E,v=[...p,b];if(c?.(s,v))return i(s,p);const F=l(k,v);nt(F)&&(m[b]=F)}return m}return i(s,p)}return l(o)}function at(o,i){return o.reduce((a,c,n)=>{const l=i[n];return c!=null&&(a[l]=c),a},{})}function He(o,i,a=!0){const{utility:c,conditions:n}=i,{hasShorthand:l,resolveShorthand:s}=c;return Ne(o,p=>Array.isArray(p)?at(p,n.breakpoints.keys):p,{stop:p=>Array.isArray(p),getKey:a?p=>l?s(p):p:void 0})}var dt={shift:o=>o,finalize:o=>o,breakpoints:{keys:[]}},lt=o=>typeof o=="string"?o.replaceAll(/[\n\s]+/g," "):o;function st(o){const{utility:i,hash:a,conditions:c=dt}=o,n=s=>[i.prefix,s].filter(Boolean).join("-"),l=(s,p)=>{let m;if(a){const E=[...c.finalize(s),p];m=n(i.toHash(E,rt))}else m=[...c.finalize(s),n(p)].join(":");return m};return fe(({base:s,...p}={})=>{const m=Object.assign(p,s),E=He(m,o),k=new Set;return Ne(E,(b,v)=>{const F=ot(b);if(b==null)return;const[G,...M]=c.shift(v),Y=Je(M),Q=i.transform(G,it(lt(b)));let j=l(Y,Q.className);F&&(j=`${j}!`),k.add(j)}),Array.from(k).join(" ")})}function ct(...o){return o.flat().filter(i=>pe(i)&&Object.keys(Qe(i)).length>0)}function gt(o){function i(n){const l=ct(...n);return l.length===1?l:l.map(s=>He(s,o))}function a(...n){return je(...i(n))}function c(...n){return Object.assign({},...i(n))}return{mergeCss:fe(a),assignCss:c}}var ht=/([A-Z])/g,bt=/^ms-/,pt=fe(o=>o.startsWith("--")?o:o.replace(ht,"-$1").replace(bt,"-ms-").toLowerCase()),ut="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${ut.split(",").join("|")}`;const xt="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",qe=new Set(xt.split(","));function Pe(o){return qe.has(o)||/^@|&|&$/.test(o)}const ft=/^_/,vt=/&|@/;function mt(o){return o.map(i=>qe.has(i)?i.replace(ft,""):vt.test(i)?`[${Fe(i.trim())}]`:i)}function St(o){return o.sort((i,a)=>{const c=Pe(i),n=Pe(a);return c&&!n?1:!c&&n?-1:0})}const yt="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",Ge=new Map,Ye=new Map;yt.split(",").forEach(o=>{const[i,a]=o.split(":"),[c,...n]=a.split("/");Ge.set(i,c),n.length&&n.forEach(l=>{Ye.set(l==="1"?c:l,i)})});const Oe=o=>Ye.get(o)||o,Ue={conditions:{shift:St,finalize:mt,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(o,i)=>{const a=Oe(o);return{className:`${Ge.get(a)||pt(a)}_${Fe(i)}`}},hasShorthand:!0,toHash:(o,i)=>i(o.join(":")),resolveShorthand:Oe}},Ct=st(Ue),e=(...o)=>Ct(Xe(...o));e.raw=(...o)=>Xe(...o);const{mergeCss:Xe}=gt(Ue);var kt=A("<div><div><h1>📊 量化交易仪表板</h1><p>实时监控市场数据和交易策略表现</p></div><div><div><h3>总资产</h3><p>¥1,234,567.89</p><p>+5.67% 今日</p></div><div><h3>活跃策略</h3><p>12</p><p>3个新策略</p></div><div><h3>今日收益</h3><p>+¥12,345.67</p><p>+2.34%</p></div><div><h3>风险评分</h3><p>7.2/10</p><p>中等风险</p></div></div><div><div><h2>实时行情</h2></div><div><table><thead><tr><th>股票代码</th><th>价格</th><th>涨跌幅</th><th>成交量</th></tr></thead><tbody></tbody></table></div></div><div><button>创建新策略</button><button>开始回测</button><button>查看报告"),wt=A("<tr><td></td><td>$</td><td></td><td>");function Le(){const[o]=oe([{symbol:"AAPL",price:150.25,change:"+2.15%",volume:"1.2M"},{symbol:"TSLA",price:245.8,change:"-1.25%",volume:"2.8M"},{symbol:"MSFT",price:310.45,change:"+0.85%",volume:"1.5M"},{symbol:"GOOGL",price:2650.3,change:"+1.45%",volume:"0.8M"}]);return ue(()=>{console.log("Dashboard mounted")}),(()=>{var i=kt(),a=i.firstChild,c=a.firstChild,n=c.nextSibling,l=a.nextSibling,s=l.firstChild,p=s.firstChild,m=p.nextSibling,E=m.nextSibling,k=s.nextSibling,b=k.firstChild,v=b.nextSibling,F=v.nextSibling,G=k.nextSibling,M=G.firstChild,Y=M.nextSibling,Q=Y.nextSibling,j=G.nextSibling,Z=j.firstChild,J=Z.nextSibling,ee=J.nextSibling,h=l.nextSibling,W=h.firstChild,N=W.firstChild,V=W.nextSibling,z=V.firstChild,L=z.firstChild,g=L.firstChild,P=g.firstChild,O=P.nextSibling,H=O.nextSibling,d=H.nextSibling,w=L.nextSibling,_=h.nextSibling,B=_.firstChild,R=B.nextSibling,x=R.nextSibling;return u(w,()=>o().map(r=>(()=>{var S=wt(),C=S.firstChild,$=C.nextSibling;$.firstChild;var I=$.nextSibling,D=I.nextSibling;return u(C,()=>r.symbol),u($,()=>r.price,null),u(I,()=>r.change),u(D,()=>r.volume),T(y=>{var q=e({borderBottom:"1px solid #e5e7eb",_hover:{backgroundColor:"gray.50"}}),f=e({padding:"12px 24px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),U=e({padding:"12px 24px",textAlign:"right",fontSize:"14px",color:"gray.900"}),X=e({padding:"12px 24px",textAlign:"right",fontSize:"14px",color:r.change.startsWith("+")?"green.600":"red.600"}),K=e({padding:"12px 24px",textAlign:"right",fontSize:"14px",color:"gray.600"});return q!==y.e&&t(S,y.e=q),f!==y.t&&t(C,y.t=f),U!==y.a&&t($,y.a=U),X!==y.o&&t(I,y.o=X),K!==y.i&&t(D,y.i=K),y},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),S})())),T(r=>{var S=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),C=e({marginBottom:"32px"}),$=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),I=e({fontSize:"16px",color:"gray.600"}),D=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"24px",marginBottom:"32px"}),y=e({backgroundColor:"white",padding:"24px",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb"}),q=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),f=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"}),U=e({fontSize:"12px",color:"green.600",marginTop:"4px"}),X=e({backgroundColor:"white",padding:"24px",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb"}),K=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),te=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"}),re=e({fontSize:"12px",color:"blue.600",marginTop:"4px"}),ie=e({backgroundColor:"white",padding:"24px",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb"}),ne=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),ae=e({fontSize:"24px",fontWeight:"bold",color:"green.600"}),de=e({fontSize:"12px",color:"green.600",marginTop:"4px"}),le=e({backgroundColor:"white",padding:"24px",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb"}),se=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),ve=e({fontSize:"24px",fontWeight:"bold",color:"orange.600"}),me=e({fontSize:"12px",color:"orange.600",marginTop:"4px"}),Se=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),ye=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),Ce=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),ke=e({overflowX:"auto"}),we=e({width:"100%",borderCollapse:"collapse"}),_e=e({backgroundColor:"gray.50"}),Te=e({padding:"12px 24px",textAlign:"left",fontSize:"14px",fontWeight:"500",color:"gray.600"}),Re=e({padding:"12px 24px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:"gray.600"}),Ae=e({padding:"12px 24px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:"gray.600"}),Ee=e({padding:"12px 24px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:"gray.600"}),ze=e({marginTop:"32px",display:"flex",gap:"16px",flexWrap:"wrap"}),Be=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),$e=e({padding:"12px 24px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"green.700"}}),Ie=e({padding:"12px 24px",backgroundColor:"white",color:"gray.700",border:"1px solid #e5e7eb",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"gray.50"}});return S!==r.e&&t(i,r.e=S),C!==r.t&&t(a,r.t=C),$!==r.a&&t(c,r.a=$),I!==r.o&&t(n,r.o=I),D!==r.i&&t(l,r.i=D),y!==r.n&&t(s,r.n=y),q!==r.s&&t(p,r.s=q),f!==r.h&&t(m,r.h=f),U!==r.r&&t(E,r.r=U),X!==r.d&&t(k,r.d=X),K!==r.l&&t(b,r.l=K),te!==r.u&&t(v,r.u=te),re!==r.c&&t(F,r.c=re),ie!==r.w&&t(G,r.w=ie),ne!==r.m&&t(M,r.m=ne),ae!==r.f&&t(Y,r.f=ae),de!==r.y&&t(Q,r.y=de),le!==r.g&&t(j,r.g=le),se!==r.p&&t(Z,r.p=se),ve!==r.b&&t(J,r.b=ve),me!==r.T&&t(ee,r.T=me),Se!==r.A&&t(h,r.A=Se),ye!==r.O&&t(W,r.O=ye),Ce!==r.I&&t(N,r.I=Ce),ke!==r.S&&t(V,r.S=ke),we!==r.W&&t(z,r.W=we),_e!==r.C&&t(g,r.C=_e),Te!==r.B&&t(P,r.B=Te),Re!==r.v&&t(O,r.v=Re),Ae!==r.k&&t(H,r.k=Ae),Ee!==r.x&&t(d,r.x=Ee),ze!==r.j&&t(_,r.j=ze),Be!==r.q&&t(B,r.q=Be),$e!==r.z&&t(R,r.z=$e),Ie!==r.P&&t(x,r.P=Ie),r},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),i})()}class _t{constructor(i={}){this.baseURL=i.baseURL||"https://api.yourdomain.com",this.timeout=i.timeout||1e4,this.defaultHeaders={"Content-Type":"application/json",...i.headers}}buildURL(i,a){const c=i.startsWith("http")?i:`${this.baseURL}${i}`;if(!a)return c;const n=new URLSearchParams;Object.entries(a).forEach(([s,p])=>{p!=null&&n.append(s,String(p))});const l=n.toString();return l?`${c}?${l}`:c}async handleResponse(i){const c=i.headers.get("content-type")?.includes("application/json");let n;try{n=c?await i.json():await i.text()}catch{throw new Error("响应解析失败")}if(!i.ok)throw{code:i.status,message:n.message||n.error||`HTTP ${i.status}`,details:n};return typeof n=="object"&&n!==null?{success:!0,data:n.data||n,message:n.message,code:i.status,timestamp:Date.now()}:{success:!0,data:n,code:i.status,timestamp:Date.now()}}async request(i){const{url:a,method:c="GET",params:n,data:l,headers:s={},timeout:p=this.timeout}=i,m={...this.defaultHeaders,...s},E=typeof window<"u"?localStorage.getItem("access_token"):null;E&&(m.Authorization=`Bearer ${E}`);const k={method:c,headers:m,signal:AbortSignal.timeout(p)};l&&c!=="GET"&&(l instanceof FormData?(delete m["Content-Type"],k.body=l):k.body=JSON.stringify(l));const b=this.buildURL(a,c==="GET"?n:void 0);try{const v=await fetch(b,k);return await this.handleResponse(v)}catch(v){throw console.error("[HttpClient] Request failed:",v),v}}async get(i,a,c){return this.request({url:i,method:"GET",params:a,headers:c})}async post(i,a,c){return this.request({url:i,method:"POST",data:a,headers:c})}async put(i,a,c){return this.request({url:i,method:"PUT",data:a,headers:c})}async delete(i,a,c){return this.request({url:i,method:"DELETE",params:a,headers:c})}async patch(i,a,c){return this.request({url:i,method:"PATCH",data:a,headers:c})}}const he=new _t,be={AUTH:{LOGIN:"/auth/login",LOGOUT:"/auth/logout",REFRESH:"/auth/refresh",REGISTER:"/auth/register",PROFILE:"/auth/me",CHANGE_PASSWORD:"/auth/change-password",RESET_PASSWORD:"/auth/reset-password",USERS:"/auth/users"},MARKET:{QUOTE:"/market/quote",QUOTES:"/market/quotes",KLINE:"/market/kline",HISTORY:"/market/history",SEARCH:"/market/search",OVERVIEW:"/market/overview",SECTORS:"/market/sectors",NEWS:"/market/news",RANKING:"/market/ranking",ORDERBOOK:"/market/orderbook",TICK:"/market/tick",STOCKS:"/market/stocks",WATCHLIST:"/market/watchlist",DEPTH:"/market/depth",SYMBOLS:"/market/symbols"},TRADING:{ACCOUNT:"/trading/account",POSITIONS:"/trading/positions",ORDERS:"/trading/orders",TRADES:"/trading/trades",SUBMIT:"/trading/submit",CANCEL:"/trading/cancel",MODIFY:"/trading/modify",HISTORY:"/trading/history"},STRATEGY:{LIST:"/strategy/list",DETAIL:"/strategy/detail",CREATE:"/strategy/create",UPDATE:"/strategy/update",DELETE:"/strategy/delete",START:"/strategy/start",STOP:"/strategy/stop",BACKTEST:"/strategy/backtest",PERFORMANCE:"/strategy/performance",SIGNALS:"/strategy/signals",TEMPLATES:"/strategy/templates",FILES:"/strategy-files"},BACKTEST:{RUN:"/backtest/run",RESULT:"/backtest/result",HISTORY:"/backtest/history",COMPARE:"/backtest/compare",START:"/backtest/start",STOP:"/backtest/stop",DELETE:"/backtest/delete",HEALTH:"/backtest/health"},USER:{PROFILE:"/user/profile",SETTINGS:"/user/settings",AVATAR:"/user/avatar",PREFERENCES:"/user/preferences"},SYSTEM:{HEALTH:"/health",STATUS:"/status",CONFIG:"/config"}};var Tt=A("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Rt=A('<p>点击"开始测试"按钮运行API连接测试'),At=A("<div>"),Et=A("<div><div><div></div><div><h3></h3><p>"),zt=A("<div>ms");function Bt(){const[o,i]=oe([]),a=(n,l,s,p)=>{i(m=>[...m,{name:n,status:l,message:s,duration:p}])},c=async()=>{i([]);try{const n=Date.now();await he.get(be.SYSTEM.HEALTH);const l=Date.now()-n;a("系统健康检查","success","连接成功",l)}catch(n){a("系统健康检查","error",n.message||"连接失败")}try{const n=Date.now();await he.get(be.MARKET.OVERVIEW);const l=Date.now()-n;a("市场概览","success","数据获取成功",l)}catch(n){a("市场概览","error",n.message||"数据获取失败")}try{const n=Date.now();await he.get(be.MARKET.SEARCH,{q:"AAPL"});const l=Date.now()-n;a("股票搜索","success","搜索成功",l)}catch(n){a("股票搜索","error",n.message||"搜索失败")}try{const n=Date.now();await he.get(be.AUTH.PROFILE);const l=Date.now()-n;a("用户信息","success","获取成功",l)}catch(n){a("用户信息","error",n.message||"获取失败（预期，因为未登录）")}};return ue(()=>{console.log("ApiTest mounted")}),(()=>{var n=Tt(),l=n.firstChild,s=l.firstChild,p=s.nextSibling,m=l.nextSibling,E=m.firstChild,k=m.nextSibling,b=k.firstChild,v=b.firstChild,F=b.nextSibling,G=k.nextSibling,M=G.firstChild,Y=M.firstChild,Q=M.nextSibling,j=Q.firstChild,Z=j.firstChild,J=Z.firstChild,ee=J.nextSibling,h=Z.nextSibling,W=h.firstChild,N=W.nextSibling,V=h.nextSibling,z=V.firstChild,L=z.nextSibling;return E.$$click=c,u(F,(()=>{var g=ge(()=>o().length===0);return()=>g()?(()=>{var P=Rt();return T(()=>t(P,e({color:"gray.500",textAlign:"center",padding:"40px 0"}))),P})():(()=>{var P=At();return u(P,()=>o().map(O=>(()=>{var H=Et(),d=H.firstChild,w=d.firstChild,_=w.nextSibling,B=_.firstChild,R=B.nextSibling;return u(w,()=>O.status==="success"?"✅":"❌"),u(B,()=>O.name),u(R,()=>O.message),u(H,(()=>{var x=ge(()=>!!O.duration);return()=>x()&&(()=>{var r=zt(),S=r.firstChild;return u(r,()=>O.duration,S),T(()=>t(r,e({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),r})()})(),null),T(x=>{var r=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:O.status==="success"?"green.200":"red.200",backgroundColor:O.status==="success"?"green.50":"red.50"}),S=e({display:"flex",alignItems:"center",gap:"12px"}),C=e({fontSize:"20px"}),$=e({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),I=e({fontSize:"14px",color:"gray.600"});return r!==x.e&&t(H,x.e=r),S!==x.t&&t(d,x.t=S),C!==x.a&&t(w,x.a=C),$!==x.o&&t(B,x.o=$),I!==x.i&&t(R,x.i=I),x},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),H})())),T(()=>t(P,e({display:"flex",flexDirection:"column",gap:"16px"}))),P})()})()),u(ee,()=>"https://api.yourdomain.com"),u(N,()=>"production"),T(g=>{var P=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),O=e({marginBottom:"32px"}),H=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),d=e({fontSize:"16px",color:"gray.600"}),w=e({marginBottom:"32px"}),_=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),B=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),R=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),x=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),r=e({padding:"24px"}),S=e({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),C=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),$=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),I=e({padding:"24px"}),D=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),y=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),q=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),f=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),U=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),X=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),K=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return P!==g.e&&t(n,g.e=P),O!==g.t&&t(l,g.t=O),H!==g.a&&t(s,g.a=H),d!==g.o&&t(p,g.o=d),w!==g.i&&t(m,g.i=w),_!==g.n&&t(E,g.n=_),B!==g.s&&t(k,g.s=B),R!==g.h&&t(b,g.h=R),x!==g.r&&t(v,g.r=x),r!==g.d&&t(F,g.d=r),S!==g.l&&t(G,g.l=S),C!==g.u&&t(M,g.u=C),$!==g.c&&t(Y,g.c=$),I!==g.w&&t(Q,g.w=I),D!==g.m&&t(j,g.m=D),y!==g.f&&t(J,g.f=y),q!==g.y&&t(ee,g.y=q),f!==g.g&&t(W,g.g=f),U!==g.p&&t(N,g.p=U),X!==g.b&&t(z,g.b=X),K!==g.T&&t(L,g.T=K),g},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),n})()}xe(["click"]);var $t=A("<div><div><h1>📈 市场行情</h1><p>实时股票行情数据和市场分析</p></div><div><input type=text placeholder=搜索股票代码或名称...><button>刷新数据</button></div><div><div><table><thead><tr><th>股票</th><th>价格</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高</th><th>最低</th><th>市值</th></tr></thead><tbody>"),It=A("<tr><td><div><div></div><div></div></div></td><td>$</td><td></td><td>%</td><td></td><td>$</td><td>$</td><td>$"),Wt=A("<div><h3> 详细信息</h3><p>这里可以显示选中股票的K线图、技术指标、新闻资讯等详细信息。");function Pt(){const[o,i]=oe([{symbol:"AAPL",name:"苹果公司",price:150.25,change:2.15,changePercent:1.45,volume:12e5,high:152.3,low:148.9,open:149.5,marketCap:25e11},{symbol:"TSLA",name:"特斯拉",price:245.8,change:-3.25,changePercent:-1.3,volume:28e5,high:250.1,low:244.5,open:248.9,marketCap:78e10},{symbol:"MSFT",name:"微软",price:310.45,change:2.85,changePercent:.93,volume:15e5,high:312,low:308.2,open:309.1,marketCap:23e11},{symbol:"GOOGL",name:"谷歌",price:2650.3,change:38.45,changePercent:1.47,volume:8e5,high:2665,low:2635.5,open:2640,marketCap:17e11},{symbol:"AMZN",name:"亚马逊",price:3245.67,change:-15.23,changePercent:-.47,volume:95e4,high:3260,low:3230.5,open:3255,marketCap:165e10}]),[a,c]=oe("AAPL"),[n,l]=oe("");let s;ue(()=>{console.log("Market page mounted"),s=setInterval(()=>{i(b=>b.map(v=>({...v,price:v.price+(Math.random()-.5)*2,change:v.change+(Math.random()-.5)*.5,changePercent:v.changePercent+(Math.random()-.5)*.2,volume:v.volume+Math.floor((Math.random()-.5)*1e5)})))},5e3)}),Ke(()=>{s&&clearInterval(s)});const p=()=>{const b=n().toLowerCase();return o().filter(v=>v.symbol.toLowerCase().includes(b)||v.name.toLowerCase().includes(b))},m=(b,v=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:v,maximumFractionDigits:v}).format(b),E=b=>b>=1e6?`${(b/1e6).toFixed(1)}M`:b>=1e3?`${(b/1e3).toFixed(1)}K`:b.toString(),k=b=>b?b>=1e12?`${(b/1e12).toFixed(2)}T`:b>=1e9?`${(b/1e9).toFixed(2)}B`:`${(b/1e6).toFixed(2)}M`:"-";return(()=>{var b=$t(),v=b.firstChild,F=v.firstChild,G=F.nextSibling,M=v.nextSibling,Y=M.firstChild,Q=Y.nextSibling,j=M.nextSibling,Z=j.firstChild,J=Z.firstChild,ee=J.firstChild,h=ee.firstChild,W=h.firstChild,N=W.nextSibling,V=N.nextSibling,z=V.nextSibling,L=z.nextSibling,g=L.nextSibling,P=g.nextSibling,O=P.nextSibling,H=ee.nextSibling;return Y.$$input=d=>l(d.currentTarget.value),u(H,()=>p().map(d=>(()=>{var w=It(),_=w.firstChild,B=_.firstChild,R=B.firstChild,x=R.nextSibling,r=_.nextSibling;r.firstChild;var S=r.nextSibling,C=S.nextSibling,$=C.firstChild,I=C.nextSibling,D=I.nextSibling;D.firstChild;var y=D.nextSibling;y.firstChild;var q=y.nextSibling;return q.firstChild,w.$$click=()=>c(d.symbol),u(R,()=>d.symbol),u(x,()=>d.name),u(r,()=>m(d.price),null),u(S,()=>d.change>=0?"+":"",null),u(S,()=>m(d.change),null),u(C,()=>d.changePercent>=0?"+":"",$),u(C,()=>m(d.changePercent),$),u(I,()=>E(d.volume)),u(D,()=>m(d.high),null),u(y,()=>m(d.low),null),u(q,()=>k(d.marketCap),null),T(f=>{var U=e({borderBottom:"1px solid #e5e7eb",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:a()===d.symbol?"blue.50":"transparent",_hover:{backgroundColor:"gray.50"}}),X=e({padding:"16px"}),K=e({fontSize:"14px",fontWeight:"600",color:"gray.900"}),te=e({fontSize:"12px",color:"gray.500"}),re=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.900"}),ie=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:d.change>=0?"green.600":"red.600"}),ne=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:d.changePercent>=0?"green.600":"red.600"}),ae=e({padding:"16px",textAlign:"right",fontSize:"14px",color:"gray.600"}),de=e({padding:"16px",textAlign:"right",fontSize:"14px",color:"gray.600"}),le=e({padding:"16px",textAlign:"right",fontSize:"14px",color:"gray.600"}),se=e({padding:"16px",textAlign:"right",fontSize:"14px",color:"gray.600"});return U!==f.e&&t(w,f.e=U),X!==f.t&&t(_,f.t=X),K!==f.a&&t(R,f.a=K),te!==f.o&&t(x,f.o=te),re!==f.i&&t(r,f.i=re),ie!==f.n&&t(S,f.n=ie),ne!==f.s&&t(C,f.s=ne),ae!==f.h&&t(I,f.h=ae),de!==f.r&&t(D,f.r=de),le!==f.d&&t(y,f.d=le),se!==f.l&&t(q,f.l=se),f},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),w})())),u(b,(()=>{var d=ge(()=>!!a());return()=>d()&&(()=>{var w=Wt(),_=w.firstChild,B=_.firstChild,R=_.nextSibling;return u(_,a,B),T(x=>{var r=e({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"24px"}),S=e({fontSize:"20px",fontWeight:"bold",color:"gray.900",marginBottom:"16px"}),C=e({color:"gray.600"});return r!==x.e&&t(w,x.e=r),S!==x.t&&t(_,x.t=S),C!==x.a&&t(R,x.a=C),x},{e:void 0,t:void 0,a:void 0}),w})()})(),null),T(d=>{var w=e({padding:"24px",maxWidth:"1400px",margin:"0 auto"}),_=e({marginBottom:"32px"}),B=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),R=e({fontSize:"16px",color:"gray.600"}),x=e({marginBottom:"24px",display:"flex",gap:"16px",alignItems:"center"}),r=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",fontSize:"14px",width:"300px",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"}}),S=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),C=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),$=e({overflowX:"auto"}),I=e({width:"100%",borderCollapse:"collapse"}),D=e({backgroundColor:"gray.50"}),y=e({padding:"16px",textAlign:"left",fontSize:"14px",fontWeight:"600",color:"gray.700"}),q=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"}),f=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"}),U=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"}),X=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"}),K=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"}),te=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"}),re=e({padding:"16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"gray.700"});return w!==d.e&&t(b,d.e=w),_!==d.t&&t(v,d.t=_),B!==d.a&&t(F,d.a=B),R!==d.o&&t(G,d.o=R),x!==d.i&&t(M,d.i=x),r!==d.n&&t(Y,d.n=r),S!==d.s&&t(Q,d.s=S),C!==d.h&&t(j,d.h=C),$!==d.r&&t(Z,d.r=$),I!==d.d&&t(J,d.d=I),D!==d.l&&t(h,d.l=D),y!==d.u&&t(W,d.u=y),q!==d.c&&t(N,d.c=q),f!==d.w&&t(V,d.w=f),U!==d.m&&t(z,d.m=U),X!==d.f&&t(L,d.f=X),K!==d.y&&t(g,d.y=K),te!==d.g&&t(P,d.g=te),re!==d.p&&t(O,d.p=re),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0}),T(()=>Y.value=n()),b})()}xe(["input","click"]);var Ot=A("<div>策略管理页面开发中..."),Lt=A("<div>回测分析页面开发中..."),Mt=A("<div>实盘交易页面开发中..."),Dt=A("<div>投资组合页面开发中..."),Ft=A("<div>风险管理页面开发中..."),jt=A("<div>系统设置页面开发中..."),Nt=A("<div><aside><div><div>量</div></div><nav></nav><div><button></button></div></aside><main><header><div><h1></h1></div><div><button>帮助</button><button>设置</button><div>用</div></div></header><div>"),Ht=A("<span>量化交易平台"),qt=A("<div><button><span>"),Gt=A("<span>");function Yt(){const[o,i]=oe("dashboard"),[a,c]=oe(!1),n=()=>{switch(o()){case"dashboard":return ce(Le,{});case"market":return ce(Pt,{});case"strategy":return(()=>{var s=Ot();return T(()=>t(s,e({padding:"24px"}))),s})();case"backtest":return(()=>{var s=Lt();return T(()=>t(s,e({padding:"24px"}))),s})();case"trading":return(()=>{var s=Mt();return T(()=>t(s,e({padding:"24px"}))),s})();case"portfolio":return(()=>{var s=Dt();return T(()=>t(s,e({padding:"24px"}))),s})();case"risk":return(()=>{var s=Ft();return T(()=>t(s,e({padding:"24px"}))),s})();case"settings":return(()=>{var s=jt();return T(()=>t(s,e({padding:"24px"}))),s})();case"api-test":return ce(Bt,{});default:return ce(Le,{})}},l=[{id:"dashboard",label:"仪表板",icon:"📊",children:[]},{id:"market",label:"行情分析",icon:"📈",children:[]},{id:"strategy",label:"量化策略",icon:"🧠",children:[{id:"strategy-list",label:"策略列表"},{id:"strategy-create",label:"创建策略"}]},{id:"backtest",label:"回测分析",icon:"📊",children:[]},{id:"trading",label:"实盘交易",icon:"💰",children:[]},{id:"portfolio",label:"投资组合",icon:"📋",children:[]},{id:"risk",label:"风险管理",icon:"⚠️",children:[]},{id:"settings",label:"系统设置",icon:"⚙️",children:[]},{id:"api-test",label:"API测试",icon:"🔧",children:[]}];return(()=>{var s=Nt(),p=s.firstChild,m=p.firstChild,E=m.firstChild,k=m.nextSibling,b=k.nextSibling,v=b.firstChild,F=p.nextSibling,G=F.firstChild,M=G.firstChild,Y=M.firstChild,Q=M.nextSibling,j=Q.firstChild,Z=j.nextSibling,J=Z.nextSibling,ee=G.nextSibling;return u(m,(()=>{var h=ge(()=>!a());return()=>h()&&(()=>{var W=Ht();return T(()=>t(W,e({fontSize:"16px",fontWeight:"600",color:"#262626"}))),W})()})(),null),u(k,()=>l.map(h=>(()=>{var W=qt(),N=W.firstChild,V=N.firstChild;return N.$$click=()=>i(h.id),u(V,()=>h.icon),u(N,(()=>{var z=ge(()=>!a());return()=>z()&&(()=>{var L=Gt();return u(L,()=>h.label),T(()=>t(L,e({fontWeight:o()===h.id?"500":"400"}))),L})()})(),null),T(z=>{var L=e({width:"100%",padding:a()?"12px 20px":"12px 16px",border:"none",backgroundColor:o()===h.id?"#e6f7ff":"transparent",color:o()===h.id?"#1890ff":"#595959",fontSize:"14px",textAlign:"left",cursor:"pointer",transition:"all 0.2s",display:"flex",alignItems:"center",gap:"12px",borderLeft:o()===h.id?"3px solid #1890ff":"3px solid transparent",_hover:{backgroundColor:"#f5f5f5",color:"#1890ff"}}),g=e({fontSize:"16px"});return L!==z.e&&t(N,z.e=L),g!==z.t&&t(V,z.t=g),z},{e:void 0,t:void 0}),W})())),v.$$click=()=>c(!a()),u(v,()=>a()?"→":"←"),u(Y,()=>l.find(h=>h.id===o())?.label||"仪表板"),u(ee,n),T(h=>{var W=e({display:"flex",minHeight:"100vh",backgroundColor:"#f5f5f5"}),N=e({width:a()?"64px":"240px",backgroundColor:"white",borderRight:"1px solid #e8e8e8",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",height:"100vh",zIndex:1e3}),V=e({padding:"16px",borderBottom:"1px solid #e8e8e8",display:"flex",alignItems:"center",gap:"12px"}),z=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"16px",fontWeight:"bold"}),L=e({flex:1,padding:"8px 0",overflowY:"auto"}),g=e({padding:"16px",borderTop:"1px solid #e8e8e8"}),P=e({width:"100%",padding:"8px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",cursor:"pointer",fontSize:"12px",color:"#595959",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),O=e({flex:1,marginLeft:a()?"64px":"240px",transition:"margin-left 0.3s ease",display:"flex",flexDirection:"column"}),H=e({backgroundColor:"white",borderBottom:"1px solid #e8e8e8",padding:"0 24px",height:"64px",display:"flex",alignItems:"center",justifyContent:"space-between"}),d=e({display:"flex",alignItems:"center",gap:"16px"}),w=e({fontSize:"18px",fontWeight:"500",color:"#262626",margin:0}),_=e({display:"flex",alignItems:"center",gap:"16px"}),B=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),R=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),x=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),r=e({flex:1,backgroundColor:"#f5f5f5",overflow:"auto"});return W!==h.e&&t(s,h.e=W),N!==h.t&&t(p,h.t=N),V!==h.a&&t(m,h.a=V),z!==h.o&&t(E,h.o=z),L!==h.i&&t(k,h.i=L),g!==h.n&&t(b,h.n=g),P!==h.s&&t(v,h.s=P),O!==h.h&&t(F,h.h=O),H!==h.r&&t(G,h.r=H),d!==h.d&&t(M,h.d=d),w!==h.l&&t(Y,h.l=w),_!==h.u&&t(Q,h.u=_),B!==h.c&&t(j,h.c=B),R!==h.w&&t(Z,h.w=R),x!==h.m&&t(J,h.m=x),r!==h.f&&t(ee,h.f=r),h},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),s})()}xe(["click"]);const Me=document.getElementById("root");Me&&Ve(()=>ce(Yt,{}),Me);
