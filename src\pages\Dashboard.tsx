import { createSignal, onMount } from 'solid-js';
import { css } from '../../styled-system/css';

export default function Dashboard() {
  const [marketData] = createSignal([
    { symbol: 'AAPL', price: 150.25, change: '+2.15%', volume: '1.2M' },
    { symbol: 'TSLA', price: 245.80, change: '-1.25%', volume: '2.8M' },
    { symbol: 'MSFT', price: 310.45, change: '+0.85%', volume: '1.5M' },
    { symbol: 'GOOGL', price: 2650.30, change: '+1.45%', volume: '0.8M' }
  ]);

  onMount(() => {
    console.log('Dashboard mounted');
  });

  return (
    <div class={css({
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    })}>
      {/* 页面标题 */}
      <div class={css({
        marginBottom: '32px'
      })}>
        <h1 class={css({
          fontSize: '32px',
          fontWeight: 'bold',
          color: 'gray.900',
          marginBottom: '8px'
        })}>
          📊 量化交易仪表板
        </h1>
        <p class={css({
          fontSize: '16px',
          color: 'gray.600'
        })}>
          实时监控市场数据和交易策略表现
        </p>
      </div>

      {/* 统计卡片 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      })}>
        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '14px',
            fontWeight: '500',
            color: 'gray.600',
            marginBottom: '8px'
          })}>
            总资产
          </h3>
          <p class={css({
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'gray.900'
          })}>
            ¥1,234,567.89
          </p>
          <p class={css({
            fontSize: '12px',
            color: 'green.600',
            marginTop: '4px'
          })}>
            +5.67% 今日
          </p>
        </div>

        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '14px',
            fontWeight: '500',
            color: 'gray.600',
            marginBottom: '8px'
          })}>
            活跃策略
          </h3>
          <p class={css({
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'gray.900'
          })}>
            12
          </p>
          <p class={css({
            fontSize: '12px',
            color: 'blue.600',
            marginTop: '4px'
          })}>
            3个新策略
          </p>
        </div>

        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '14px',
            fontWeight: '500',
            color: 'gray.600',
            marginBottom: '8px'
          })}>
            今日收益
          </h3>
          <p class={css({
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'green.600'
          })}>
            +¥12,345.67
          </p>
          <p class={css({
            fontSize: '12px',
            color: 'green.600',
            marginTop: '4px'
          })}>
            +2.34%
          </p>
        </div>

        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '14px',
            fontWeight: '500',
            color: 'gray.600',
            marginBottom: '8px'
          })}>
            风险评分
          </h3>
          <p class={css({
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'orange.600'
          })}>
            7.2/10
          </p>
          <p class={css({
            fontSize: '12px',
            color: 'orange.600',
            marginTop: '4px'
          })}>
            中等风险
          </p>
        </div>
      </div>

      {/* 市场数据表格 */}
      <div class={css({
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        overflow: 'hidden'
      })}>
        <div class={css({
          padding: '24px',
          borderBottom: '1px solid #e5e7eb'
        })}>
          <h2 class={css({
            fontSize: '20px',
            fontWeight: 'bold',
            color: 'gray.900'
          })}>
            实时行情
          </h2>
        </div>
        
        <div class={css({
          overflowX: 'auto'
        })}>
          <table class={css({
            width: '100%',
            borderCollapse: 'collapse'
          })}>
            <thead>
              <tr class={css({
                backgroundColor: 'gray.50'
              })}>
                <th class={css({
                  padding: '12px 24px',
                  textAlign: 'left',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'gray.600'
                })}>
                  股票代码
                </th>
                <th class={css({
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'gray.600'
                })}>
                  价格
                </th>
                <th class={css({
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'gray.600'
                })}>
                  涨跌幅
                </th>
                <th class={css({
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'gray.600'
                })}>
                  成交量
                </th>
              </tr>
            </thead>
            <tbody>
              {marketData().map((item) => (
                <tr class={css({
                  borderBottom: '1px solid #e5e7eb',
                  _hover: {
                    backgroundColor: 'gray.50'
                  }
                })}>
                  <td class={css({
                    padding: '12px 24px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'gray.900'
                  })}>
                    {item.symbol}
                  </td>
                  <td class={css({
                    padding: '12px 24px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: 'gray.900'
                  })}>
                    ${item.price}
                  </td>
                  <td class={css({
                    padding: '12px 24px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: item.change.startsWith('+') ? 'green.600' : 'red.600'
                  })}>
                    {item.change}
                  </td>
                  <td class={css({
                    padding: '12px 24px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: 'gray.600'
                  })}>
                    {item.volume}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 快速操作 */}
      <div class={css({
        marginTop: '32px',
        display: 'flex',
        gap: '16px',
        flexWrap: 'wrap'
      })}>
        <button class={css({
          padding: '12px 24px',
          backgroundColor: 'blue.600',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.2s',
          _hover: {
            backgroundColor: 'blue.700'
          }
        })}>
          创建新策略
        </button>
        
        <button class={css({
          padding: '12px 24px',
          backgroundColor: 'green.600',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.2s',
          _hover: {
            backgroundColor: 'green.700'
          }
        })}>
          开始回测
        </button>
        
        <button class={css({
          padding: '12px 24px',
          backgroundColor: 'white',
          color: 'gray.700',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.2s',
          _hover: {
            backgroundColor: 'gray.50'
          }
        })}>
          查看报告
        </button>
      </div>
    </div>
  );
}
