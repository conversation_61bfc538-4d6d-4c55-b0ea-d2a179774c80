/**
 * 应用常量定义
 */

// API配置常量
export const API_CONFIG = {
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
  BASE_URL: import.meta.env?.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
} as const

// API路径常量
export const API_PATHS = {
  // 用户认证相关
  AUTH: { 
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    REGISTER: '/auth/register',
    PROFILE: '/auth/me',
    CHANGE_PASSWORD: '/auth/change-password',
    RESET_PASSWORD: '/auth/reset-password',
    USERS: '/auth/users'
  },

  // 市场数据相关
  MARKET: {
    QUOTE: '/market/quote',
    QUOTES: '/market/quotes',
    KLINE: '/market/kline',
    HISTORY: '/market/history',
    SEARCH: '/market/search',
    OVERVIEW: '/market/overview',
    SECTORS: '/market/sectors',
    NEWS: '/market/news',
    RANKING: '/market/ranking',
    ORDERBOOK: '/market/orderbook',
    TICK: '/market/tick',
    STOCKS: '/market/stocks',
    WATCHLIST: '/market/watchlist',
    DEPTH: '/market/depth',
    SYMBOLS: '/market/symbols'
  },

  // 交易相关
  TRADING: {
    ACCOUNT: '/trading/account',
    POSITIONS: '/trading/positions',
    ORDERS: '/trading/orders',
    TRADES: '/trading/trades',
    SUBMIT: '/trading/submit',
    CANCEL: '/trading/cancel',
    MODIFY: '/trading/modify',
    HISTORY: '/trading/history'
  },

  // 策略相关
  STRATEGY: {
    LIST: '/strategy/list',
    DETAIL: '/strategy/detail',
    CREATE: '/strategy/create',
    UPDATE: '/strategy/update',
    DELETE: '/strategy/delete',
    START: '/strategy/start',
    STOP: '/strategy/stop',
    BACKTEST: '/strategy/backtest',
    PERFORMANCE: '/strategy/performance',
    SIGNALS: '/strategy/signals',
    TEMPLATES: '/strategy/templates',
    FILES: '/strategy-files'
  },

  // 回测相关
  BACKTEST: {
    RUN: '/backtest/run',
    RESULT: '/backtest/result',
    HISTORY: '/backtest/history',
    COMPARE: '/backtest/compare',
    START: '/backtest/start',
    STOP: '/backtest/stop',
    DELETE: '/backtest/delete',
    HEALTH: '/backtest/health'
  },

  // 用户管理
  USER: {
    PROFILE: '/user/profile',
    SETTINGS: '/user/settings',
    AVATAR: '/user/avatar',
    PREFERENCES: '/user/preferences'
  },

  // 系统相关
  SYSTEM: {
    HEALTH: '/health',
    STATUS: '/status',
    CONFIG: '/config'
  }
} as const

// WebSocket路径常量
export const WS_PATHS = {
  MARKET: '/ws/market',
  TRADING: '/ws/trading',
  STRATEGY: '/ws/strategy',
  GENERAL: '/ws'
} as const

// 环境配置
export const ENV_CONFIG = {
  isDevelopment: import.meta.env?.MODE === 'development',
  isProduction: import.meta.env?.MODE === 'production',
  isTesting: import.meta.env?.MODE === 'test',
  apiBaseUrl: import.meta.env?.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  wsUrl: import.meta.env?.VITE_WS_URL || 'ws://localhost:8000/api/v1',
  // 可通过环境变量控制模拟模式
  enableMock: import.meta.env?.VITE_USE_MOCK === 'true' || import.meta.env?.MODE === 'development',
  enableDevtools: import.meta.env?.VITE_ENABLE_DEVTOOLS === 'true' || import.meta.env?.MODE === 'development'
} as const

// 市场配置
export const MARKET_CONFIG = {
  UPDATE_INTERVAL: 1000, // 行情更新间隔(ms)
  RECONNECT_INTERVAL: 5000, // 重连间隔(ms)
  MAX_RECONNECT_ATTEMPTS: 10, // 最大重连次数
  HEARTBEAT_INTERVAL: 30000, // 心跳间隔(ms)
  DEFAULT_SYMBOLS: ['000001', '000002', '000300'], // 默认关注股票
  KLINE_PERIODS: ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M'] as const
} as const

// 交易配置
export const TRADING_CONFIG = {
  ORDER_TYPES: ['market', 'limit', 'stop', 'stop_limit'] as const,
  ORDER_SIDES: ['buy', 'sell'] as const,
  ORDER_STATUS: ['pending', 'filled', 'cancelled', 'rejected'] as const,
  POSITION_SIDES: ['long', 'short'] as const,
  DEFAULT_LEVERAGE: 1,
  MAX_LEVERAGE: 10,
  MIN_ORDER_AMOUNT: 100 // 最小下单金额
} as const

// 策略配置
export const STRATEGY_CONFIG = {
  TYPES: ['trend_following', 'mean_reversion', 'momentum', 'arbitrage', 'market_making'] as const,
  STATUS: ['inactive', 'active', 'paused', 'error'] as const,
  RISK_LEVELS: ['low', 'medium', 'high'] as const,
  FREQUENCIES: ['tick', 'minute', 'hour', 'day'] as const,
  CODE_LANGUAGES: ['python', 'javascript', 'pine'] as const,
  MAX_STRATEGIES: 50, // 最大策略数量
  DEFAULT_CAPITAL: 100000 // 默认初始资金
} as const

// 回测配置
export const BACKTEST_CONFIG = {
  STATUS: ['pending', 'running', 'completed', 'failed', 'cancelled'] as const,
  DEFAULT_PERIOD: '1y', // 默认回测周期
  MIN_PERIOD: '1d', // 最小回测周期
  MAX_PERIOD: '5y', // 最大回测周期
  DEFAULT_CAPITAL: 100000, // 默认初始资金
  COMMISSION_RATE: 0.0003, // 默认手续费率
  SLIPPAGE_RATE: 0.0001 // 默认滑点率
} as const

// 主题配置
export const THEME_CONFIG = {
  THEMES: ['light', 'dark', 'auto'] as const,
  DEFAULT_THEME: 'light' as const,
  STORAGE_KEY: 'quant-theme'
} as const

// 语言配置
export const LANGUAGE_CONFIG = {
  LANGUAGES: ['zh-CN', 'en-US'] as const,
  DEFAULT_LANGUAGE: 'zh-CN' as const,
  STORAGE_KEY: 'quant-language'
} as const

// 存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  THEME: 'quant-theme',
  LANGUAGE: 'quant-language',
  WATCHLIST: 'quant-watchlist',
  LAYOUT: 'quant-layout'
} as const

// 错误代码
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  CREATE_SUCCESS: '创建成功'
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  AUTH_ERROR: '认证失败，请重新登录',
  PERMISSION_ERROR: '权限不足，无法执行此操作',
  VALIDATION_ERROR: '输入数据格式错误',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  UNKNOWN_ERROR: '未知错误，请联系技术支持'
} as const
