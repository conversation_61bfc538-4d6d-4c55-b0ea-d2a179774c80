import { Component, JSX, splitProps } from 'solid-js';
import { css, cx } from '../../styled-system/css';

interface CardProps extends JSX.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  headerAction?: JSX.Element;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
}

const Card: Component<CardProps> = (props) => {
  const [local, others] = splitProps(props, [
    'title',
    'subtitle', 
    'headerAction',
    'padding',
    'shadow',
    'border',
    'hover',
    'children',
    'class'
  ]);

  const baseStyles = css({
    backgroundColor: 'white',
    borderRadius: '12px',
    overflow: 'hidden',
    transition: 'all 0.2s',
  });

  const shadowStyles = {
    none: '',
    sm: css({
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
    }),
    md: css({
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.07)',
    }),
    lg: css({
      boxShadow: '0 10px 15px rgba(0, 0, 0, 0.1)',
    }),
  };

  const borderStyle = css({
    border: '1px solid',
    borderColor: 'gray.200',
  });

  const hoverStyle = css({
    _hover: {
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
    },
  });

  const paddingStyles = {
    none: '',
    sm: css({
      padding: '16px',
    }),
    md: css({
      padding: '24px',
    }),
    lg: css({
      padding: '32px',
    }),
  };

  const headerStyles = css({
    padding: '24px 24px 0 24px',
    marginBottom: '16px',
  });

  const titleStyles = css({
    fontSize: '18px',
    fontWeight: '600',
    color: 'gray.900',
    marginBottom: '4px',
  });

  const subtitleStyles = css({
    fontSize: '14px',
    color: 'gray.600',
  });

  const headerActionStyles = css({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  });

  const shadow = local.shadow || 'md';
  const padding = local.padding || 'md';

  return (
    <div
      class={cx(
        baseStyles,
        shadowStyles[shadow],
        local.border && borderStyle,
        local.hover && hoverStyle,
        local.class
      )}
      {...others}
    >
      {(local.title || local.subtitle || local.headerAction) && (
        <div class={headerStyles}>
          <div class={local.headerAction ? headerActionStyles : ''}>
            <div>
              {local.title && <h3 class={titleStyles}>{local.title}</h3>}
              {local.subtitle && <p class={subtitleStyles}>{local.subtitle}</p>}
            </div>
            {local.headerAction && local.headerAction}
          </div>
        </div>
      )}
      
      <div class={paddingStyles[padding]}>
        {local.children}
      </div>
    </div>
  );
};

export default Card;
