!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("jotai/vanilla"),require("jotai/react")):"function"==typeof define&&define.amd?define(["exports","jotai/vanilla","jotai/react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).jotai={},e.j<PERSON><PERSON>,e.jotaiReact)}(this,function(e,t,o){"use strict";Object.keys(t).forEach(function(o){"default"===o||Object.prototype.hasOwnProperty.call(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[o]}})}),Object.keys(o).forEach(function(t){"default"===t||Object.prototype.hasOwnProperty.call(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:function(){return o[t]}})})});
