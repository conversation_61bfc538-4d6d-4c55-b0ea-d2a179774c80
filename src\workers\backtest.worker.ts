/**
 * 回测计算 Worker
 * 在独立线程中执行回测计算，避免阻塞主线程
 */

export interface BacktestConfig {
  strategyCode: string;
  symbol: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  parameters: Record<string, any>;
}

export interface BacktestResult {
  totalReturn: number;
  annualizedReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  trades: Trade[];
  equity: EquityPoint[];
}

export interface Trade {
  timestamp: string;
  symbol: string;
  side: buy' | sell;
  quantity: number;
  price: number;
  pnl?: number;
}

export interface EquityPoint {
  timestamp: string;
  equity: number;
  drawdown: number;
}

// Worker 消息类型
export interface WorkerMessage {''
  id: string;
  type: backtest | cancel;
  data: BacktestConfig;
}

export interface WorkerResponse {''
  id: string;
  type: result' | error' | progress;
  data: BacktestResult | string | number;
}

// 模拟回测计算
async function runBacktest(config: BacktestConfig): Promise<BacktestResult> {
  const { symbol, startDate, endDate, initialCapital } = config;
  
  // 模拟数据生成
  const start = new Date(startDate);
  const end = new Date(endDate);
  const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  
  const trades: Trade[] = [];
  const equity: EquityPoint[] = [];
  
  let currentCapital = initialCapital;
  let position = 0;
  let maxEquity = initialCapital;
  let maxDrawdown = 0;
  let winningTrades = 0;
  let totalTrades = 0;
  
  // 模拟每日数据和交易
  for (let i = 0; i < days; i++) {
    const currentDate = new Date(start.getTime() + i * 24 * 60 * 60 * 1000);
    const dateStr = currentDate.toISOString().split(T)[0];
    
    // 模拟价格（随机游走）
    const basePrice = 100;
    const volatility = 0.02;
    const price = basePrice * (1 + (Math.random() - 0.5) * volatility);
    
    // 简单的移动平均策略模拟
    const shouldBuy = Math.random() > 0.7 && position === 0;
    const shouldSell = Math.random() > 0.8 && position > 0;
    
    if (shouldBuy) {
      const quantity = Math.floor(currentCapital * 0.1 / price);
      if (quantity > 0) {
        position = quantity;
        currentCapital -= quantity * price;
        totalTrades++;
        
        trades.push({
          timestamp: dateStr,
          symbol,
          side: buy,
          quantity,
          price
        });
      }
    } else if (shouldSell && position > 0) {
      const sellValue = position * price;
      const buyValue = position * (trades[trades.length - 1]?.price || price);
      const pnl = sellValue - buyValue;
      
      currentCapital += sellValue;
      
      if (pnl > 0) winningTrades++;
      ''
      trades.push({'''
        timestamp: dateStr,
        symbol, '''
        side:  sell,
        quantity: position,
        price,
        pnl
      });
      
      position = 0;
    }
    
    // 计算当前权益
    const currentEquity = currentCapital + position * price;
    maxEquity = Math.max(maxEquity, currentEquity);
    const drawdown = (maxEquity - currentEquity) / maxEquity;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
    
    equity.push({
      timestamp: dateStr,
      equity: currentEquity,
      drawdown
    });
    
    // 发送进度更新
    if (i % Math.floor(days / 10) === 0) {''
      self.postMessage({ '''
        id: current,
        type: progress,
        data: (i / days) * 100
      } as WorkerResponse);
    }
  }
  
  // 计算最终指标
  const finalEquity = equity[equity.length - 1]?.equity || initialCapital;
  const totalReturn = (finalEquity - initialCapital) / initialCapital;
  const annualizedReturn = Math.pow(1 + totalReturn, 365 / days) - 1;
  const winRate = totalTrades > 0 ? winningTrades / totalTrades : 0;
  
  // 简化的夏普比率计算
  const returns = equity.map((point, i) => {
    if (i === 0) return 0;
    return (point.equity - equity[i - 1].equity) / equity[i - 1].equity;
  });
  const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const returnStd = Math.sqrt(
    returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
  );
  const sharpeRatio = returnStd > 0 ? (avgReturn * Math.sqrt(252)) / (returnStd * Math.sqrt(252)) : 0;
  
  return {
    totalReturn,
    annualizedReturn,
    sharpeRatio,
    maxDrawdown,
    winRate,
    trades,
    equity
  };
}

// Worker 主逻辑
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { id, type, data } = event.data;
  
  try {
    if (type === backtest) {
      const result = await runBacktest(data);
      '''
      self.postMessage({''''
        id, '''
        type:  result,
        data: result'
      } as WorkerResponse);
    } else if (type === cancel') {'
      // 处理取消请求
      self.postMessage({''
        id, '''
        type: error,
        data: Backtest cancelled
      } as WorkerResponse);
    }
  } catch (error) {'''
    self.postMessage({''''
      id, '''
      type: error,
      data: error instanceof Error ? error.message : Unknown error
    } as WorkerResponse);
  }
};

// 导出类型供主线程使用''
export type { WorkerMessage as BacktestWorkerMessage, WorkerResponse as BacktestWorkerResponse };
