const it=(e,t)=>e===t,x=Symbol("solid-proxy"),lt=typeof Proxy=="function",Re=Symbol("solid-track"),z={equals:it};let $e=Be;const T=1,ee=2,ke={owned:null,cleanups:null,context:null,owner:null};var p=null;let ae=null,ut=null,b=null,P=null,$=null,le=0;function Te(e,t){const n=b,r=p,o=e.length===0,s=t===void 0?r:t,l=o?ke:{owned:null,cleanups:null,context:s?s.context:null,owner:s},i=o?e:()=>e(()=>C(()=>H(l)));p=l,b=null;try{return j(i,!0)}finally{b=n,p=r}}function U(e,t){t=t?Object.assign({},z,t):z;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},r=o=>(typeof o=="function"&&(o=o(n.value)),Ie(n,o));return[Fe.bind(n),r]}function V(e,t,n){const r=Se(e,t,!1,T);Y(r)}function ct(e,t,n){$e=gt;const r=Se(e,t,!1,T);r.user=!0,$?$.push(r):Y(r)}function v(e,t,n){n=n?Object.assign({},z,n):z;const r=Se(e,t,!0,0);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,Y(r),Fe.bind(r)}function je(e){return j(e,!1)}function C(e){if(b===null)return e();const t=b;b=null;try{return e()}finally{b=t}}function be(e,t,n){const r=Array.isArray(e);let o,s=n&&n.defer;return l=>{let i;if(r){i=Array(e.length);for(let c=0;c<e.length;c++)i[c]=e[c]()}else i=e();if(s)return s=!1,l;const u=C(()=>t(i,o,l));return o=i,u}}function nn(e){ct(()=>C(e))}function _e(e){return p===null||(p.cleanups===null?p.cleanups=[e]:p.cleanups.push(e)),e}function ge(){return b}function De(){return p}function Ne(e,t){const n=p,r=b;p=e,b=null;try{return j(t,!0)}catch(o){ve(o)}finally{p=n,b=r}}function at(e){const t=b,n=p;return Promise.resolve().then(()=>{b=t,p=n;let r;return j(e,!1),b=p=null,r?r.done:void 0})}const[rn,on]=U(!1);function Ue(e,t){const n=Symbol("context");return{id:n,Provider:yt(n),defaultValue:e}}function ft(e){let t;return p&&p.context&&(t=p.context[e.id])!==void 0?t:e.defaultValue}function Ae(e){const t=v(e),n=v(()=>pe(t()));return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}function Fe(){if(this.sources&&this.state)if(this.state===T)Y(this);else{const e=P;P=null,j(()=>ne(this),!1),P=e}if(b){const e=this.observers?this.observers.length:0;b.sources?(b.sources.push(this),b.sourceSlots.push(e)):(b.sources=[this],b.sourceSlots=[e]),this.observers?(this.observers.push(b),this.observerSlots.push(b.sources.length-1)):(this.observers=[b],this.observerSlots=[b.sources.length-1])}return this.value}function Ie(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&j(()=>{for(let o=0;o<e.observers.length;o+=1){const s=e.observers[o],l=ae&&ae.running;l&&ae.disposed.has(s),(l?!s.tState:!s.state)&&(s.pure?P.push(s):$.push(s),s.observers&&Ke(s)),l||(s.state=T)}if(P.length>1e6)throw P=[],new Error},!1)),t}function Y(e){if(!e.fn)return;H(e);const t=le;ht(e,e.value,t)}function ht(e,t,n){let r;const o=p,s=b;b=p=e;try{r=e.fn(t)}catch(l){return e.pure&&(e.state=T,e.owned&&e.owned.forEach(H),e.owned=null),e.updatedAt=n+1,ve(l)}finally{b=s,p=o}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?Ie(e,r):e.value=r,e.updatedAt=n)}function Se(e,t,n,r=T,o){const s={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:p,context:p?p.context:null,pure:n};return p===null||p!==ke&&(p.owned?p.owned.push(s):p.owned=[s]),s}function te(e){if(e.state===0)return;if(e.state===ee)return ne(e);if(e.suspense&&C(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<le);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===T)Y(e);else if(e.state===ee){const r=P;P=null,j(()=>ne(e,t[0]),!1),P=r}}function j(e,t){if(P)return e();let n=!1;t||(P=[]),$?n=!0:$=[],le++;try{const r=e();return dt(n),r}catch(r){n||($=null),P=null,ve(r)}}function dt(e){if(P&&(Be(P),P=null),e)return;const t=$;$=null,t.length&&j(()=>$e(t),!1)}function Be(e){for(let t=0;t<e.length;t++)te(e[t])}function gt(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:te(r)}for(t=0;t<n;t++)te(e[t])}function ne(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const o=r.state;o===T?r!==t&&(!r.updatedAt||r.updatedAt<le)&&te(r):o===ee&&ne(r,t)}}}function Ke(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=ee,n.pure?P.push(n):$.push(n),n.observers&&Ke(n))}}function H(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),o=n.observers;if(o&&o.length){const s=o.pop(),l=n.observerSlots.pop();r<o.length&&(s.sourceSlots[l]=r,o[r]=s,n.observerSlots[r]=l)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)H(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)H(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function pt(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function ve(e,t=p){throw pt(e)}function pe(e){if(typeof e=="function"&&!e.length)return pe(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=pe(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function yt(e,t){return function(r){let o;return V(()=>o=C(()=>(p.context={...p.context,[e]:r.value},Ae(()=>r.children))),void 0),o}}function k(e,t){return C(()=>e(t||{}))}function Q(){return!0}const mt={get(e,t,n){return t===x?n:e.get(t)},has(e,t){return t===x?!0:e.has(t)},set:Q,deleteProperty:Q,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:Q,deleteProperty:Q}},ownKeys(e){return e.keys()}};function fe(e){return(e=typeof e=="function"?e():e)?e:{}}function wt(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function bt(...e){let t=!1;for(let l=0;l<e.length;l++){const i=e[l];t=t||!!i&&x in i,e[l]=typeof i=="function"?(t=!0,v(i)):i}if(lt&&t)return new Proxy({get(l){for(let i=e.length-1;i>=0;i--){const u=fe(e[i])[l];if(u!==void 0)return u}},has(l){for(let i=e.length-1;i>=0;i--)if(l in fe(e[i]))return!0;return!1},keys(){const l=[];for(let i=0;i<e.length;i++)l.push(...Object.keys(fe(e[i])));return[...new Set(l)]}},mt);const n={},r=Object.create(null);for(let l=e.length-1;l>=0;l--){const i=e[l];if(!i)continue;const u=Object.getOwnPropertyNames(i);for(let c=u.length-1;c>=0;c--){const a=u[c];if(a==="__proto__"||a==="constructor")continue;const f=Object.getOwnPropertyDescriptor(i,a);if(!r[a])r[a]=f.get?{enumerable:!0,configurable:!0,get:wt.bind(n[a]=[f.get.bind(i)])}:f.value!==void 0?f:void 0;else{const g=n[a];g&&(f.get?g.push(f.get.bind(i)):f.value!==void 0&&g.push(()=>f.value))}}}const o={},s=Object.keys(r);for(let l=s.length-1;l>=0;l--){const i=s[l],u=r[i];u&&u.get?Object.defineProperty(o,i,u):o[i]=u?u.value:void 0}return o}const At=e=>`Stale read from <${e}>.`;function qe(e){const t=e.keyed,n=v(()=>e.when,void 0,void 0),r=t?n:v(n,void 0,{equals:(o,s)=>!o==!s});return v(()=>{const o=r();if(o){const s=e.children;return typeof s=="function"&&s.length>0?C(()=>s(t?o:()=>{if(!C(r))throw At("Show");return n()})):s}return e.fallback},void 0,void 0)}const St=e=>v(()=>e());function vt(e,t,n){let r=n.length,o=t.length,s=r,l=0,i=0,u=t[o-1].nextSibling,c=null;for(;l<o||i<s;){if(t[l]===n[i]){l++,i++;continue}for(;t[o-1]===n[s-1];)o--,s--;if(o===l){const a=s<r?i?n[i-1].nextSibling:n[s-i]:u;for(;i<s;)e.insertBefore(n[i++],a)}else if(s===i)for(;l<o;)(!c||!c.has(t[l]))&&t[l].remove(),l++;else if(t[l]===n[s-1]&&n[i]===t[o-1]){const a=t[--o].nextSibling;e.insertBefore(n[i++],t[l++].nextSibling),e.insertBefore(n[--s],a),t[o]=n[s]}else{if(!c){c=new Map;let f=i;for(;f<s;)c.set(n[f],f++)}const a=c.get(t[l]);if(a!=null)if(i<a&&a<s){let f=l,g=1,y;for(;++f<o&&f<s&&!((y=c.get(t[f]))==null||y!==a+g);)g++;if(g>a-i){const O=t[l];for(;i<a;)e.insertBefore(n[i++],O)}else e.replaceChild(n[i++],t[l++])}else l++;else t[l++].remove()}}}const xe="_$DX_DELEGATE";function sn(e,t,n,r={}){let o;return Te(s=>{o=s,t===document?e():Ot(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{o(),t.textContent=""}}function ln(e,t,n,r){let o;const s=()=>{const i=document.createElement("template");return i.innerHTML=e,i.content.firstChild},l=()=>(o||(o=s())).cloneNode(!0);return l.cloneNode=l,l}function Pt(e,t=window.document){const n=t[xe]||(t[xe]=new Set);for(let r=0,o=e.length;r<o;r++){const s=e[r];n.has(s)||(n.add(s),t.addEventListener(s,Et))}}function un(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function cn(e,t){t==null?e.removeAttribute("class"):e.className=t}function Ot(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return re(e,t,r,n);V(o=>re(e,t(),o,n),r)}function Et(e){let t=e.target;const n=`$$${e.type}`,r=e.target,o=e.currentTarget,s=u=>Object.defineProperty(e,"target",{configurable:!0,value:u}),l=()=>{const u=t[n];if(u&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?u.call(t,c,e):u.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&s(t.host),!0},i=()=>{for(;l()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const u=e.composedPath();s(u[0]);for(let c=0;c<u.length-2&&(t=u[c],!!l());c++){if(t._$host){t=t._$host,i();break}if(t.parentNode===o)break}}else i();s(r)}function re(e,t,n,r,o){for(;typeof n=="function";)n=n();if(t===n)return n;const s=typeof t,l=r!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,s==="string"||s==="number"){if(s==="number"&&(t=t.toString(),t===n))return n;if(l){let i=n[0];i&&i.nodeType===3?i.data!==t&&(i.data=t):i=document.createTextNode(t),n=B(e,n,r,i)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||s==="boolean")n=B(e,n,r);else{if(s==="function")return V(()=>{let i=t();for(;typeof i=="function";)i=i();n=re(e,i,n,r)}),()=>n;if(Array.isArray(t)){const i=[],u=n&&Array.isArray(n);if(ye(i,t,n,o))return V(()=>n=re(e,i,n,r,!0)),()=>n;if(i.length===0){if(n=B(e,n,r),l)return n}else u?n.length===0?Ce(e,i,r):vt(e,n,i):(n&&B(e),Ce(e,i));n=i}else if(t.nodeType){if(Array.isArray(n)){if(l)return n=B(e,n,r,t);B(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function ye(e,t,n,r){let o=!1;for(let s=0,l=t.length;s<l;s++){let i=t[s],u=n&&n[e.length],c;if(!(i==null||i===!0||i===!1))if((c=typeof i)=="object"&&i.nodeType)e.push(i);else if(Array.isArray(i))o=ye(e,i,u)||o;else if(c==="function")if(r){for(;typeof i=="function";)i=i();o=ye(e,Array.isArray(i)?i:[i],Array.isArray(u)?u:[u])||o}else e.push(i),o=!0;else{const a=String(i);u&&u.nodeType===3&&u.data===a?e.push(u):e.push(document.createTextNode(a))}}return o}function Ce(e,t,n=null){for(let r=0,o=t.length;r<o;r++)e.insertBefore(t[r],n)}function B(e,t,n,r){if(n===void 0)return e.textContent="";const o=r||document.createTextNode("");if(t.length){let s=!1;for(let l=t.length-1;l>=0;l--){const i=t[l];if(o!==i){const u=i.parentNode===e;!s&&!l?u?e.replaceChild(o,i):e.insertBefore(o,n):u&&i.remove()}else s=!0}}else e.insertBefore(o,n);return[o]}const Rt=!1;function We(){let e=new Set;function t(o){return e.add(o),()=>e.delete(o)}let n=!1;function r(o,s){if(n)return!(n=!1);const l={to:o,options:s,defaultPrevented:!1,preventDefault:()=>l.defaultPrevented=!0};for(const i of e)i.listener({...l,from:i.location,retry:u=>{u&&(n=!0),i.navigate(o,{...s,resolve:!1})}});return!l.defaultPrevented}return{subscribe:t,confirm:r}}let me;function Pe(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),me=window.history.state._depth}Pe();function xt(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function Ct(e,t){let n=!1;return()=>{const r=me;Pe();const o=r==null?null:me-r;if(n){n=!1;return}o&&t(o)?(n=!0,window.history.go(-o)):e()}}const Lt=/^(?:[a-z0-9]+:)?\/\//i,$t=/^\/+|(\/)\/+$/g,Me="http://sr";function M(e,t=!1){const n=e.replace($t,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function Z(e,t,n){if(Lt.test(t))return;const r=M(e),o=n&&M(n);let s="";return!o||t.startsWith("/")?s=r:o.toLowerCase().indexOf(r.toLowerCase())!==0?s=r+o:s=o,(s||"/")+M(t,!s)}function kt(e,t){return M(e).replace(/\/*(\*.*)?$/g,"")+M(t)}function Ve(e){const t={};return e.searchParams.forEach((n,r)=>{t[r]=n}),t}function Tt(e,t,n){const[r,o]=e.split("/*",2),s=r.split("/").filter(Boolean),l=s.length;return i=>{const u=i.split("/").filter(Boolean),c=u.length-l;if(c<0||c>0&&o===void 0&&!t)return null;const a={path:l?"":"/",params:{}},f=g=>n===void 0?void 0:n[g];for(let g=0;g<l;g++){const y=s[g],O=u[g],h=y[0]===":",d=h?y.slice(1):y;if(h&&he(O,f(d)))a.params[d]=O;else if(h||!he(O,y))return null;a.path+=`/${O}`}if(o){const g=c?u.slice(-c).join("/"):"";if(he(g,f(o)))a.params[o]=g;else return null}return a}}function he(e,t){const n=r=>r.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function jt(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((o,s)=>o+(s.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function He(e){const t=new Map,n=De();return new Proxy({},{get(r,o){return t.has(o)||Ne(n,()=>t.set(o,v(()=>e()[o]))),t.get(o)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function Xe(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const o=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)o.push(n+=t[1]),r=r.slice(t[0].length);return Xe(r).reduce((s,l)=>[...s,...o.map(i=>i+l)],[])}const _t=100,Dt=Ue(),Ge=Ue();function Nt(e,t=""){const{component:n,load:r,children:o,info:s}=e,l=!o||Array.isArray(o)&&!o.length,i={key:e,component:n,load:r,info:s};return Ye(e.path).reduce((u,c)=>{for(const a of Xe(c)){const f=kt(t,a);let g=l?f:f.split("/*",1)[0];g=g.split("/").map(y=>y.startsWith(":")||y.startsWith("*")?y:encodeURIComponent(y)).join("/"),u.push({...i,originalPath:c,pattern:g,matcher:Tt(g,!l,e.matchFilters)})}return u},[])}function Ut(e,t=0){return{routes:e,score:jt(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let o=e.length-1;o>=0;o--){const s=e[o],l=s.matcher(n);if(!l)return null;r.unshift({...l,route:s})}return r}}}function Ye(e){return Array.isArray(e)?e:[e]}function Je(e,t="",n=[],r=[]){const o=Ye(e);for(let s=0,l=o.length;s<l;s++){const i=o[s];if(i&&typeof i=="object"){i.hasOwnProperty("path")||(i.path="");const u=Nt(i,t);for(const c of u){n.push(c);const a=Array.isArray(i.children)&&i.children.length===0;if(i.children&&!a)Je(i.children,c.pattern,n,r);else{const f=Ut([...n],r.length);r.push(f)}n.pop()}}}return n.length?r:r.sort((s,l)=>l.score-s.score)}function de(e,t){for(let n=0,r=e.length;n<r;n++){const o=e[n].matcher(t);if(o)return o}return[]}function Ft(e,t){const n=new URL(Me),r=v(u=>{const c=e();try{return new URL(c,n)}catch{return console.error(`Invalid path ${c}`),u}},n,{equals:(u,c)=>u.href===c.href}),o=v(()=>r().pathname),s=v(()=>r().search,!0),l=v(()=>r().hash),i=()=>"";return{get pathname(){return o()},get search(){return s()},get hash(){return l()},get state(){return t()},get key(){return i()},query:He(be(s,()=>Ve(r())))}}let N;function It(){return N}function Bt(e,t,n,r={}){const{signal:[o,s],utils:l={}}=e,i=l.parsePath||(w=>w),u=l.renderPath||(w=>w),c=l.beforeLeave||We(),a=Z("",r.base||"");if(a===void 0)throw new Error(`${a} is not a valid base path`);a&&!o().value&&s({value:a,replace:!0,scroll:!1});const[f,g]=U(!1);let y;const O=(w,S)=>{S.value===h()&&S.state===A()||(y===void 0&&g(!0),N=w,y=S,at(()=>{y===S&&(d(y.value),m(y.state),_[1]([]))}).finally(()=>{y===S&&je(()=>{N=void 0,w==="navigate"&&ot(y),g(!1),y=void 0})}))},[h,d]=U(o().value),[A,m]=U(o().state),F=Ft(h,A),R=[],_=U([]),q=v(()=>typeof r.transformUrl=="function"?de(t(),r.transformUrl(F.pathname)):de(t(),F.pathname)),tt=He(()=>{const w=q(),S={};for(let E=0;E<w.length;E++)Object.assign(S,w[E].params);return S}),Oe={pattern:a,path:()=>a,outlet:()=>null,resolvePath(w){return Z(a,w)}};return V(be(o,w=>O("native",w),{defer:!0})),{base:Oe,location:F,params:tt,isRouting:f,renderPath:u,parsePath:i,navigatorFactory:rt,matches:q,beforeLeave:c,preloadRoute:st,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:_};function nt(w,S,E){C(()=>{if(typeof S=="number"){S&&(l.go?l.go(S):console.warn("Router integration does not support relative routing"));return}const{replace:ue,resolve:ce,scroll:I,state:J}={replace:!1,resolve:!0,scroll:!0,...E},D=ce?w.resolvePath(S):Z("",S);if(D===void 0)throw new Error(`Path '${S}' is not a routable path`);if(R.length>=_t)throw new Error("Too many redirects");const Ee=h();(D!==Ee||J!==A())&&(Rt||c.confirm(D,E)&&(R.push({value:Ee,replace:ue,scroll:I,state:A()}),O("navigate",{value:D,state:J})))})}function rt(w){return w=w||ft(Ge)||Oe,(S,E)=>nt(w,S,E)}function ot(w){const S=R[0];S&&(s({...w,replace:S.replace,scroll:S.scroll}),R.length=0)}function st(w,S={}){const E=de(t(),w.pathname),ue=N;N="preload";for(let ce in E){const{route:I,params:J}=E[ce];I.component&&I.component.preload&&I.component.preload();const{load:D}=I;S.preloadData&&D&&Ne(n(),()=>D({params:J,location:{pathname:w.pathname,search:w.search,hash:w.hash,query:Ve(w),state:null,key:""},intent:"preload"}))}N=ue}}function Kt(e,t,n,r){const{base:o,location:s,params:l}=e,{pattern:i,component:u,load:c}=r().route,a=v(()=>r().path);u&&u.preload&&u.preload();const f=c?c({params:l,location:s,intent:N||"initial"}):void 0;return{parent:t,pattern:i,path:a,outlet:()=>u?k(u,{params:l,location:s,data:f,get children(){return n()}}):n(),resolvePath(y){return Z(o.path(),y,a())}}}const qt=e=>t=>{const{base:n}=t,r=Ae(()=>t.children),o=v(()=>Je(r(),t.base||""));let s;const l=Bt(e,o,()=>s,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(l),k(Dt.Provider,{value:l,get children(){return k(Wt,{routerState:l,get root(){return t.root},get load(){return t.rootLoad},get children(){return[St(()=>(s=De())&&null),k(Mt,{routerState:l,get branches(){return o()}})]}})}})};function Wt(e){const t=e.routerState.location,n=e.routerState.params,r=v(()=>e.load&&C(()=>{e.load({params:n,location:t,intent:It()||"initial"})}));return k(qe,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:o=>k(o,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function Mt(e){const t=[];let n;const r=v(be(e.routerState.matches,(o,s,l)=>{let i=s&&o.length===s.length;const u=[];for(let c=0,a=o.length;c<a;c++){const f=s&&s[c],g=o[c];l&&f&&g.route.key===f.route.key?u[c]=l[c]:(i=!1,t[c]&&t[c](),Te(y=>{t[c]=y,u[c]=Kt(e.routerState,u[c-1]||e.routerState.base,Le(()=>r()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(o.length).forEach(c=>c()),l&&i?l:(n=u[0],u)}));return Le(()=>r()&&n)()}const Le=e=>()=>k(qe,{get when(){return e()},keyed:!0,children:t=>k(Ge.Provider,{value:t,get children(){return t.outlet()}})}),an=e=>{const t=Ae(()=>e.children);return bt(e,{get children(){return t()}})};function Vt([e,t],n,r){return[e,r?o=>t(r(o)):t]}function Ht(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function Xt(e){let t=!1;const n=o=>typeof o=="string"?{value:o}:o,r=Vt(U(n(e.get()),{equals:(o,s)=>o.value===s.value&&o.state===s.state}),void 0,o=>(!t&&e.set(o),o));return e.init&&_e(e.init((o=e.get())=>{t=!0,r[1](n(o)),t=!1})),qt({signal:r,create:e.create,utils:e.utils})}function Gt(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function Yt(e,t){const n=Ht(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const Jt=new Map;function Qt(e=!0,t=!1,n="/_server",r){return o=>{const s=o.base.path(),l=o.navigatorFactory(o.base);let i={};function u(h){return h.namespaceURI==="http://www.w3.org/2000/svg"}function c(h){if(h.defaultPrevented||h.button!==0||h.metaKey||h.altKey||h.ctrlKey||h.shiftKey)return;const d=h.composedPath().find(q=>q instanceof Node&&q.nodeName.toUpperCase()==="A");if(!d||t&&!d.hasAttribute("link"))return;const A=u(d),m=A?d.href.baseVal:d.href;if((A?d.target.baseVal:d.target)||!m&&!d.hasAttribute("state"))return;const R=(d.getAttribute("rel")||"").split(/\s+/);if(d.hasAttribute("download")||R&&R.includes("external"))return;const _=A?new URL(m,document.baseURI):new URL(m);if(!(_.origin!==window.location.origin||s&&_.pathname&&!_.pathname.toLowerCase().startsWith(s.toLowerCase())))return[d,_]}function a(h){const d=c(h);if(!d)return;const[A,m]=d,F=o.parsePath(m.pathname+m.search+m.hash),R=A.getAttribute("state");h.preventDefault(),l(F,{resolve:!1,replace:A.hasAttribute("replace"),scroll:!A.hasAttribute("noscroll"),state:R&&JSON.parse(R)})}function f(h){const d=c(h);if(!d)return;const[A,m]=d;typeof r=="function"&&(m.pathname=r(m.pathname)),i[m.pathname]||o.preloadRoute(m,{preloadData:A.getAttribute("preload")!=="false"})}function g(h){const d=c(h);if(!d)return;const[A,m]=d;typeof r=="function"&&(m.pathname=r(m.pathname)),!i[m.pathname]&&(i[m.pathname]=setTimeout(()=>{o.preloadRoute(m,{preloadData:A.getAttribute("preload")!=="false"}),delete i[m.pathname]},200))}function y(h){const d=c(h);if(!d)return;const[,A]=d;typeof r=="function"&&(A.pathname=r(A.pathname)),i[A.pathname]&&(clearTimeout(i[A.pathname]),delete i[A.pathname])}function O(h){if(h.defaultPrevented)return;let d=h.submitter&&h.submitter.hasAttribute("formaction")?h.submitter.getAttribute("formaction"):h.target.getAttribute("action");if(!d)return;if(!d.startsWith("https://action/")){const m=new URL(d,Me);if(d=o.parsePath(m.pathname+m.search),!d.startsWith(n))return}if(h.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const A=Jt.get(d);if(A){h.preventDefault();const m=new FormData(h.target);h.submitter&&h.submitter.name&&m.append(h.submitter.name,h.submitter.value),A.call({r:o,f:h.target},m)}}Pt(["click","submit"]),document.addEventListener("click",a),e&&(document.addEventListener("mouseover",g),document.addEventListener("mouseout",y),document.addEventListener("focusin",f),document.addEventListener("touchstart",f)),document.addEventListener("submit",O),_e(()=>{document.removeEventListener("click",a),e&&(document.removeEventListener("mouseover",g),document.removeEventListener("mouseout",y),document.removeEventListener("focusin",f),document.removeEventListener("touchstart",f)),document.removeEventListener("submit",O)})}}function fn(e){const t=()=>{const r=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(r)+window.location.hash:r+window.location.hash,state:window.history.state}},n=We();return Xt({get:t,set({value:r,replace:o,scroll:s,state:l}){o?window.history.replaceState(xt(l),"",r):window.history.pushState(l,"",r),Yt(decodeURIComponent(window.location.hash.slice(1)),s),Pe()},init:r=>Gt(window,"popstate",Ct(r,o=>{if(o&&o<0)return!n.confirm(o);{const s=t();return!n.confirm(s.value,{state:s.state})}})),create:Qt(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}const we=Symbol("store-raw"),K=Symbol("store-node"),L=Symbol("store-has"),Qe=Symbol("store-self");function Ze(e){let t=e[x];if(!t&&(Object.defineProperty(e,x,{value:t=new Proxy(e,en)}),!Array.isArray(e))){const n=Object.keys(e),r=Object.getOwnPropertyDescriptors(e);for(let o=0,s=n.length;o<s;o++){const l=n[o];r[l].get&&Object.defineProperty(e,l,{enumerable:r[l].enumerable,get:r[l].get.bind(t)})}}return t}function oe(e){let t;return e!=null&&typeof e=="object"&&(e[x]||!(t=Object.getPrototypeOf(e))||t===Object.prototype||Array.isArray(e))}function X(e,t=new Set){let n,r,o,s;if(n=e!=null&&e[we])return n;if(!oe(e)||t.has(e))return e;if(Array.isArray(e)){Object.isFrozen(e)?e=e.slice(0):t.add(e);for(let l=0,i=e.length;l<i;l++)o=e[l],(r=X(o,t))!==o&&(e[l]=r)}else{Object.isFrozen(e)?e=Object.assign({},e):t.add(e);const l=Object.keys(e),i=Object.getOwnPropertyDescriptors(e);for(let u=0,c=l.length;u<c;u++)s=l[u],!i[s].get&&(o=e[s],(r=X(o,t))!==o&&(e[s]=r))}return e}function se(e,t){let n=e[t];return n||Object.defineProperty(e,t,{value:n=Object.create(null)}),n}function G(e,t,n){if(e[t])return e[t];const[r,o]=U(n,{equals:!1,internal:!0});return r.$=o,e[t]=r}function Zt(e,t){const n=Reflect.getOwnPropertyDescriptor(e,t);return!n||n.get||!n.configurable||t===x||t===K||(delete n.value,delete n.writable,n.get=()=>e[x][t]),n}function ze(e){ge()&&G(se(e,K),Qe)()}function zt(e){return ze(e),Reflect.ownKeys(e)}const en={get(e,t,n){if(t===we)return e;if(t===x)return n;if(t===Re)return ze(e),n;const r=se(e,K),o=r[t];let s=o?o():e[t];if(t===K||t===L||t==="__proto__")return s;if(!o){const l=Object.getOwnPropertyDescriptor(e,t);ge()&&(typeof s!="function"||e.hasOwnProperty(t))&&!(l&&l.get)&&(s=G(r,t,s)())}return oe(s)?Ze(s):s},has(e,t){return t===we||t===x||t===Re||t===K||t===L||t==="__proto__"?!0:(ge()&&G(se(e,L),t)(),t in e)},set(){return!0},deleteProperty(){return!0},ownKeys:zt,getOwnPropertyDescriptor:Zt};function ie(e,t,n,r=!1){if(!r&&e[t]===n)return;const o=e[t],s=e.length;n===void 0?(delete e[t],e[L]&&e[L][t]&&o!==void 0&&e[L][t].$()):(e[t]=n,e[L]&&e[L][t]&&o===void 0&&e[L][t].$());let l=se(e,K),i;if((i=G(l,t,o))&&i.$(()=>n),Array.isArray(e)&&e.length!==s){for(let u=e.length;u<s;u++)(i=l[u])&&i.$();(i=G(l,"length",s))&&i.$(e.length)}(i=l[Qe])&&i.$()}function et(e,t){const n=Object.keys(t);for(let r=0;r<n.length;r+=1){const o=n[r];ie(e,o,t[o])}}function tn(e,t){if(typeof t=="function"&&(t=t(e)),t=X(t),Array.isArray(t)){if(e===t)return;let n=0,r=t.length;for(;n<r;n++){const o=t[n];e[n]!==o&&ie(e,n,o)}ie(e,"length",r)}else et(e,t)}function W(e,t,n=[]){let r,o=e;if(t.length>1){r=t.shift();const l=typeof r,i=Array.isArray(e);if(Array.isArray(r)){for(let u=0;u<r.length;u++)W(e,[r[u]].concat(t),n);return}else if(i&&l==="function"){for(let u=0;u<e.length;u++)r(e[u],u)&&W(e,[u].concat(t),n);return}else if(i&&l==="object"){const{from:u=0,to:c=e.length-1,by:a=1}=r;for(let f=u;f<=c;f+=a)W(e,[f].concat(t),n);return}else if(t.length>1){W(e[r],t,[r].concat(n));return}o=e[r],n=[r].concat(n)}let s=t[0];typeof s=="function"&&(s=s(o,n),s===o)||r===void 0&&s==null||(s=X(s),r===void 0||oe(o)&&oe(s)&&!Array.isArray(s)?et(o,s):ie(e,r,s))}function hn(...[e,t]){const n=X(e||{}),r=Array.isArray(n),o=Ze(n);function s(...l){je(()=>{r&&l.length===1?tn(n,l[0]):W(n,l)})}return[o,s]}export{an as R,v as a,k as b,hn as c,fn as d,V as e,cn as f,U as g,Ot as i,nn as o,sn as r,un as s,ln as t};
