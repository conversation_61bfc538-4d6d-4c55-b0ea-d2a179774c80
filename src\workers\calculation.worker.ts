/**
 * 计算密集型任务Worker
 * 用于处理技术指标计算、数据分析等CPU密集型任务
 */

// 消息类型
export interface WorkerMessage {
  id: string;
  type: calculate | indicator' | analysis' | backtest;
  data: any;
  timestamp: number;
}

export interface WorkerResponse {
  id: string;
  type: string;
  data: any;
  error?: string;
  timestamp: number;
  duration: number;
}

// K线数据结构
interface KLineData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// 技术指标计算函数

/**
 * 简单移动平均线 (SMA)
 */
function calculateSMA(data: number[], period: number): number[] {
  const result: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN);
    } else {
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += data[i - j];
      }
      result.push(sum / period);
    }
  }
  
  return result;
}

/**
 * 指数移动平均线 (EMA)
 */
function calculateEMA(data: number[], period: number): number[] {
  const result: number[] = [];
  const multiplier = 2 / (period + 1);
  let ema = data[0];
  
  result.push(ema);
  
  for (let i = 1; i < data.length; i++) {
    ema = (data[i] - ema) * multiplier + ema;
    result.push(ema);
  }
  
  return result;
}

/**
 * 布林带 (Bollinger Bands)
 */
function calculateBollingerBands(data: number[], period: number, stdDev: number = 2) {
  const sma = calculateSMA(data, period);
  const upper: number[] = [];
  const lower: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upper.push(NaN);
      lower.push(NaN);
    } else {
      let sum = 0;
      for (let j = 0; j < period; j++) {
        const diff = data[i - j] - sma[i];
        sum += diff * diff;
      }
      const standardDeviation = Math.sqrt(sum / period);
      
      upper.push(sma[i] + standardDeviation * stdDev);
      lower.push(sma[i] - standardDeviation * stdDev);
    }
  }
  
  return { upper, middle: sma, lower };
}

/**
 * 相对强弱指数 (RSI)
 */
function calculateRSI(data: number[], period: number = 14): number[] {
  const result: number[] = [];
  const gains: number[] = [];
  const losses: number[] = [];
  
  // 计算价格变化
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }
  
  result.push(NaN); // 第一个值无法计算
  
  // 计算初始平均涨幅和跌幅
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
  
  for (let i = period; i < data.length; i++) {
    if (i === period) {
      const rs = avgGain / avgLoss;
      result.push(100 - (100 / (1 + rs)));
    } else {
      avgGain = (avgGain * (period - 1) + gains[i - 1]) / period;
      avgLoss = (avgLoss * (period - 1) + losses[i - 1]) / period;
      const rs = avgGain / avgLoss;
      result.push(100 - (100 / (1 + rs)));
    }
  }
  
  return result;
}

/**
 * MACD指标
 */
function calculateMACD(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);
  const macd: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    macd.push(fastEMA[i] - slowEMA[i]);
  }
  
  const signal = calculateEMA(macd, signalPeriod);
  const histogram: number[] = [];
  
  for (let i = 0; i < macd.length; i++) {
    histogram.push(macd[i] - signal[i]);
  }
  
  return { macd, signal, histogram };
}

/**
 * 成交量加权平均价格 (VWAP)
 */
function calculateVWAP(klineData: KLineData[]): number[] {
  const result: number[] = [];
  let cumulativeVolume = 0;
  let cumulativeTypicalPriceVolume = 0;
  
  for (const item of klineData) {
    const typicalPrice = (item.high + item.low + item.close) / 3;
    cumulativeTypicalPriceVolume += typicalPrice * item.volume;
    cumulativeVolume += item.volume;
    
    result.push(cumulativeTypicalPriceVolume / cumulativeVolume);
  }
  
  return result;
}

/**
 * 随机指标 (Stochastic)
 */
function calculateStochastic(klineData: KLineData[], kPeriod: number = 14, dPeriod: number = 3): { k: number[], d: number[] } {
  const k: number[] = [];
  
  for (let i = 0; i < klineData.length; i++) {
    if (i < kPeriod - 1) {
      k.push(NaN);
    } else {
      const period = klineData.slice(i - kPeriod + 1, i + 1);
      const highest = Math.max(...period.map(item => item.high));
      const lowest = Math.min(...period.map(item => item.low));
      const currentClose = klineData[i].close;
      
      const kValue = ((currentClose - lowest) / (highest - lowest)) * 100;
      k.push(kValue);
    }
  }
  
  const d = calculateSMA(k.filter(val => !isNaN(val)), dPeriod);
  
  return { k, d };
}

/**
 * 回测计算
 */
function runBacktest(data: {
  klineData: KLineData[];
  strategy: string;
  parameters: any;
  initialCapital: number;
}): any {
  const { klineData, parameters, initialCapital } = data;
  const trades: any[] = [];
  let capital = initialCapital;
  let position = 0;
  let totalReturn = 0;
  let maxDrawdown = 0;
  let peakCapital = initialCapital;
  
  // 计算技术指标
  const prices = klineData.map(item => item.close);
  const sma20 = calculateSMA(prices, parameters.shortPeriod || 20);
  const sma50 = calculateSMA(prices, parameters.longPeriod || 50);
  
  // 简单的均线交叉策略
  for (let i = 1; i < klineData.length; i++) {
    const currentPrice = prices[i];
    const prevSMA20 = sma20[i - 1];
    const currentSMA20 = sma20[i];
    const prevSMA50 = sma50[i - 1];
    const currentSMA50 = sma50[i];
    
    // 买入信号：短期均线上穿长期均线
    if (
      position === 0 &&
      !isNaN(prevSMA20) && !isNaN(currentSMA20) &&
      !isNaN(prevSMA50) && !isNaN(currentSMA50) &&
      prevSMA20 <= prevSMA50 && currentSMA20 > currentSMA50
    ) {
      position = Math.floor(capital / currentPrice);
      capital -= position * currentPrice;
      
      trades.push({ 
        type: buy,
        time: klineData[i].time,
        price: currentPrice,
        quantity: position,
        capital: capital + position * currentPrice,
      });
    }
    
    // 卖出信号：短期均线下穿长期均线
    if (
      position > 0 &&
      !isNaN(prevSMA20) && !isNaN(currentSMA20) &&
      !isNaN(prevSMA50) && !isNaN(currentSMA50) &&
      prevSMA20 >= prevSMA50 && currentSMA20 < currentSMA50
    ) {
      capital += position * currentPrice;
      
      trades.push({ 
        type: sell,
        time: klineData[i].time,
        price: currentPrice,
        quantity: position,
        capital,
      });
      
      position = 0;
    }
    
    // 更新统计数据
    const currentCapital = capital + position * currentPrice;
    if (currentCapital > peakCapital) {
      peakCapital = currentCapital;
    }
    
    const drawdown = (peakCapital - currentCapital) / peakCapital;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }
  
  // 计算最终结果
  const finalPrice = prices[prices.length - 1];
  const finalCapital = capital + position * finalPrice;
  totalReturn = (finalCapital - initialCapital) / initialCapital;
  
  return {
    trades,
    initialCapital,
    finalCapital,
    totalReturn,
    maxDrawdown,
    totalTrades: trades.length,
    winRate: trades.length > 0 ? trades.filter(t => t.type === sell).length / (trades.length / 2) : 0,
  };
}

// Worker消息处理
self.onmessage = function(event: MessageEvent<WorkerMessage>) {
  const { id, type, data } = event.data;
  const startTime = performance.now();
  
  try {
    let result: any;
    
    switch (type) {
      case indicator:'
        const { indicatorType, data: priceData, parameters } = data;
        ''''
        switch (indicatorType) {'''''
          case SMA': ''
            result = calculateSMA(priceData, parameters.period);
            break;
          case EMA': ''
            result = calculateEMA(priceData, parameters.period);
            break;
          case RSI': ''
            result = calculateRSI(priceData, parameters.period);
            break;
          case MACD': ''
            result = calculateMACD(priceData, parameters.fastPeriod, parameters.slowPeriod, parameters.signalPeriod);
            break;
          case BB': ''
            result = calculateBollingerBands(priceData, parameters.period, parameters.stdDev);
            break;
          case VWAP: result = calculateVWAP(data.klineData);
            break;
          case STOCH': '
            result = calculateStochastic(data.klineData, parameters.kPeriod, parameters.dPeriod);
            break;
          default:
            throw new Error(`未知的指标类型: ${indicatorType}`);
        }
        break;
        ``
      case backtest: `result = runBacktest(data);
        break;
        `
      case analysis: `
        // 数据分析任务
        result = {
          mean: data.reduce((a: number, b: number) => a + b, 0) / data.length,
          max: Math.max(...data),
          min: Math.min(...data),
          volatility: calculateVolatility(data),
        };
        break;
        
      case calculate:
        // 通用计算任务
        result = eval(data.expression);
        break;
        
      default:
        throw new Error(`未知的任务类型: ${type}`);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    const response: WorkerResponse = {
      id,
      type,
      data: result,
      timestamp: Date.now(),
      duration,
    };
    
    self.postMessage(response);
    
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    const response: WorkerResponse = {
      id,
      type,
      data: null,
      error: error instanceof Error ? error.message : String(error),
      timestamp: Date.now(),
      duration,
    };
    
    self.postMessage(response);
  }
};

// 辅助函数
function calculateVolatility(data: number[]): number {
  const mean = data.reduce((a, b) => a + b, 0) / data.length;
  const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length;
  return Math.sqrt(variance);
}
`
// 导出类型供主线程使用''`
export type { WorkerMessage as CalcWorkerMessage, WorkerResponse as CalcWorkerResponse };