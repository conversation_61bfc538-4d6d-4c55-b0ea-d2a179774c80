/**
 * 计算Worker - 最小实现
 * 临时简化版本，避免构建错误
 */

export interface WorkerMessage {
  id: string
  type: 'calculate_indicators' | 'optimize_parameters' | 'process_data'
  data: any
}

export interface WorkerResponse {
  id: string
  type: 'result' | 'error' | 'progress'
  success: boolean
  data?: any
  error?: string
  timestamp: number
}

// 简化的指标计算
function calculateIndicators(data: any): any {
  const prices = data?.prices || []
  const period = data?.period || 20
  
  // 模拟SMA计算
  const sma = prices.map((price: number, index: number) => {
    if (index < period - 1) return null
    const sum = prices.slice(index - period + 1, index + 1).reduce((a: number, b: number) => a + b, 0)
    return sum / period
  })
  
  // 模拟EMA计算
  const ema = prices.map((price: number, index: number) => {
    if (index === 0) return price
    const multiplier = 2 / (period + 1)
    return price * multiplier + (ema[index - 1] || price) * (1 - multiplier)
  })
  
  // 模拟RSI计算
  const rsi = prices.map((price: number, index: number) => {
    return 50 + Math.random() * 40 - 20 // 模拟RSI值 (30-70)
  })
  
  return {
    sma: sma.filter(v => v !== null),
    ema,
    rsi,
    macd: {
      macd: new Array(prices.length).fill(0).map(() => Math.random() * 2 - 1),
      signal: new Array(prices.length).fill(0).map(() => Math.random() * 2 - 1),
      histogram: new Array(prices.length).fill(0).map(() => Math.random() * 1 - 0.5)
    }
  }
}

// 简化的参数优化
function optimizeParameters(data: any): any {
  const iterations = Math.min(data?.iterations || 100, 50) // 限制迭代次数
  const results = []
  
  for (let i = 0; i < iterations; i++) {
    results.push({
      params: {
        param1: Math.random() * 50,
        param2: Math.random() * 100,
        param3: Math.random() * 20
      },
      return: Math.random() * 0.5 - 0.1, // -10% to 40%
      sharpe: Math.random() * 3,
      maxDrawdown: Math.random() * 0.3
    })
  }
  
  // 找到最佳结果
  const best = results.reduce((best, current) => 
    current.return > best.return ? current : best
  )
  
  return {
    bestParams: best.params,
    bestReturn: best.return,
    results: results.slice(0, 20), // 只返回前20个结果
    totalIterations: iterations
  }
}

// Worker 主逻辑
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { id, type, data } = event.data
  
  try {
    let result: any
    
    switch (type) {
      case 'calculate_indicators':
        result = calculateIndicators(data)
        break
        
      case 'optimize_parameters':
        result = optimizeParameters(data)
        break
        
      case 'process_data':
        result = { processed: true, input: data, timestamp: Date.now() }
        break
        
      default:
        throw new Error(`Unknown task type: ${type}`)
    }
    
    self.postMessage({
      id,
      type: 'result',
      success: true,
      data: result,
      timestamp: Date.now()
    } as WorkerResponse)
    
  } catch (error) {
    self.postMessage({
      id,
      type: 'error',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now()
    } as WorkerResponse)
  }
}

// 导出类型
export type { WorkerMessage as CalculationWorkerMessage, WorkerResponse as CalculationWorkerResponse }