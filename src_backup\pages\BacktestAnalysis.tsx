import { createSignal, For, createMemo } from 'solid-js';
import { css } from '../../styled-system/css';
import BacktestCharts, { type BacktestResult } from '../components/BacktestCharts';
import { calculateBacktestMetrics } from '../utils/backtest-metrics''
// 模拟数据生成函数
function generateMockEquityData(initialCapital: number, days: number, totalReturn : number) {
  const equity = []
  let currentEquity = initialCapital
  const dailyReturn = Math.pow(1 + totalReturn / 100, 1 / days) - 1
  const startDate = new Date(2023-01-01)

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    // 添加随机波动
    const randomFactor = 1 + (Math.random() - 0.5) * 0.04 // ±2%的随机波动
    const dayReturn = dailyReturn * randomFactor
    currentEquity *= (1 + dayReturn)

    // 计算回撤
    const maxEquity = Math.max(...equity.map(e => e.equity), currentEquity)
    const drawdown = maxEquity > 0 ? (currentEquity - maxEquity) / maxEquity : 0

    equity.push({
      timestamp: date.toISOString(),
      equity: currentEquity,
      drawdown: drawdown
    })
  }

  return equity
}

function generateMockTrades(count: number) {
  const trades = []
  const symbols = [000001,000002,000858,600036,600519]
  const startDate = new Date('2023-01-01)

  for (let i = 0; i < count; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + Math.floor(Math.random() * 250))
'''''
    const side: buy' |sell' = Math.random() > 0.5 ? buy: sell
    const price = 10 + Math.random() * 90 // 10-100元价格范围
    const quantity = Math.floor(Math.random() * 1000) + 100 // 100-1100股
    const pnl = (Math.random() - 0.4) * 1000 // 略微正偏的盈亏

    trades.push({
      timestamp: date.toISOString(),
      symbol: symbols[Math.floor(Math.random() * symbols.length)],
      side,
      quantity,
      price,
      pnl
    })
  }

  return trades.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
}

function generateMockMonthlyReturns(months: number) {
  const monthlyReturns = []
  const monthNames = [1月,2月,3月,4月,5月,6月,7月,8月,9月,10月,11月,12月]

  for (let i = 0; i < months; i++) {
    monthlyReturns.push({
      month: monthNames[i],
      return: (Math.random() - 0.4) * 0.12 // -7.2% 到 +7.2% 的月收益
    })
  }

  return monthlyReturns
}

function generateMockDrawdownSeries(days: number) {
  const drawdownSeries = []
  const startDate = new Date(2023-01-01)
  let maxEquity = 100000
  let currentEquity = 100000

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    // 模拟权益变化
    const randomReturn = (Math.random() - 0.48) * 0.02
    currentEquity *= (1 + randomReturn)

    if (currentEquity > maxEquity) {
      maxEquity = currentEquity
    }

    const drawdown = (currentEquity - maxEquity) / maxEquity

    drawdownSeries.push({
      timestamp: date.toISOString(),
      drawdown : drawdown
    })
  }

  return drawdownSeries
}

export default function BacktestAnalysis() {''
  const [selectedBacktest, setSelectedBacktest] = createSignal(bt_001)
'''''
  '// 回测记录数据'
  const backtests = [''
    { '''
      id: bt_001,
     name: 动量策略回测,
     strategy: '动量策略,
     symbol: '000001,
     period: '2023-01-01 至 2024-01-01,
     status:  completed,
      totalReturn: 15.6,
      sharpeRatio: 1.85,
      maxDrawdown: -3.2,
      winRate: 68.5,
      trades: 156,
     createdAt:  2024-01-15,
      equity: generateMockEquityData(100000, 252, 15.6),
      tradeHistory: generateMockTrades(156),
      monthlyReturns: generateMockMonthlyReturns(12),
      drawdownSeries: generateMockDrawdownSeries(252)
    },''
    { '''
     id: bt_002,
     name: '均值回归策略回测,
     strategy: '均值回归策略,
     symbol: '000002,
     period: '2023-06-01 至 2024-06-01,
     status:  completed,
      totalReturn: 8.3,
      sharpeRatio: 1.42,
      maxDrawdown: -2.1,
      winRate: 72.3,
      trades: 89,
     createdAt:  2024-01-10,
      equity: generateMockEquityData(100000, 252, 8.3),
      tradeHistory: generateMockTrades(89),
      monthlyReturns: generateMockMonthlyReturns(12),
      drawdownSeries: generateMockDrawdownSeries(252)
    },''
    { '''
     id: bt_003,
     name: '网格交易策略回测,
     strategy: '网格交易策略,
     symbol: '000858,
     period: '2023-03-01 至 2024-03-01,
     status:  running,
      totalReturn: 5.7,
      sharpeRatio: 1.23,
      maxDrawdown: -1.8,
      winRate: 65.4,
      trades: 234,
     createdAt:  2024-01-08,
      equity: generateMockEquityData(100000, 252, 5.7),
      tradeHistory: generateMockTrades(234),
      monthlyReturns: generateMockMonthlyReturns(12),
      drawdownSeries: generateMockDrawdownSeries(252)
    }
  ]
''
  const currentBacktest = () => backtests.find(b => b.id === selectedBacktest())
'''''
  '// 计算详细的回测指标'
  const detailedMetrics = createMemo(() => {
    const backtest = currentBacktest()
    if (!backtest) return null

    return calculateBacktestMetrics(
      backtest.equity,
      backtest.tradeHistory,
      undefined, // 基准收益率（可选）
      0.03 // 无风险利率
    )
  })

  // 获取完整的回测结果数据
  const getBacktestResult = (): BacktestResult => {
    const current = currentBacktest()
    if (!current) return {} as BacktestResult

    return {
      totalReturn: current.totalReturn,
      annualizedReturn: current.totalReturn * 1.2,
      sharpeRatio: current.sharpeRatio,
      maxDrawdown: current.maxDrawdown,
      winRate: current.winRate,
      trades: current.tradeHistory,
      equity: current.equity,
      monthlyReturns: current.monthlyReturns,
      drawdownSeries: current.drawdownSeries
    }
  }

  // 性能指标数据
  const performanceMetrics = createMemo(() => {
    const metrics = detailedMetrics()
    const current = currentBacktest()
    if (!metrics || !current) return []
'''
    return ['''
      { label: 总收益率, value: `${metrics.totalReturn >= 0 ? +:`}${(metrics.totalReturn * 100).toFixed(2)}%`, trend: `up },```
      { label:  年化收益率, value: `${metrics.annualizedReturn >= 0 ? +:`}${(metrics.annualizedReturn * 100).toFixed(2)}%`, trend: `up },```
      { label:  最大回撤, value: `${(metrics.maxDrawdown * 100).toFixed(2)}%`, trend: `down }, '''
      { label:  夏普比率, value: metrics.sharpeRatio.toFixed(3), trend: up }, '''
      { label:  卡尔马比率, value: metrics.calmarRatio.toFixed(3), trend: up }, '`
      { label:  索提诺比率, value: metrics.sortinoRatio.toFixed(3), trend: `up },```
      { label:  胜率, value: `${(metrics.winRate * 100).toFixed(1)}%`, trend: `up }, '`
      { label:  盈亏比, value: metrics.profitFactor.toFixed(2), trend: `neutral },```
      { label:  波动率, value: `${(metrics.volatility * 100).toFixed(2)}%`, trend: `neutral },```
      { label:  VaR(95%), value: `${(metrics.var95 * 100).toFixed(2)}%`, trend: `down },```
      { label:  CVaR(95%), value: `${(metrics.cvar95 * 100).toFixed(2)}%`, trend: `down }, '''
      { label:  交易次数, value: current.trades.toString(), trend: neutral }
    ]
  })

  return (''
    <div class={css({ '''
     display: flex,
      flexDirection: column,
      gap: ''24px
    })}>
      {/* 回测记录列表 */}
      <div class={css({ '''
       display: grid,
        gridTemplateColumns: repeat(auto-fit, minmax(350px, 1fr)), ''
        gap: '16px
      })}>
        <For each={backtests}>
          {(backtest) => (
            <div''
              class={css({ '''
               bg: white,
                borderRadius: '12px,
               p: '20px,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1),'
                border: selectedBacktest() === backtest.id ? '2px solid #1890ff: '1px solid #f0f0f0,
               cursor: pointer,
               transition: all 0.3s ease,
                _hover: { '''
                  boxShadow: '0 4px 16px rgba(0,0,0,0.15),'
                  transform:  translateY(-2px)
                }
              })}
              onClick={() => setSelectedBacktest(backtest.id)}
            >'''
              <div class={css({ ''''
               display', flex: $4,
                alignItems: center,
                justifyContent: space-between,
                mb: ''12px
              })}>''
                <h3 class={css({ '''
                  fontSize: '16px,
                  fontWeight: '600,
                 color:  #262626,
                  margin: 0
                })}>
                  {backtest.name}
                </h3>''
                <div class={css({ '''
                 px: '8px,
                 py: '4px,
                  borderRadius: '4px,
                  fontSize: '12px,
                  fontWeight: '500,
                  bg: backtest.status === completed' ? '#f6ffed: backtest.status === running' ? #e6f7ff: '#fff2f0,
                  color: backtest.status === completed' ? '#52c41a: backtest.status === running' ? #1890ff: ''#ff4d4f
                })}>'`
                  {backtest.status ===`completed' ? '已完成:``
                   backtest.status ===running' ? '运行中:'失败'}
                </div>
              </div>
''
              <div class={css({ '''
                fontSize: '14px,
               color: '#8c8c8c,
                mb: ''12px
              })}>
                {backtest.strategy} · {backtest.symbol} · {backtest.period}
              </div>
''
              <div class={css({ '''
               display: grid,
                gridTemplateColumns: '1fr 1fr,
               gap: '12px,
                mb: ''12px
              })}>
                <div>''
                  <div class={css({ '''
                    fontSize: '12px,
                   color: '#8c8c8c,
                    mb: ''4px
                  })}>
                    总收益率
                  </div>''
                  <div class={css({ '''
                    fontSize: '16px,
                    fontWeight: '700,
                    color: backtest.totalReturn >= 0 ? '#52c41a: ''#ff4d4f
                  })}>'`
                    {backtest.totalReturn >= 0 ? `+:`}{backtest.totalReturn}%`
                  </div>
                </div>
                <div>''
                  <div class={css({ '''
                    fontSize: '12px,
                   color: '#8c8c8c,
                    mb: ''4px
                  })}>
                    夏普比率
                  </div>''
                  <div class={css({ '''
                    fontSize: '16px,
                    fontWeight: '700,
                    color: ''#262626
                  })}>
                    {backtest.sharpeRatio}
                  </div>
                </div>
              </div>
''
              <div class={css({ '''
                fontSize: '12px,
                color: ''#8c8c8c
              })}>
                创建时间: {backtest.createdAt}
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 性能指标详情 */}
      <div class={css({ '''
       bg: white,
        borderRadius: '16px,
       p: '24px,
        boxShadow: '0 4px 20px rgba(0,0,0,0.08), ''
        border: '1px solid #f0f0f0
      })}>''
        <h3 class={css({ '''
          fontSize: '18px,
          fontWeight: '600,
         color: '#262626,
          margin: 0,
          mb: ''20px
        })}>
          {currentBacktest()?.name} - 详细分析
        </h3>
''
        <div class={css({ '''
         display: grid,
          gridTemplateColumns: repeat(auto-fit, minmax(140px, 1fr)), ''
          gap: '12px
        })}>
          <For each={performanceMetrics()}>
            {(metric) => (''
              <div class={css({ '''
               textAlign: center,
               p: '12px,
                borderRadius: '8px,
               bg: '#fafafa,
               border: '1px solid #f0f0f0,
               transition: all 0.3s ease,
                _hover: { '''
                 bg: '#f0f0f0,
                 transform: translateY(-2px),
                  boxShadow:  0 4px 12px rgba(0,0,0,0.1)
                }
              })}>'''
                <div class={css({ ''''
                  fontSize', 11px: $4,
                 color: '#8c8c8c,
                 mb: '6px,
                  fontWeight: ''500
                })}>
                  {metric.label}
                </div>''
                <div class={css({ '''
                  fontSize: '16px,
                  fontWeight: '700,
                  color: metric.trend ===up' ?'#52c41a' metric.trend ===down' ? '#ff4d4f: ''#262626
                })}>
                  {metric.value}
                </div>
              </div>
            )}
          </For>
        </div>
      </div>

      {/* 交互式图表分析 */}
      <BacktestCharts result={getBacktestResult()} height={450} />
    </div>
  )
}
'`
```
