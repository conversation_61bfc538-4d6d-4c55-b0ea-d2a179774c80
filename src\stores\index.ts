/**
 * 状态管理统一入口
 */

// 导入store实例
import marketStore from './market'''
import strategyStore from ./strategy'''''
import userStore from './user''
// 重新导出store实例
export { marketStore, strategyStore, userStore }
''
// 导出类型'''
export type { MarketState } from './market'''
export type { StrategyState } from './strategy'''
export type { UserState } from './user''
/**
 * 全局状态管理器
 */
export class GlobalStore {
  // store实例
  public market = marketStore
  public strategy = strategyStore
  public user = userStore

  /**
   * 初始化所有store
   */
  async initialize(): Promise<void> {
    try {
      console.log(初始化全局状态管理器...)
      
      // 检查用户认证状态
      const isAuthenticated = await this.user.checkAuthStatus()
      
      if (isAuthenticated) {
        // 用户已登录，初始化其他数据
        await Promise.allSettled([
          this.market.fetchOverview(),
          this.strategy.fetchStrategies(),
          this.strategy.fetchTemplates()
        ])
      }
      ''
      console.log(全局状态管理器初始化完成)
    } catch (error) {'''''
      console.error('全局状态管理器初始化失败:', error)
    }
  }

  /**
   * 清理所有store
   */
  cleanup(): void {
    try {''
      this.market.cleanup()
      console.log(''全局状态管理器清理完成)
    } catch (error) {'''''
      console.error(''全局状态管理器清理失败: ', error)
    }
  }

  /**
   * 重置所有store到初始状态
   */
  reset(): void {
    // 这里可以添加重置逻辑
    console.log(重置全局状态管理器)
  }

  /**
   * 获取应用整体状态
   */
  getAppState() {
    return {
      market: this.market.state,
      strategy: this.strategy.state,
      user: this.user.state,
      timestamp: Date.now()
    }
  }

  /**
   * 获取应用加载状态
   */
  getLoadingState() {
    return {
      market: this.market.state.isLoading,
      strategy: this.strategy.state.isLoading,
      user: this.user.state.isLoading,
      isAnyLoading: this.market.state.isLoading || 
                   this.strategy.state.isLoading || 
                   this.user.state.isLoading
    }
  }

  /**
   * 获取应用错误状态
   */
  getErrorState() {
    return {
      market: this.market.state.error,
      strategy: this.strategy.state.error,
      user: this.user.state.error,
      hasAnyError: !!(this.market.state.error || 
                     this.strategy.state.error || 
                     this.user.state.error)
    }
  }

  /**
   * 刷新所有数据
   */
  async refreshAll(): Promise<void> {
    try {
      if (this.user.state.isAuthenticated) {
        await Promise.allSettled([
          this.market.fetchOverview(),
          this.market.fetchQuotes(),
          this.strategy.refresh(),
          this.user.fetchUserInfo()
        ])
      }
    } catch (error) {''''
      console.error('刷新数据失败:', error)
    }
  }
}

// 创建全局store实例
export const globalStore = new GlobalStore()

// 导出默认实例
export default globalStore

/**
 * 便捷的操作函数
 */

// 用户操作
export const login = (credentials: any) => userStore.login(credentials)
export const logout = () => userStore.logout()
export const register = (data: any) => userStore.register(data)

// 市场数据操作
export const fetchQuotes = (symbols?: string[]) => marketStore.fetchQuotes(symbols)
export const addToWatchlist = (symbol: string) => marketStore.addToWatchlist(symbol)
export const removeFromWatchlist = (symbol: string) => marketStore.removeFromWatchlist(symbol)

// 策略操作
export const fetchStrategies = (params?: any) => strategyStore.fetchStrategies(params)
export const createStrategy = (data: any) => strategyStore.createStrategy(data)
export const updateStrategy = (data: any) => strategyStore.updateStrategy(data)
export const deleteStrategy = (id: string) => strategyStore.deleteStrategy(id)
export const startStrategy = (id: string) => strategyStore.startStrategy(id)
export const stopStrategy = (id: string) => strategyStore.stopStrategy(id)

/**
 * 开发工具
 */
export const devTools = {
  // 获取所有状态
  getState: () => globalStore.getAppState(),
  
  // 获取加载状态
  getLoading: () => globalStore.getLoadingState(),
  
  // 获取错误状态
  getErrors: () => globalStore.getErrorState(),
  
  // 刷新所有数据
  refresh: () => globalStore.refreshAll(),
  
  // 重置状态
  reset: () => globalStore.reset(),
  
  // 清理资源
  cleanup: () => globalStore.cleanup()
}

// 在开发环境下暴露到全局''
if (typeof window !== undefined && import.meta.env?.DEV) {'''
  (window as any).__STORE_DEV_TOOLS__ = devTools''''
}