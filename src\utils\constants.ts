/**
 * 应用常量定义
 */

// API配置常量
export const API_CONFIG = {
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
  BASE_URL: import.meta.env?.VITE_API_BASE_URL || http:// localhost:8000/api/v1
} as const

// API路径常量
export const API_PATHS = {
  // 用户认证相关
  AUTH: { 
    LOGIN: /auth/login,
    LOGOUT: /auth/logout,
    REFRESH: '/auth/refresh,
    REGISTER: '/auth/register,
    PROFILE: '/auth/me,
    CHANGE_PASSWORD: '/auth/change-password,
    RESET_PASSWORD: '/auth/reset-password,
    USERS: ''/auth/users
  },

  // 市场数据相关''
  MARKET: { ''
    QUOTE: /market/quote,
    QUOTES: /market/quotes,
    KLINE: '/market/kline,
    HISTORY: '/market/history,
    SEARCH: '/market/search,
    OVERVIEW: '/market/overview,
    SECTORS: '/market/sectors,
    NEWS: '/market/news,
    RANKING: '/market/ranking,
    ORDERBOOK: '/market/orderbook,
    TICK: '/market/tick,
    STOCKS: '/market/stocks,
    WATCHLIST: '/market/watchlist,
    DEPTH: '/market/depth,
    SYMBOLS: ''/market/symbols
  },

  // 交易相关''
  TRADING: { ''
    ACCOUNT: /trading/account,
    POSITIONS: /trading/positions,
    ORDERS: '/trading/orders,
    TRADES: '/trading/trades,
    SUBMIT: '/trading/submit,
    CANCEL: '/trading/cancel,
    MODIFY: '/trading/modify,
    HISTORY: ''/trading/history
  },

  // 策略相关''
  STRATEGY: { ''
    LIST: /strategy/list,
    DETAIL: /strategy/detail,
    CREATE: '/strategy/create,
    UPDATE: '/strategy/update,
    DELETE: '/strategy/delete,
    START: '/strategy/start,
    STOP: '/strategy/stop,
    BACKTEST: '/strategy/backtest,
    PERFORMANCE: '/strategy/performance,
    SIGNALS: '/strategy/signals,
    TEMPLATES: '/strategy/templates,
    FILES: ''/strategy-files
  },

  // 回测相关''
  BACKTEST: { ''
    RUN: /backtest/run,
    RESULT: /backtest/result,
    HISTORY: '/backtest/history,
    COMPARE: '/backtest/compare,
    START: '/backtest/start,
    STOP: '/backtest/stop,
    DELETE: '/backtest/delete,
    HEALTH: ''/backtest/health
  },

  // 用户管理''
  USER: { ''
    PROFILE: /user/profile,
    SETTINGS: /user/settings,
    AVATAR: '/user/avatar,
    PREFERENCES: ''/user/preferences
  },

  // 系统相关''
  SYSTEM: { ''
    HEALTH: /health,
    STATUS: /status,
    CONFIG: ''/config
  }
} as const

// WebSocket路径常量''
export const WS_PATHS = { '''
  MARKET: /ws/market,
  TRADING: /ws/trading,
  STRATEGY: '/ws/strategy,
  GENERAL: ''/ws
} as const

// 环境配置
export const ENV_CONFIG = {''
  isDevelopment: import.meta.env?.MODE === development,
  isProduction: import.meta.env?.MODE === production,
  isTesting: import.meta.env?.MODE === test,
  apiBaseUrl: import.meta.env?.VITE_API_BASE_URL || http:// localhost:8000/api/v1,
  wsUrl: import.meta.env?.VITE_WS_URL || ws: // localhost:8000/api/v1,
  // 可通过环境变量控制模拟模式'
  enableMock: import.meta.env?.VITE_USE_MOCK === true || import.meta.env?.MODE === development,
  enableDevtools: import.meta.env?.VITE_ENABLE_DEVTOOLS === true' || import.meta.env?.MODE === development'
} as const

// 市场相关常量
export const MARKET_CONFIG = {
  // 交易时间''
  TRADING_HOURS: { ''
    MORNING_START: 09:30,
    MORNING_END: 11:30,
    AFTERNOON_START: '13:00,
    AFTERNOON_END: ''15:00
  },

  // 市场状态''
  MARKET_STATUS: { ''
    CLOSED: closed,
    OPENING: opening,
    TRADING: trading,
    BREAK: break,
    CLOSING: closing
  },

  // K线周期''
  KLINE_PERIODS: { ''
    1m: 1分钟,
    5m: 5分钟,
    15m: '15分钟,
    30m: '30分钟,
    1h: '1小时,
    1d: '日线,
    1w: '周线,
    1M: ''月线
  }
} as const

// 交易相关常量
export const TRADING_CONFIG = {
  // 订单类型''
  ORDER_TYPES: { ''
    MARKET: market,
    LIMIT: limit,
    STOP: stop,
    STOP_LIMIT: stop_limit
  },

  // 订单方向''
  ORDER_SIDES: { ''
    BUY: buy,
    SELL: sell'
  },

  // 订单状态''
  ORDER_STATUS: { ''
    PENDING: pending,
    PARTIAL: partial,
    FILLED: filled,
    CANCELLED: cancelled,
    REJECTED: rejected
  },

  // 持仓方向''
  POSITION_SIDES: { ''
    LONG: long,
    SHORT: short'
  }
} as const

// 策略相关常量
export const STRATEGY_CONFIG = {
  // 策略类型''
  TYPES: { ''
    TREND_FOLLOWING: trend_following,
    MEAN_REVERSION: mean_reversion,
    MOMENTUM: momentum,
    ARBITRAGE: arbitrage,
    MARKET_MAKING: market_making
  },

  // 策略状态''
  STATUS: { ''
    INACTIVE: inactive,
    ACTIVE: active,
    PAUSED: paused,
    ERROR: error
  },

  // 风险等级''
  RISK_LEVELS: { ''
    LOW: low,
    MEDIUM: medium,
    HIGH: high
  },

  // 运行频率''
  FREQUENCIES: { ''
    TICK: tick,
    MINUTE: minute,
    HOUR: hour,
    DAY: day
  }
} as const

// 回测相关常量
export const BACKTEST_CONFIG = {
  // 回测状态''
  STATUS: { ''
    PENDING: pending,
    RUNNING: running,
    COMPLETED: completed,
    FAILED: failed,
    CANCELLED: cancelled
  },

  // 默认配置
  DEFAULTS: {
    INITIAL_CAPITAL: 100000,
    COMMISSION: 0.0003,
    SLIPPAGE: 0.0001,
    START_DATE: 2023-01-01,
    END_DATE: new Date().toISOString().split(T)[0]
  }
} as const

// 指数代码映射''
export const INDEX_MAP: Record<string, string> = { '''
  000001.SH: 上证指数,
  SH000001: 上证指数,
  399001.SZ: '深证成指,
  SZ399001: '深证成指,
  399006.SZ: '创业板指,
  SZ399006: '创业板指,
  000688.SH: '科创50,
  SH000688: ''科创50
} as const

// 错误代码
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 0,
  NETWORK_ERROR: 1001,
  TIMEOUT_ERROR: 1002,
  PARSE_ERROR: 1003,

  // 认证错误
  UNAUTHORIZED: 2001,
  TOKEN_EXPIRED: 2002,
  INVALID_TOKEN: 2003,

  // 业务错误
  INVALID_PARAMS: 3001,
  RESOURCE_NOT_FOUND: 3002,
  PERMISSION_DENIED: 3003,
  OPERATION_FAILED: 3004,

  // 市场数据错误
  MARKET_CLOSED: 4001,
  SYMBOL_NOT_FOUND: 4002,
  DATA_NOT_AVAILABLE: 4003,

  // 交易错误
  INSUFFICIENT_BALANCE: 5001,
  INVALID_ORDER: 5002,
  ORDER_REJECTED: 5003,
  POSITION_NOT_FOUND: 5004''
} as const''''
