import { Component, JSX, Show, createEffect, onCleanup } from 'solid-js';
import { Portal } from 'solid-js/web';
import { css, cx } from '../../styled-system/css';
import Button from './Button';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
  maskClosable?: boolean;
  children: JSX.Element;
  footer?: JSX.Element;
  class?: string;
}

const Modal: Component<ModalProps> = (props) => {
  // 处理ESC键关闭
  createEffect(() => {
    if (props.isOpen) {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape' && props.closable !== false) {
          props.onClose();
        }
      };
      
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
      
      onCleanup(() => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = '';
      });
    }
  });

  const overlayStyles = css({
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    padding: '16px',
  });

  const modalStyles = css({
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 20px 25px rgba(0, 0, 0, 0.1)',
    maxHeight: '90vh',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    animation: 'modalEnter 0.2s ease-out',
  });

  const sizeStyles = {
    sm: css({ width: '400px', maxWidth: '90vw' }),
    md: css({ width: '600px', maxWidth: '90vw' }),
    lg: css({ width: '800px', maxWidth: '90vw' }),
    xl: css({ width: '1000px', maxWidth: '90vw' }),
  };

  const headerStyles = css({
    padding: '24px 24px 0 24px',
    borderBottom: '1px solid',
    borderColor: 'gray.200',
    paddingBottom: '16px',
    marginBottom: '24px',
  });

  const titleStyles = css({
    fontSize: '20px',
    fontWeight: '600',
    color: 'gray.900',
    margin: 0,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  });

  const closeButtonStyles = css({
    background: 'none',
    border: 'none',
    fontSize: '24px',
    cursor: 'pointer',
    color: 'gray.400',
    padding: '4px',
    borderRadius: '4px',
    _hover: {
      color: 'gray.600',
      backgroundColor: 'gray.100',
    },
  });

  const contentStyles = css({
    padding: '0 24px',
    flex: 1,
    overflow: 'auto',
  });

  const footerStyles = css({
    padding: '16px 24px 24px 24px',
    borderTop: '1px solid',
    borderColor: 'gray.200',
    marginTop: '24px',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
  });

  const size = props.size || 'md';

  const handleOverlayClick = (e: MouseEvent) => {
    if (e.target === e.currentTarget && props.maskClosable !== false) {
      props.onClose();
    }
  };

  return (
    <Show when={props.isOpen}>
      <Portal>
        <div class={overlayStyles} onClick={handleOverlayClick}>
          <div 
            class={cx(modalStyles, sizeStyles[size], props.class)}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <Show when={props.title || props.closable !== false}>
              <div class={headerStyles}>
                <h2 class={titleStyles}>
                  <span>{props.title}</span>
                  <Show when={props.closable !== false}>
                    <button
                      class={closeButtonStyles}
                      onClick={props.onClose}
                      aria-label="Close modal"
                    >
                      ×
                    </button>
                  </Show>
                </h2>
              </div>
            </Show>

            {/* Content */}
            <div class={contentStyles}>
              {props.children}
            </div>

            {/* Footer */}
            <Show when={props.footer}>
              <div class={footerStyles}>
                {props.footer}
              </div>
            </Show>
          </div>
        </div>
      </Portal>
    </Show>
  );
};

export default Modal;
