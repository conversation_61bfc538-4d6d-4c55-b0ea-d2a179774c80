/**
 * 市场数据状态管理 - 基于 SolidJS Store
 */
import { createStore, createSignal } from 'solid-js/store';
import { createEffect, onCleanup } from 'solid-js';

// 市场数据接口
export interface QuoteData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: number;
}

export interface MarketOverview {
  indices: {
    name: string;
    value: number;
    change: number;
    changePercent: number;
  }[];
  summary: {
    totalSymbols: number;
    advancing: number;
    declining: number;
    unchanged: number;
  };
}

// EventBus 事件总线系统
class EventBus<T extends Record<string, any>> {
  private listeners: Map<keyof T, Set<(data: T[keyof T]) => void>> = new Map();

  on<K extends keyof T>(event: K, listener: (data: T[K]) => void): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);

    // 返回取消订阅函数
    return () => {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(listener);
        if (eventListeners.size === 0) {
          this.listeners.delete(event);
        }
      }
    };
  }

  emit<K extends keyof T>(event: K, data: T[K]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`EventBus error for event ${String(event)}:`, error);
        }
      });
    }
  }

  off<K extends keyof T>(event: K, listener?: (data: T[K]) => void): void {
    if (!listener) {
      this.listeners.delete(event);
    } else {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(listener);
      }
    }
  }

  clear(): void {
    this.listeners.clear();
  }
}

// 实时行情事件总线
export const marketEventBus = new EventBus<{
  'price-update': { symbol: string; price: number; change: number; changePercent: number };
  'volume-spike': { symbol: string; volume: number; avgVolume: number };
  'market-open': { timestamp: number };
  'market-close': { timestamp: number };
  'connection-status': { status: 'connected' | 'disconnected' | 'error' };
}>();

// 市场数据状态
export interface MarketState {
  tickers: Record<string, number>;
  selectedSymbol: string | null;
  watchlist: string[];
  marketOverview: MarketOverview | null;
  loading: boolean;
  error: string | null;
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastUpdate: number;
}

// 创建市场数据 Store
export const [marketStore, setMarketStore] = createStore<MarketState>({
  tickers: {},
  selectedSymbol: null,
  watchlist: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'BTC-USD', 'ETH-USD', 'EUR/USD', 'GC=F'],
  marketOverview: null,
  loading: false,
  error: null,
  connectionStatus: 'disconnected',
  lastUpdate: 0
});

// 市场数据操作函数
export const updateTickers = (newTickers: Record<string, number>) => {
  // 更新ticker数据并触发事件
  Object.entries(newTickers).forEach(([symbol, price]) => {
    const oldPrice = marketStore.tickers[symbol] || 0;
    const change = price - oldPrice;
    const changePercent = oldPrice > 0 ? (change / oldPrice) * 100 : 0;

    // 发布价格更新事件
    marketEventBus.emit('price-update', {
      symbol,
      price,
      change,
      changePercent
    });
  });

  // 更新状态
  setMarketStore('tickers', tickers => ({ ...tickers, ...newTickers }));
  setMarketStore('lastUpdate', Date.now());
};

export const updateQuotes = (quotes: QuoteData[]) => {
  const quotesMap: Record<string, number> = {};
  quotes.forEach(quote => {
    quotesMap[quote.symbol] = quote.price;

    // 检测成交量异动
    if (quote.volume && quote.volume > 1000000) {
      marketEventBus.emit('volume-spike', {
        symbol: quote.symbol,
        volume: quote.volume,
        avgVolume: 500000 // 使用固定值
      });
    }
  });

  updateTickers(quotesMap);
};

export const setSelectedSymbol = (symbol: string | null) => {
  setMarketStore('selectedSymbol', symbol);
};

export const addToWatchlist = (symbol: string) => {
  if (!marketStore.watchlist.includes(symbol)) {
    setMarketStore('watchlist', watchlist => [...watchlist, symbol]);
  }
};

export const removeFromWatchlist = (symbol: string) => {
  setMarketStore('watchlist', watchlist => watchlist.filter(s => s !== symbol));
};

export const setMarketLoading = (loading: boolean) => {
  setMarketStore('loading', loading);
};

export const setMarketError = (error: string | null) => {
  setMarketStore('error', error);
};

export const setMarketConnection = (status: 'connected' | 'disconnected' | 'error') => {
  setMarketStore('connectionStatus', status);
  marketEventBus.emit('connection-status', { status });
};

// 异步操作函数
export const fetchQuotes = async (symbols?: string[]) => {
  const targetSymbols = symbols || marketStore.watchlist;
  if (targetSymbols.length === 0) return;

  try {
    setMarketLoading(true);
    setMarketError(null);
    
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟行情数据
    const mockQuotes: QuoteData[] = targetSymbols.map(symbol => ({
      symbol,
      price: Math.random() * 100 + 50,
      change: (Math.random() - 0.5) * 10,
      changePercent: (Math.random() - 0.5) * 5,
      volume: Math.floor(Math.random() * 1000000),
      timestamp: Date.now()
    }));
    
    updateQuotes(mockQuotes);
  } catch (error) {
    console.error('获取行情失败:', error);
    setMarketError(error instanceof Error ? error.message : '获取行情失败');
  } finally {
    setMarketLoading(false);
  }
};

export const fetchOverview = async () => {
  try {
    setMarketLoading(true);
    setMarketError(null);
    
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const mockOverview: MarketOverview = {
      indices: [
        { name: 'S&P 500', value: 4200, change: 25.5, changePercent: 0.6 },
        { name: 'NASDAQ', value: 13000, change: -15.2, changePercent: -0.12 },
        { name: 'DOW', value: 34500, change: 45.8, changePercent: 0.13 }
      ],
      summary: {
        totalSymbols: 5000,
        advancing: 2800,
        declining: 1900,
        unchanged: 300
      }
    };
    
    setMarketStore('marketOverview', mockOverview);
    setMarketStore('lastUpdate', Date.now());
  } catch (error) {
    console.error('获取市场概览失败:', error);
    setMarketError(error instanceof Error ? error.message : '获取市场概览失败');
  } finally {
    setMarketLoading(false);
  }
};

// 便捷访问函数
export const getQuote = (symbol: string) => marketStore.tickers[symbol];
export const getQuotes = (symbols: string[]) => {
  return symbols.map(symbol => marketStore.tickers[symbol]).filter(Boolean);
};

export const getSelectedSymbol = () => marketStore.selectedSymbol;
export const getWatchlist = () => marketStore.watchlist;
export const getMarketOverview = () => marketStore.marketOverview;
export const isMarketLoading = () => marketStore.loading;
export const getMarketError = () => marketStore.error;
export const getMarketConnection = () => marketStore.connectionStatus;
export const getLastUpdate = () => marketStore.lastUpdate;

// 初始化市场数据系统
export const initializeMarketSystem = () => {
  // 定期获取市场概览
  const overviewInterval = setInterval(() => {
    fetchOverview();
  }, 30000); // 30秒更新一次

  // 定期获取行情数据
  const quotesInterval = setInterval(() => {
    if (marketStore.connectionStatus !== 'connected') {
      fetchQuotes();
    }
  }, 5000); // 5秒更新一次

  // 返回清理函数
  return () => {
    clearInterval(overviewInterval);
    clearInterval(quotesInterval);
    marketEventBus.clear();
  };
};

// 初始数据加载
setTimeout(() => {
  fetchOverview();
  fetchQuotes();
}, 100);

// 导出EventBus实例供其他组件使用
export { marketEventBus };