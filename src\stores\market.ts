/**
 * 市场数据状态管理 - 基于Jotai原子化状态
 */
import { atom, createStore } from jotai'''
import { createEffect, onCleanup } from solid-js'''
import { marketApi, marketWS, type QuoteData, type MarketOverview } from '../api''
// 创建Jotai store实例
const marketStore = createStore()

// EventBus 事件总线系统
class EventBus<T extends Record<string, any>> {
  private listeners: Map<keyof T, Set<(data: T[keyof T]) => void>> = new Map()

  on<K extends keyof T>(event: K, listener: (data: T[K]) => void): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(listener)

    // 返回取消订阅函数
    return () => {
      const eventListeners = this.listeners.get(event)
      if (eventListeners) {
        eventListeners.delete(listener)
        if (eventListeners.size === 0) {
          this.listeners.delete(event)
        }
      }
    }
  }

  emit<K extends keyof T>(event: K, data: T[K]): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`EventBus error for event ${String(event)}:`, error)
        }
      })
    }
  }

  off<K extends keyof T>(event: K, listener?: (data: T[K]) => void): void {
    if (!listener) {
      this.listeners.delete(event)
    } else {
      const eventListeners = this.listeners.get(event)
      if (eventListeners) {
        eventListeners.delete(listener)
      }
    }
  }

  clear(): void {
    this.listeners.clear()
  }
}

// 实时行情事件总线
export const marketEventBus = new EventBus<{`
  price-update: { symbol: string; price: number; change: number; changePercent: number }
  volume-spike': { symbol: string; volume: number; avgVolume: number }
  market-open': { timestamp: number }
  market-close': { timestamp: number }
  connection-status': { status: connected' | disconnected' | error }
}>()

// Jotai 原子状态
export const tickersAtom = atom<Record<string, number>>({})
export const selectedSymbolAtom = atom<string | null>(null)
export const watchlistAtom = atom<string[]>(['''
  AAPL, MSFT', GOOGL', AMZN', TSLA,'
  BTC-USD', ETH-USD', EUR/USD', GC=F])

// 衍生状态
export const topMoversAtom = atom((get) => {
  const tickers = get(tickersAtom)
  return Object.entries(tickers)
    .map(([symbol, price]) => ({ symbol, price }))
    .sort((a, b) => Math.abs(b.price - a.price) - Math.abs(a.price - b.price))
    .slice(0, 5)
})

// 市场概览原子
export const marketOverviewAtom = atom<MarketOverview | null>(null)

// 加载状态原子
export const marketLoadingAtom = atom<boolean>(false)

// 错误状态原子
export const marketErrorAtom = atom<string | null>(null)
'''
// 连接状态原子''''
export const marketConnectionAtom = atom<connected | disconnected' | connecting' | error'>(disconnected)

// 最后更新时间原子
export const lastUpdateAtom = atom<number>(0)

// 市场数据操作函数
export const updateTickers = (newTickers: Record<string, number>) => {
  // 更新ticker数据并触发事件
  Object.entries(newTickers).forEach(([symbol, price]) => {
    const oldPrice = marketStore.get(tickersAtom)[symbol] || 0
    const change = price - oldPrice
    const changePercent = oldPrice > 0 ? (change / oldPrice) * 100 : 0

    // 发布价格更新事件
    marketEventBus.emit(price-update, {
      symbol,
      price,
      change,
      changePercent
    })
  })

  // 更新原子状态
  marketStore.set(tickersAtom, prev => ({ ...prev, ...newTickers }))
  marketStore.set(lastUpdateAtom, Date.now())
}

export const updateQuotes = (quotes: QuoteData[]) => {
  const quotesMap: Record<string, number> = {}
  quotes.forEach(quote => {
    quotesMap[quote.symbol] = quote.price

    // 检测成交量异动
    if (quote.volume && quote.volume > 1000000) { // 简化条件，移除avgVolume
      marketEventBus.emit(volume-spike, {
        symbol: quote.symbol,
        volume: quote.volume,
        avgVolume: 500000 // 使用固定值
      })
    }
  })

  updateTickers(quotesMap)
}

export const setSelectedSymbol = (symbol: string | null) => {
  marketStore.set(selectedSymbolAtom, symbol)
}

export const addToWatchlist = (symbol: string) => {
  const currentWatchlist = marketStore.get(watchlistAtom)
  if (!currentWatchlist.includes(symbol)) {
    marketStore.set(watchlistAtom, [...currentWatchlist, symbol])
  }
}

export const removeFromWatchlist = (symbol: string) => {
  const currentWatchlist = marketStore.get(watchlistAtom)
  marketStore.set(watchlistAtom, currentWatchlist.filter(s => s !== symbol))
}

export const setMarketLoading = (loading: boolean) => {
  marketStore.set(marketLoadingAtom, loading)
}

export const setMarketError = (error: string | null) => {
  marketStore.set(marketErrorAtom, error)
}
''''
export const setMarketConnection = (status: connected | disconnected' | error') => {'
  marketStore.set(marketConnectionAtom, status)
  marketEventBus.emit(connection-status', { status })
}

// 异步操作函数
export const fetchQuotes = async (symbols?: string[]) => {
  const targetSymbols = symbols || marketStore.get(watchlistAtom)
  if (targetSymbols.length === 0) return

  try {
    setMarketLoading(true)
    setMarketError(null)
    const quotes = await marketApi.getQuote(targetSymbols)
    updateQuotes(quotes)
  } catch (error) {'''
    console.error(''获取行情失败: ', error)
    setMarketError(error instanceof Error ? error.message: '获取行情失败)
  } finally {
    setMarketLoading(false)
  }
}

export const fetchOverview = async () => {
  try {
    setMarketLoading(true)
    setMarketError(null)
    const overview = await marketApi.getOverview()
    marketStore.set(marketOverviewAtom, overview)
    marketStore.set(lastUpdateAtom, Date.now())
  } catch (error) {'''''
    console.error(''获取市场概览失败: ', error)
    setMarketError(error instanceof Error ? error.message: '获取市场概览失败)
  } finally {
    setMarketLoading(false)
  }
}

// WebSocket 管理函数
export const subscribeQuotes = (symbols: string[]) => {
  if (marketWS.getState() === connected) {
    marketWS.subscribeQuote(symbols)
  }
}
''''
export const unsubscribeQuotes = (symbols: string[]) => {'''''
  if (marketWS.getState() === connected') {
    marketWS.unsubscribeQuote(symbols)
  }
}
''
export const initializeWebSocket = () => {'''
  // 监听WebSocket连接状态''''
  marketWS.on(open, () => {'''''
    console.log(''市场数据WebSocket已连接)
    setMarketConnection(connected)

    // 连接成功后订阅监控列表
    const watchlist = marketStore.get(watchlistAtom)
    if (watchlist.length > 0) {
      subscribeQuotes(watchlist)
    }

    // 发布市场开盘事件
    marketEventBus.emit(market-open, { timestamp: Date.now() })
  })
'''
  // 监听实时数据''''
  marketWS.on(message, (message) => {'''''
    if (message.type === quote') {''
      updateQuotes([message.data])
    } else if (message.type === overview') {'
      marketStore.set(marketOverviewAtom, message.data)
      marketStore.set(lastUpdateAtom, Date.now())
    }
  })
'''
  // 监听连接错误''''
  marketWS.on(error, (error) => {'''''
    console.error(''市场数据WebSocket错误: ', error)
    setMarketConnection(error)
    setMarketError('实时数据连接异常)
  })
'''
  // 监听连接关闭'`
  marketWS.on(close, () => {``
    console.log(''市场数据WebSocket连接已关闭)
    setMarketConnection(disconnected)

    // 发布市场收盘事件
    marketEventBus.emit(market-close, { timestamp: Date.now() })
  })
}

export const cleanupMarket = () => {
  marketWS.disconnect()
  marketEventBus.clear()
}

// 便捷访问函数
export const getQuote = (symbol: string) => marketStore.get(tickersAtom)[symbol]
export const getQuotes = (symbols: string[]) => {
  const tickers = marketStore.get(tickersAtom)
  return symbols.map(symbol => tickers[symbol]).filter(Boolean)
}

export const getSelectedSymbol = () => marketStore.get(selectedSymbolAtom)
export const getWatchlist = () => marketStore.get(watchlistAtom)
export const getTopMovers = () => marketStore.get(topMoversAtom)
export const getMarketOverview = () => marketStore.get(marketOverviewAtom)
export const isMarketLoading = () => marketStore.get(marketLoadingAtom)
export const getMarketError = () => marketStore.get(marketErrorAtom)
export const getMarketConnection = () => marketStore.get(marketConnectionAtom)
export const getLastUpdate = () => marketStore.get(lastUpdateAtom)

// 初始化市场数据系统
export const initializeMarketSystem = () => {
  // 初始化WebSocket
  initializeWebSocket()

  // 定期获取市场概览
  const overviewInterval = setInterval(() => {
    fetchOverview()
  }, 30000) // 30秒更新一次

  // 定期获取行情数据（作为WebSocket的备用）
  const quotesInterval = setInterval(() => {
    if (marketWS.getState() !== connected) {
      fetchQuotes()
    }
  }, 5000) // 5秒更新一次

  // 返回清理函数
  return () => {
    clearInterval(overviewInterval)
    clearInterval(quotesInterval)
    cleanupMarket()
  }
}

// SolidJS 集成效果
createEffect(() => {
  const cleanup = initializeMarketSystem()

  onCleanup(() => {
    cleanup()
  })
})

// 初始数据加载
fetchOverview()
fetchQuotes()
'''
// 导出EventBus实例供其他组件使用''`
export { marketEventBus }