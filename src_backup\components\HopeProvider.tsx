import { <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, HopeTheme } from '@hope-ui/solid';
import { JSX } from solid-js';

// 自定义主题配置
const theme: HopeTheme = {
  colors: {''
    // 主要颜色 - 量化交易主题'''
    primary: {''''
      50: #eff6ff,
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: ''#1e3a8a'
    },''
    // 成功/涨 - 绿色'''
    success: {''''
      50: #f0fdf4,
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: ''#14532d'
    },''
    // 警告/跌 - 红色'''
    danger: {''''
      50: #fef2f2,
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: ''#7f1d1d'
    },''
    // 中性色'''
    neutral: {''''
      50: #f8fafc,
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: ''#0f172a'
    }
  },''
  fonts: { '''
    sans: Inter, system-ui, sans-serif, ''
    mono:  JetBrains Mono, Monaco, Consolas, monospace'
  },''
  fontSizes: { '''
    xs: '0.75rem,
    sm: '0.875rem,
    base: '1rem,
    lg: '1.125rem,
    xl: '1.25rem,
    2xl: '1.5rem,
    3xl: '1.875rem,
    4xl: '2.25rem,
    5xl: ''3rem
  },''
  radii: { '''
    none: '0,
    sm: '0.125rem,
    base: '0.25rem,
    md: '0.375rem,
    lg: '0.5rem,
    xl: '0.75rem,
    2xl: '1rem,
    3xl: '1.5rem,
    full: ''9999px
  },''
  shadows: { '''
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05), ''
    sm:  0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06), ''
    base:  0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), ''
    md:  0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), ''
    lg:  0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04),'
    xl:  0 25px 50px -12px rgba(0, 0, 0, 0.25)
  }
};

interface HopeProviderProps {
  children: JSX.Element;
}

export default function HopeProvider(props: HopeProviderProps) {
  return (
    <HopeUIProvider theme={theme}>
      {props.children}
    </HopeUIProvider>''
  );
}