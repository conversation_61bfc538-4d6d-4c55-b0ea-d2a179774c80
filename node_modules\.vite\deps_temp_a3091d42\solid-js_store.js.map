{"version": 3, "sources": ["../../solid-js/store/dist/dev.js"], "sourcesContent": ["import { DEV as DEV$1, $PROXY, $TRACK, getListener, batch, createSignal } from 'solid-js';\n\nconst $RAW = Symbol(\"store-raw\"),\n  $NODE = Symbol(\"store-node\"),\n  $HAS = Symbol(\"store-has\"),\n  $SELF = Symbol(\"store-self\");\nconst DevHooks = {\n  onStoreNodeUpdate: null\n};\nfunction wrap$1(value) {\n  let p = value[$PROXY];\n  if (!p) {\n    Object.defineProperty(value, $PROXY, {\n      value: p = new Proxy(value, proxyTraps$1)\n    });\n    if (!Array.isArray(value)) {\n      const keys = Object.keys(value),\n        desc = Object.getOwnPropertyDescriptors(value);\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const prop = keys[i];\n        if (desc[prop].get) {\n          Object.defineProperty(value, prop, {\n            enumerable: desc[prop].enumerable,\n            get: desc[prop].get.bind(p)\n          });\n        }\n      }\n    }\n  }\n  return p;\n}\nfunction isWrappable(obj) {\n  let proto;\n  return obj != null && typeof obj === \"object\" && (obj[$PROXY] || !(proto = Object.getPrototypeOf(obj)) || proto === Object.prototype || Array.isArray(obj));\n}\nfunction unwrap(item, set = new Set()) {\n  let result, unwrapped, v, prop;\n  if (result = item != null && item[$RAW]) return result;\n  if (!isWrappable(item) || set.has(item)) return item;\n  if (Array.isArray(item)) {\n    if (Object.isFrozen(item)) item = item.slice(0);else set.add(item);\n    for (let i = 0, l = item.length; i < l; i++) {\n      v = item[i];\n      if ((unwrapped = unwrap(v, set)) !== v) item[i] = unwrapped;\n    }\n  } else {\n    if (Object.isFrozen(item)) item = Object.assign({}, item);else set.add(item);\n    const keys = Object.keys(item),\n      desc = Object.getOwnPropertyDescriptors(item);\n    for (let i = 0, l = keys.length; i < l; i++) {\n      prop = keys[i];\n      if (desc[prop].get) continue;\n      v = item[prop];\n      if ((unwrapped = unwrap(v, set)) !== v) item[prop] = unwrapped;\n    }\n  }\n  return item;\n}\nfunction getNodes(target, symbol) {\n  let nodes = target[symbol];\n  if (!nodes) Object.defineProperty(target, symbol, {\n    value: nodes = Object.create(null)\n  });\n  return nodes;\n}\nfunction getNode(nodes, property, value) {\n  if (nodes[property]) return nodes[property];\n  const [s, set] = createSignal(value, {\n    equals: false,\n    internal: true\n  });\n  s.$ = set;\n  return nodes[property] = s;\n}\nfunction proxyDescriptor$1(target, property) {\n  const desc = Reflect.getOwnPropertyDescriptor(target, property);\n  if (!desc || desc.get || !desc.configurable || property === $PROXY || property === $NODE) return desc;\n  delete desc.value;\n  delete desc.writable;\n  desc.get = () => target[$PROXY][property];\n  return desc;\n}\nfunction trackSelf(target) {\n  getListener() && getNode(getNodes(target, $NODE), $SELF)();\n}\nfunction ownKeys(target) {\n  trackSelf(target);\n  return Reflect.ownKeys(target);\n}\nconst proxyTraps$1 = {\n  get(target, property, receiver) {\n    if (property === $RAW) return target;\n    if (property === $PROXY) return receiver;\n    if (property === $TRACK) {\n      trackSelf(target);\n      return receiver;\n    }\n    const nodes = getNodes(target, $NODE);\n    const tracked = nodes[property];\n    let value = tracked ? tracked() : target[property];\n    if (property === $NODE || property === $HAS || property === \"__proto__\") return value;\n    if (!tracked) {\n      const desc = Object.getOwnPropertyDescriptor(target, property);\n      if (getListener() && (typeof value !== \"function\" || target.hasOwnProperty(property)) && !(desc && desc.get)) value = getNode(nodes, property, value)();\n    }\n    return isWrappable(value) ? wrap$1(value) : value;\n  },\n  has(target, property) {\n    if (property === $RAW || property === $PROXY || property === $TRACK || property === $NODE || property === $HAS || property === \"__proto__\") return true;\n    getListener() && getNode(getNodes(target, $HAS), property)();\n    return property in target;\n  },\n  set() {\n    console.warn(\"Cannot mutate a Store directly\");\n    return true;\n  },\n  deleteProperty() {\n    console.warn(\"Cannot mutate a Store directly\");\n    return true;\n  },\n  ownKeys: ownKeys,\n  getOwnPropertyDescriptor: proxyDescriptor$1\n};\nfunction setProperty(state, property, value, deleting = false) {\n  if (!deleting && state[property] === value) return;\n  const prev = state[property],\n    len = state.length;\n  DevHooks.onStoreNodeUpdate && DevHooks.onStoreNodeUpdate(state, property, value, prev);\n  if (value === undefined) {\n    delete state[property];\n    if (state[$HAS] && state[$HAS][property] && prev !== undefined) state[$HAS][property].$();\n  } else {\n    state[property] = value;\n    if (state[$HAS] && state[$HAS][property] && prev === undefined) state[$HAS][property].$();\n  }\n  let nodes = getNodes(state, $NODE),\n    node;\n  if (node = getNode(nodes, property, prev)) node.$(() => value);\n  if (Array.isArray(state) && state.length !== len) {\n    for (let i = state.length; i < len; i++) (node = nodes[i]) && node.$();\n    (node = getNode(nodes, \"length\", len)) && node.$(state.length);\n  }\n  (node = nodes[$SELF]) && node.$();\n}\nfunction mergeStoreNode(state, value) {\n  const keys = Object.keys(value);\n  for (let i = 0; i < keys.length; i += 1) {\n    const key = keys[i];\n    setProperty(state, key, value[key]);\n  }\n}\nfunction updateArray(current, next) {\n  if (typeof next === \"function\") next = next(current);\n  next = unwrap(next);\n  if (Array.isArray(next)) {\n    if (current === next) return;\n    let i = 0,\n      len = next.length;\n    for (; i < len; i++) {\n      const value = next[i];\n      if (current[i] !== value) setProperty(current, i, value);\n    }\n    setProperty(current, \"length\", len);\n  } else mergeStoreNode(current, next);\n}\nfunction updatePath(current, path, traversed = []) {\n  let part,\n    prev = current;\n  if (path.length > 1) {\n    part = path.shift();\n    const partType = typeof part,\n      isArray = Array.isArray(current);\n    if (Array.isArray(part)) {\n      for (let i = 0; i < part.length; i++) {\n        updatePath(current, [part[i]].concat(path), traversed);\n      }\n      return;\n    } else if (isArray && partType === \"function\") {\n      for (let i = 0; i < current.length; i++) {\n        if (part(current[i], i)) updatePath(current, [i].concat(path), traversed);\n      }\n      return;\n    } else if (isArray && partType === \"object\") {\n      const {\n        from = 0,\n        to = current.length - 1,\n        by = 1\n      } = part;\n      for (let i = from; i <= to; i += by) {\n        updatePath(current, [i].concat(path), traversed);\n      }\n      return;\n    } else if (path.length > 1) {\n      updatePath(current[part], path, [part].concat(traversed));\n      return;\n    }\n    prev = current[part];\n    traversed = [part].concat(traversed);\n  }\n  let value = path[0];\n  if (typeof value === \"function\") {\n    value = value(prev, traversed);\n    if (value === prev) return;\n  }\n  if (part === undefined && value == undefined) return;\n  value = unwrap(value);\n  if (part === undefined || isWrappable(prev) && isWrappable(value) && !Array.isArray(value)) {\n    mergeStoreNode(prev, value);\n  } else setProperty(current, part, value);\n}\nfunction createStore(...[store, options]) {\n  const unwrappedStore = unwrap(store || {});\n  const isArray = Array.isArray(unwrappedStore);\n  if (typeof unwrappedStore !== \"object\" && typeof unwrappedStore !== \"function\") throw new Error(`Unexpected type ${typeof unwrappedStore} received when initializing 'createStore'. Expected an object.`);\n  const wrappedStore = wrap$1(unwrappedStore);\n  DEV$1.registerGraph({\n    value: unwrappedStore,\n    name: options && options.name\n  });\n  function setStore(...args) {\n    batch(() => {\n      isArray && args.length === 1 ? updateArray(unwrappedStore, args[0]) : updatePath(unwrappedStore, args);\n    });\n  }\n  return [wrappedStore, setStore];\n}\n\nfunction proxyDescriptor(target, property) {\n  const desc = Reflect.getOwnPropertyDescriptor(target, property);\n  if (!desc || desc.get || desc.set || !desc.configurable || property === $PROXY || property === $NODE) return desc;\n  delete desc.value;\n  delete desc.writable;\n  desc.get = () => target[$PROXY][property];\n  desc.set = v => target[$PROXY][property] = v;\n  return desc;\n}\nconst proxyTraps = {\n  get(target, property, receiver) {\n    if (property === $RAW) return target;\n    if (property === $PROXY) return receiver;\n    if (property === $TRACK) {\n      trackSelf(target);\n      return receiver;\n    }\n    const nodes = getNodes(target, $NODE);\n    const tracked = nodes[property];\n    let value = tracked ? tracked() : target[property];\n    if (property === $NODE || property === $HAS || property === \"__proto__\") return value;\n    if (!tracked) {\n      const desc = Object.getOwnPropertyDescriptor(target, property);\n      const isFunction = typeof value === \"function\";\n      if (getListener() && (!isFunction || target.hasOwnProperty(property)) && !(desc && desc.get)) value = getNode(nodes, property, value)();else if (value != null && isFunction && value === Array.prototype[property]) {\n        return (...args) => batch(() => Array.prototype[property].apply(receiver, args));\n      }\n    }\n    return isWrappable(value) ? wrap(value) : value;\n  },\n  has(target, property) {\n    if (property === $RAW || property === $PROXY || property === $TRACK || property === $NODE || property === $HAS || property === \"__proto__\") return true;\n    getListener() && getNode(getNodes(target, $HAS), property)();\n    return property in target;\n  },\n  set(target, property, value) {\n    batch(() => setProperty(target, property, unwrap(value)));\n    return true;\n  },\n  deleteProperty(target, property) {\n    batch(() => setProperty(target, property, undefined, true));\n    return true;\n  },\n  ownKeys: ownKeys,\n  getOwnPropertyDescriptor: proxyDescriptor\n};\nfunction wrap(value) {\n  let p = value[$PROXY];\n  if (!p) {\n    Object.defineProperty(value, $PROXY, {\n      value: p = new Proxy(value, proxyTraps)\n    });\n    const keys = Object.keys(value),\n      desc = Object.getOwnPropertyDescriptors(value);\n    const proto = Object.getPrototypeOf(value);\n    const isClass = proto !== null && value !== null && typeof value === \"object\" && !Array.isArray(value) && proto !== Object.prototype;\n    if (isClass) {\n      let curProto = proto;\n      while (curProto != null) {\n        const descriptors = Object.getOwnPropertyDescriptors(curProto);\n        keys.push(...Object.keys(descriptors));\n        Object.assign(desc, descriptors);\n        curProto = Object.getPrototypeOf(curProto);\n      }\n    }\n    for (let i = 0, l = keys.length; i < l; i++) {\n      const prop = keys[i];\n      if (isClass && prop === \"constructor\") continue;\n      if (desc[prop].get) {\n        const get = desc[prop].get.bind(p);\n        Object.defineProperty(value, prop, {\n          get,\n          configurable: true\n        });\n      }\n      if (desc[prop].set) {\n        const og = desc[prop].set,\n          set = v => batch(() => og.call(p, v));\n        Object.defineProperty(value, prop, {\n          set,\n          configurable: true\n        });\n      }\n    }\n  }\n  return p;\n}\nfunction createMutable(state, options) {\n  const unwrappedStore = unwrap(state || {});\n  if (typeof unwrappedStore !== \"object\" && typeof unwrappedStore !== \"function\") throw new Error(`Unexpected type ${typeof unwrappedStore} received when initializing 'createMutable'. Expected an object.`);\n  const wrappedStore = wrap(unwrappedStore);\n  DEV$1.registerGraph({\n    value: unwrappedStore,\n    name: options && options.name\n  });\n  return wrappedStore;\n}\nfunction modifyMutable(state, modifier) {\n  batch(() => modifier(unwrap(state)));\n}\n\nconst $ROOT = Symbol(\"store-root\");\nfunction applyState(target, parent, property, merge, key) {\n  const previous = parent[property];\n  if (target === previous) return;\n  const isArray = Array.isArray(target);\n  if (property !== $ROOT && (!isWrappable(target) || !isWrappable(previous) || isArray !== Array.isArray(previous) || key && target[key] !== previous[key])) {\n    setProperty(parent, property, target);\n    return;\n  }\n  if (isArray) {\n    if (target.length && previous.length && (!merge || key && target[0] && target[0][key] != null)) {\n      let i, j, start, end, newEnd, item, newIndicesNext, keyVal;\n      for (start = 0, end = Math.min(previous.length, target.length); start < end && (previous[start] === target[start] || key && previous[start] && target[start] && previous[start][key] && previous[start][key] === target[start][key]); start++) {\n        applyState(target[start], previous, start, merge, key);\n      }\n      const temp = new Array(target.length),\n        newIndices = new Map();\n      for (end = previous.length - 1, newEnd = target.length - 1; end >= start && newEnd >= start && (previous[end] === target[newEnd] || key && previous[end] && target[newEnd] && previous[end][key] && previous[end][key] === target[newEnd][key]); end--, newEnd--) {\n        temp[newEnd] = previous[end];\n      }\n      if (start > newEnd || start > end) {\n        for (j = start; j <= newEnd; j++) setProperty(previous, j, target[j]);\n        for (; j < target.length; j++) {\n          setProperty(previous, j, temp[j]);\n          applyState(target[j], previous, j, merge, key);\n        }\n        if (previous.length > target.length) setProperty(previous, \"length\", target.length);\n        return;\n      }\n      newIndicesNext = new Array(newEnd + 1);\n      for (j = newEnd; j >= start; j--) {\n        item = target[j];\n        keyVal = key && item ? item[key] : item;\n        i = newIndices.get(keyVal);\n        newIndicesNext[j] = i === undefined ? -1 : i;\n        newIndices.set(keyVal, j);\n      }\n      for (i = start; i <= end; i++) {\n        item = previous[i];\n        keyVal = key && item ? item[key] : item;\n        j = newIndices.get(keyVal);\n        if (j !== undefined && j !== -1) {\n          temp[j] = previous[i];\n          j = newIndicesNext[j];\n          newIndices.set(keyVal, j);\n        }\n      }\n      for (j = start; j < target.length; j++) {\n        if (j in temp) {\n          setProperty(previous, j, temp[j]);\n          applyState(target[j], previous, j, merge, key);\n        } else setProperty(previous, j, target[j]);\n      }\n    } else {\n      for (let i = 0, len = target.length; i < len; i++) {\n        applyState(target[i], previous, i, merge, key);\n      }\n    }\n    if (previous.length > target.length) setProperty(previous, \"length\", target.length);\n    return;\n  }\n  const targetKeys = Object.keys(target);\n  for (let i = 0, len = targetKeys.length; i < len; i++) {\n    applyState(target[targetKeys[i]], previous, targetKeys[i], merge, key);\n  }\n  const previousKeys = Object.keys(previous);\n  for (let i = 0, len = previousKeys.length; i < len; i++) {\n    if (target[previousKeys[i]] === undefined) setProperty(previous, previousKeys[i], undefined);\n  }\n}\nfunction reconcile(value, options = {}) {\n  const {\n      merge,\n      key = \"id\"\n    } = options,\n    v = unwrap(value);\n  return state => {\n    if (!isWrappable(state) || !isWrappable(v)) return v;\n    const res = applyState(v, {\n      [$ROOT]: state\n    }, $ROOT, merge, key);\n    return res === undefined ? state : res;\n  };\n}\nconst producers = new WeakMap();\nconst setterTraps = {\n  get(target, property) {\n    if (property === $RAW) return target;\n    const value = target[property];\n    let proxy;\n    return isWrappable(value) ? producers.get(value) || (producers.set(value, proxy = new Proxy(value, setterTraps)), proxy) : value;\n  },\n  set(target, property, value) {\n    setProperty(target, property, unwrap(value));\n    return true;\n  },\n  deleteProperty(target, property) {\n    setProperty(target, property, undefined, true);\n    return true;\n  }\n};\nfunction produce(fn) {\n  return state => {\n    if (isWrappable(state)) {\n      let proxy;\n      if (!(proxy = producers.get(state))) {\n        producers.set(state, proxy = new Proxy(state, setterTraps));\n      }\n      fn(proxy);\n    }\n    return state;\n  };\n}\n\nconst DEV = {\n  $NODE,\n  isWrappable,\n  hooks: DevHooks\n} ;\n\nexport { $RAW, DEV, createMutable, createStore, modifyMutable, produce, reconcile, unwrap };\n"], "mappings": ";;;;;;;;;;;AAEA,IAAM,OAAO,OAAO,WAAW;AAA/B,IACE,QAAQ,OAAO,YAAY;AAD7B,IAEE,OAAO,OAAO,WAAW;AAF3B,IAGE,QAAQ,OAAO,YAAY;AAC7B,IAAM,WAAW;AAAA,EACf,mBAAmB;AACrB;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,IAAI,MAAM,MAAM;AACpB,MAAI,CAAC,GAAG;AACN,WAAO,eAAe,OAAO,QAAQ;AAAA,MACnC,OAAO,IAAI,IAAI,MAAM,OAAO,YAAY;AAAA,IAC1C,CAAC;AACD,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,OAAO,OAAO,KAAK,KAAK,GAC5B,OAAO,OAAO,0BAA0B,KAAK;AAC/C,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAM,OAAO,KAAK,CAAC;AACnB,YAAI,KAAK,IAAI,EAAE,KAAK;AAClB,iBAAO,eAAe,OAAO,MAAM;AAAA,YACjC,YAAY,KAAK,IAAI,EAAE;AAAA,YACvB,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,KAAK;AACxB,MAAI;AACJ,SAAO,OAAO,QAAQ,OAAO,QAAQ,aAAa,IAAI,MAAM,KAAK,EAAE,QAAQ,OAAO,eAAe,GAAG,MAAM,UAAU,OAAO,aAAa,MAAM,QAAQ,GAAG;AAC3J;AACA,SAAS,OAAO,MAAM,MAAM,oBAAI,IAAI,GAAG;AACrC,MAAI,QAAQ,WAAW,GAAG;AAC1B,MAAI,SAAS,QAAQ,QAAQ,KAAK,IAAI,EAAG,QAAO;AAChD,MAAI,CAAC,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,EAAG,QAAO;AAChD,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,QAAI,OAAO,SAAS,IAAI,EAAG,QAAO,KAAK,MAAM,CAAC;AAAA,QAAO,KAAI,IAAI,IAAI;AACjE,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,UAAI,KAAK,CAAC;AACV,WAAK,YAAY,OAAO,GAAG,GAAG,OAAO,EAAG,MAAK,CAAC,IAAI;AAAA,IACpD;AAAA,EACF,OAAO;AACL,QAAI,OAAO,SAAS,IAAI,EAAG,QAAO,OAAO,OAAO,CAAC,GAAG,IAAI;AAAA,QAAO,KAAI,IAAI,IAAI;AAC3E,UAAM,OAAO,OAAO,KAAK,IAAI,GAC3B,OAAO,OAAO,0BAA0B,IAAI;AAC9C,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,aAAO,KAAK,CAAC;AACb,UAAI,KAAK,IAAI,EAAE,IAAK;AACpB,UAAI,KAAK,IAAI;AACb,WAAK,YAAY,OAAO,GAAG,GAAG,OAAO,EAAG,MAAK,IAAI,IAAI;AAAA,IACvD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,QAAQ,OAAO,MAAM;AACzB,MAAI,CAAC,MAAO,QAAO,eAAe,QAAQ,QAAQ;AAAA,IAChD,OAAO,QAAQ,uBAAO,OAAO,IAAI;AAAA,EACnC,CAAC;AACD,SAAO;AACT;AACA,SAAS,QAAQ,OAAO,UAAU,OAAO;AACvC,MAAI,MAAM,QAAQ,EAAG,QAAO,MAAM,QAAQ;AAC1C,QAAM,CAAC,GAAG,GAAG,IAAI,aAAa,OAAO;AAAA,IACnC,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,CAAC;AACD,IAAE,IAAI;AACN,SAAO,MAAM,QAAQ,IAAI;AAC3B;AACA,SAAS,kBAAkB,QAAQ,UAAU;AAC3C,QAAM,OAAO,QAAQ,yBAAyB,QAAQ,QAAQ;AAC9D,MAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,KAAK,gBAAgB,aAAa,UAAU,aAAa,MAAO,QAAO;AACjG,SAAO,KAAK;AACZ,SAAO,KAAK;AACZ,OAAK,MAAM,MAAM,OAAO,MAAM,EAAE,QAAQ;AACxC,SAAO;AACT;AACA,SAAS,UAAU,QAAQ;AACzB,cAAY,KAAK,QAAQ,SAAS,QAAQ,KAAK,GAAG,KAAK,EAAE;AAC3D;AACA,SAAS,QAAQ,QAAQ;AACvB,YAAU,MAAM;AAChB,SAAO,QAAQ,QAAQ,MAAM;AAC/B;AACA,IAAM,eAAe;AAAA,EACnB,IAAI,QAAQ,UAAU,UAAU;AAC9B,QAAI,aAAa,KAAM,QAAO;AAC9B,QAAI,aAAa,OAAQ,QAAO;AAChC,QAAI,aAAa,QAAQ;AACvB,gBAAU,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,UAAM,UAAU,MAAM,QAAQ;AAC9B,QAAI,QAAQ,UAAU,QAAQ,IAAI,OAAO,QAAQ;AACjD,QAAI,aAAa,SAAS,aAAa,QAAQ,aAAa,YAAa,QAAO;AAChF,QAAI,CAAC,SAAS;AACZ,YAAM,OAAO,OAAO,yBAAyB,QAAQ,QAAQ;AAC7D,UAAI,YAAY,MAAM,OAAO,UAAU,cAAc,OAAO,eAAe,QAAQ,MAAM,EAAE,QAAQ,KAAK,KAAM,SAAQ,QAAQ,OAAO,UAAU,KAAK,EAAE;AAAA,IACxJ;AACA,WAAO,YAAY,KAAK,IAAI,OAAO,KAAK,IAAI;AAAA,EAC9C;AAAA,EACA,IAAI,QAAQ,UAAU;AACpB,QAAI,aAAa,QAAQ,aAAa,UAAU,aAAa,UAAU,aAAa,SAAS,aAAa,QAAQ,aAAa,YAAa,QAAO;AACnJ,gBAAY,KAAK,QAAQ,SAAS,QAAQ,IAAI,GAAG,QAAQ,EAAE;AAC3D,WAAO,YAAY;AAAA,EACrB;AAAA,EACA,MAAM;AACJ,YAAQ,KAAK,gCAAgC;AAC7C,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,YAAQ,KAAK,gCAAgC;AAC7C,WAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA,0BAA0B;AAC5B;AACA,SAAS,YAAY,OAAO,UAAU,OAAO,WAAW,OAAO;AAC7D,MAAI,CAAC,YAAY,MAAM,QAAQ,MAAM,MAAO;AAC5C,QAAM,OAAO,MAAM,QAAQ,GACzB,MAAM,MAAM;AACd,WAAS,qBAAqB,SAAS,kBAAkB,OAAO,UAAU,OAAO,IAAI;AACrF,MAAI,UAAU,QAAW;AACvB,WAAO,MAAM,QAAQ;AACrB,QAAI,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE,QAAQ,KAAK,SAAS,OAAW,OAAM,IAAI,EAAE,QAAQ,EAAE,EAAE;AAAA,EAC1F,OAAO;AACL,UAAM,QAAQ,IAAI;AAClB,QAAI,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE,QAAQ,KAAK,SAAS,OAAW,OAAM,IAAI,EAAE,QAAQ,EAAE,EAAE;AAAA,EAC1F;AACA,MAAI,QAAQ,SAAS,OAAO,KAAK,GAC/B;AACF,MAAI,OAAO,QAAQ,OAAO,UAAU,IAAI,EAAG,MAAK,EAAE,MAAM,KAAK;AAC7D,MAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,KAAK;AAChD,aAAS,IAAI,MAAM,QAAQ,IAAI,KAAK,IAAK,EAAC,OAAO,MAAM,CAAC,MAAM,KAAK,EAAE;AACrE,KAAC,OAAO,QAAQ,OAAO,UAAU,GAAG,MAAM,KAAK,EAAE,MAAM,MAAM;AAAA,EAC/D;AACA,GAAC,OAAO,MAAM,KAAK,MAAM,KAAK,EAAE;AAClC;AACA,SAAS,eAAe,OAAO,OAAO;AACpC,QAAM,OAAO,OAAO,KAAK,KAAK;AAC9B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,UAAM,MAAM,KAAK,CAAC;AAClB,gBAAY,OAAO,KAAK,MAAM,GAAG,CAAC;AAAA,EACpC;AACF;AACA,SAAS,YAAY,SAAS,MAAM;AAClC,MAAI,OAAO,SAAS,WAAY,QAAO,KAAK,OAAO;AACnD,SAAO,OAAO,IAAI;AAClB,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,QAAI,YAAY,KAAM;AACtB,QAAI,IAAI,GACN,MAAM,KAAK;AACb,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,QAAQ,CAAC,MAAM,MAAO,aAAY,SAAS,GAAG,KAAK;AAAA,IACzD;AACA,gBAAY,SAAS,UAAU,GAAG;AAAA,EACpC,MAAO,gBAAe,SAAS,IAAI;AACrC;AACA,SAAS,WAAW,SAAS,MAAM,YAAY,CAAC,GAAG;AACjD,MAAI,MACF,OAAO;AACT,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,KAAK,MAAM;AAClB,UAAM,WAAW,OAAO,MACtB,UAAU,MAAM,QAAQ,OAAO;AACjC,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,mBAAW,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,GAAG,SAAS;AAAA,MACvD;AACA;AAAA,IACF,WAAW,WAAW,aAAa,YAAY;AAC7C,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,EAAG,YAAW,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI,GAAG,SAAS;AAAA,MAC1E;AACA;AAAA,IACF,WAAW,WAAW,aAAa,UAAU;AAC3C,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,KAAK,QAAQ,SAAS;AAAA,QACtB,KAAK;AAAA,MACP,IAAI;AACJ,eAAS,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AACnC,mBAAW,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI,GAAG,SAAS;AAAA,MACjD;AACA;AAAA,IACF,WAAW,KAAK,SAAS,GAAG;AAC1B,iBAAW,QAAQ,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,OAAO,SAAS,CAAC;AACxD;AAAA,IACF;AACA,WAAO,QAAQ,IAAI;AACnB,gBAAY,CAAC,IAAI,EAAE,OAAO,SAAS;AAAA,EACrC;AACA,MAAI,QAAQ,KAAK,CAAC;AAClB,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAQ,MAAM,MAAM,SAAS;AAC7B,QAAI,UAAU,KAAM;AAAA,EACtB;AACA,MAAI,SAAS,UAAa,SAAS,OAAW;AAC9C,UAAQ,OAAO,KAAK;AACpB,MAAI,SAAS,UAAa,YAAY,IAAI,KAAK,YAAY,KAAK,KAAK,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC1F,mBAAe,MAAM,KAAK;AAAA,EAC5B,MAAO,aAAY,SAAS,MAAM,KAAK;AACzC;AACA,SAAS,eAAe,CAAC,OAAO,OAAO,GAAG;AACxC,QAAM,iBAAiB,OAAO,SAAS,CAAC,CAAC;AACzC,QAAM,UAAU,MAAM,QAAQ,cAAc;AAC5C,MAAI,OAAO,mBAAmB,YAAY,OAAO,mBAAmB,WAAY,OAAM,IAAI,MAAM,mBAAmB,OAAO,cAAc,gEAAgE;AACxM,QAAM,eAAe,OAAO,cAAc;AAC1C,MAAM,cAAc;AAAA,IAClB,OAAO;AAAA,IACP,MAAM,WAAW,QAAQ;AAAA,EAC3B,CAAC;AACD,WAAS,YAAY,MAAM;AACzB,UAAM,MAAM;AACV,iBAAW,KAAK,WAAW,IAAI,YAAY,gBAAgB,KAAK,CAAC,CAAC,IAAI,WAAW,gBAAgB,IAAI;AAAA,IACvG,CAAC;AAAA,EACH;AACA,SAAO,CAAC,cAAc,QAAQ;AAChC;AAEA,SAAS,gBAAgB,QAAQ,UAAU;AACzC,QAAM,OAAO,QAAQ,yBAAyB,QAAQ,QAAQ;AAC9D,MAAI,CAAC,QAAQ,KAAK,OAAO,KAAK,OAAO,CAAC,KAAK,gBAAgB,aAAa,UAAU,aAAa,MAAO,QAAO;AAC7G,SAAO,KAAK;AACZ,SAAO,KAAK;AACZ,OAAK,MAAM,MAAM,OAAO,MAAM,EAAE,QAAQ;AACxC,OAAK,MAAM,OAAK,OAAO,MAAM,EAAE,QAAQ,IAAI;AAC3C,SAAO;AACT;AACA,IAAM,aAAa;AAAA,EACjB,IAAI,QAAQ,UAAU,UAAU;AAC9B,QAAI,aAAa,KAAM,QAAO;AAC9B,QAAI,aAAa,OAAQ,QAAO;AAChC,QAAI,aAAa,QAAQ;AACvB,gBAAU,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,UAAM,UAAU,MAAM,QAAQ;AAC9B,QAAI,QAAQ,UAAU,QAAQ,IAAI,OAAO,QAAQ;AACjD,QAAI,aAAa,SAAS,aAAa,QAAQ,aAAa,YAAa,QAAO;AAChF,QAAI,CAAC,SAAS;AACZ,YAAM,OAAO,OAAO,yBAAyB,QAAQ,QAAQ;AAC7D,YAAM,aAAa,OAAO,UAAU;AACpC,UAAI,YAAY,MAAM,CAAC,cAAc,OAAO,eAAe,QAAQ,MAAM,EAAE,QAAQ,KAAK,KAAM,SAAQ,QAAQ,OAAO,UAAU,KAAK,EAAE;AAAA,eAAW,SAAS,QAAQ,cAAc,UAAU,MAAM,UAAU,QAAQ,GAAG;AACnN,eAAO,IAAI,SAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,EAAE,MAAM,UAAU,IAAI,CAAC;AAAA,MACjF;AAAA,IACF;AACA,WAAO,YAAY,KAAK,IAAI,KAAK,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,IAAI,QAAQ,UAAU;AACpB,QAAI,aAAa,QAAQ,aAAa,UAAU,aAAa,UAAU,aAAa,SAAS,aAAa,QAAQ,aAAa,YAAa,QAAO;AACnJ,gBAAY,KAAK,QAAQ,SAAS,QAAQ,IAAI,GAAG,QAAQ,EAAE;AAC3D,WAAO,YAAY;AAAA,EACrB;AAAA,EACA,IAAI,QAAQ,UAAU,OAAO;AAC3B,UAAM,MAAM,YAAY,QAAQ,UAAU,OAAO,KAAK,CAAC,CAAC;AACxD,WAAO;AAAA,EACT;AAAA,EACA,eAAe,QAAQ,UAAU;AAC/B,UAAM,MAAM,YAAY,QAAQ,UAAU,QAAW,IAAI,CAAC;AAC1D,WAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA,0BAA0B;AAC5B;AACA,SAAS,KAAK,OAAO;AACnB,MAAI,IAAI,MAAM,MAAM;AACpB,MAAI,CAAC,GAAG;AACN,WAAO,eAAe,OAAO,QAAQ;AAAA,MACnC,OAAO,IAAI,IAAI,MAAM,OAAO,UAAU;AAAA,IACxC,CAAC;AACD,UAAM,OAAO,OAAO,KAAK,KAAK,GAC5B,OAAO,OAAO,0BAA0B,KAAK;AAC/C,UAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,UAAM,UAAU,UAAU,QAAQ,UAAU,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,KAAK,UAAU,OAAO;AAC3H,QAAI,SAAS;AACX,UAAI,WAAW;AACf,aAAO,YAAY,MAAM;AACvB,cAAM,cAAc,OAAO,0BAA0B,QAAQ;AAC7D,aAAK,KAAK,GAAG,OAAO,KAAK,WAAW,CAAC;AACrC,eAAO,OAAO,MAAM,WAAW;AAC/B,mBAAW,OAAO,eAAe,QAAQ;AAAA,MAC3C;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,YAAM,OAAO,KAAK,CAAC;AACnB,UAAI,WAAW,SAAS,cAAe;AACvC,UAAI,KAAK,IAAI,EAAE,KAAK;AAClB,cAAM,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC;AACjC,eAAO,eAAe,OAAO,MAAM;AAAA,UACjC;AAAA,UACA,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,IAAI,EAAE,KAAK;AAClB,cAAM,KAAK,KAAK,IAAI,EAAE,KACpB,MAAM,OAAK,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;AACtC,eAAO,eAAe,OAAO,MAAM;AAAA,UACjC;AAAA,UACA,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,OAAO,SAAS;AACrC,QAAM,iBAAiB,OAAO,SAAS,CAAC,CAAC;AACzC,MAAI,OAAO,mBAAmB,YAAY,OAAO,mBAAmB,WAAY,OAAM,IAAI,MAAM,mBAAmB,OAAO,cAAc,kEAAkE;AAC1M,QAAM,eAAe,KAAK,cAAc;AACxC,MAAM,cAAc;AAAA,IAClB,OAAO;AAAA,IACP,MAAM,WAAW,QAAQ;AAAA,EAC3B,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc,OAAO,UAAU;AACtC,QAAM,MAAM,SAAS,OAAO,KAAK,CAAC,CAAC;AACrC;AAEA,IAAM,QAAQ,OAAO,YAAY;AACjC,SAAS,WAAW,QAAQ,QAAQ,UAAU,OAAO,KAAK;AACxD,QAAM,WAAW,OAAO,QAAQ;AAChC,MAAI,WAAW,SAAU;AACzB,QAAM,UAAU,MAAM,QAAQ,MAAM;AACpC,MAAI,aAAa,UAAU,CAAC,YAAY,MAAM,KAAK,CAAC,YAAY,QAAQ,KAAK,YAAY,MAAM,QAAQ,QAAQ,KAAK,OAAO,OAAO,GAAG,MAAM,SAAS,GAAG,IAAI;AACzJ,gBAAY,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,SAAS;AACX,QAAI,OAAO,UAAU,SAAS,WAAW,CAAC,SAAS,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG,KAAK,OAAO;AAC9F,UAAI,GAAG,GAAG,OAAO,KAAK,QAAQ,MAAM,gBAAgB;AACpD,WAAK,QAAQ,GAAG,MAAM,KAAK,IAAI,SAAS,QAAQ,OAAO,MAAM,GAAG,QAAQ,QAAQ,SAAS,KAAK,MAAM,OAAO,KAAK,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,KAAK,KAAK,SAAS,KAAK,EAAE,GAAG,KAAK,SAAS,KAAK,EAAE,GAAG,MAAM,OAAO,KAAK,EAAE,GAAG,IAAI,SAAS;AAC7O,mBAAW,OAAO,KAAK,GAAG,UAAU,OAAO,OAAO,GAAG;AAAA,MACvD;AACA,YAAM,OAAO,IAAI,MAAM,OAAO,MAAM,GAClC,aAAa,oBAAI,IAAI;AACvB,WAAK,MAAM,SAAS,SAAS,GAAG,SAAS,OAAO,SAAS,GAAG,OAAO,SAAS,UAAU,UAAU,SAAS,GAAG,MAAM,OAAO,MAAM,KAAK,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,KAAK,SAAS,GAAG,EAAE,GAAG,KAAK,SAAS,GAAG,EAAE,GAAG,MAAM,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,UAAU;AAChQ,aAAK,MAAM,IAAI,SAAS,GAAG;AAAA,MAC7B;AACA,UAAI,QAAQ,UAAU,QAAQ,KAAK;AACjC,aAAK,IAAI,OAAO,KAAK,QAAQ,IAAK,aAAY,UAAU,GAAG,OAAO,CAAC,CAAC;AACpE,eAAO,IAAI,OAAO,QAAQ,KAAK;AAC7B,sBAAY,UAAU,GAAG,KAAK,CAAC,CAAC;AAChC,qBAAW,OAAO,CAAC,GAAG,UAAU,GAAG,OAAO,GAAG;AAAA,QAC/C;AACA,YAAI,SAAS,SAAS,OAAO,OAAQ,aAAY,UAAU,UAAU,OAAO,MAAM;AAClF;AAAA,MACF;AACA,uBAAiB,IAAI,MAAM,SAAS,CAAC;AACrC,WAAK,IAAI,QAAQ,KAAK,OAAO,KAAK;AAChC,eAAO,OAAO,CAAC;AACf,iBAAS,OAAO,OAAO,KAAK,GAAG,IAAI;AACnC,YAAI,WAAW,IAAI,MAAM;AACzB,uBAAe,CAAC,IAAI,MAAM,SAAY,KAAK;AAC3C,mBAAW,IAAI,QAAQ,CAAC;AAAA,MAC1B;AACA,WAAK,IAAI,OAAO,KAAK,KAAK,KAAK;AAC7B,eAAO,SAAS,CAAC;AACjB,iBAAS,OAAO,OAAO,KAAK,GAAG,IAAI;AACnC,YAAI,WAAW,IAAI,MAAM;AACzB,YAAI,MAAM,UAAa,MAAM,IAAI;AAC/B,eAAK,CAAC,IAAI,SAAS,CAAC;AACpB,cAAI,eAAe,CAAC;AACpB,qBAAW,IAAI,QAAQ,CAAC;AAAA,QAC1B;AAAA,MACF;AACA,WAAK,IAAI,OAAO,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,KAAK,MAAM;AACb,sBAAY,UAAU,GAAG,KAAK,CAAC,CAAC;AAChC,qBAAW,OAAO,CAAC,GAAG,UAAU,GAAG,OAAO,GAAG;AAAA,QAC/C,MAAO,aAAY,UAAU,GAAG,OAAO,CAAC,CAAC;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,mBAAW,OAAO,CAAC,GAAG,UAAU,GAAG,OAAO,GAAG;AAAA,MAC/C;AAAA,IACF;AACA,QAAI,SAAS,SAAS,OAAO,OAAQ,aAAY,UAAU,UAAU,OAAO,MAAM;AAClF;AAAA,EACF;AACA,QAAM,aAAa,OAAO,KAAK,MAAM;AACrC,WAAS,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,KAAK;AACrD,eAAW,OAAO,WAAW,CAAC,CAAC,GAAG,UAAU,WAAW,CAAC,GAAG,OAAO,GAAG;AAAA,EACvE;AACA,QAAM,eAAe,OAAO,KAAK,QAAQ;AACzC,WAAS,IAAI,GAAG,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK;AACvD,QAAI,OAAO,aAAa,CAAC,CAAC,MAAM,OAAW,aAAY,UAAU,aAAa,CAAC,GAAG,MAAS;AAAA,EAC7F;AACF;AACA,SAAS,UAAU,OAAO,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACF;AAAA,IACA,MAAM;AAAA,EACR,IAAI,SACJ,IAAI,OAAO,KAAK;AAClB,SAAO,WAAS;AACd,QAAI,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY,CAAC,EAAG,QAAO;AACnD,UAAM,MAAM,WAAW,GAAG;AAAA,MACxB,CAAC,KAAK,GAAG;AAAA,IACX,GAAG,OAAO,OAAO,GAAG;AACpB,WAAO,QAAQ,SAAY,QAAQ;AAAA,EACrC;AACF;AACA,IAAM,YAAY,oBAAI,QAAQ;AAC9B,IAAM,cAAc;AAAA,EAClB,IAAI,QAAQ,UAAU;AACpB,QAAI,aAAa,KAAM,QAAO;AAC9B,UAAM,QAAQ,OAAO,QAAQ;AAC7B,QAAI;AACJ,WAAO,YAAY,KAAK,IAAI,UAAU,IAAI,KAAK,MAAM,UAAU,IAAI,OAAO,QAAQ,IAAI,MAAM,OAAO,WAAW,CAAC,GAAG,SAAS;AAAA,EAC7H;AAAA,EACA,IAAI,QAAQ,UAAU,OAAO;AAC3B,gBAAY,QAAQ,UAAU,OAAO,KAAK,CAAC;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,eAAe,QAAQ,UAAU;AAC/B,gBAAY,QAAQ,UAAU,QAAW,IAAI;AAC7C,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,IAAI;AACnB,SAAO,WAAS;AACd,QAAI,YAAY,KAAK,GAAG;AACtB,UAAI;AACJ,UAAI,EAAE,QAAQ,UAAU,IAAI,KAAK,IAAI;AACnC,kBAAU,IAAI,OAAO,QAAQ,IAAI,MAAM,OAAO,WAAW,CAAC;AAAA,MAC5D;AACA,SAAG,KAAK;AAAA,IACV;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAMA,OAAM;AAAA,EACV;AAAA,EACA;AAAA,EACA,OAAO;AACT;", "names": ["DEV"]}