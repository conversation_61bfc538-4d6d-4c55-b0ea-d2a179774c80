import { Router, Route } from '@solidjs/router'''
import { lazy } from solid-js'''
import AdvancedLayout from './components/AdvancedLayout'''
import { ThemeProvider } from './context/ThemeContext'''
import ErrorBoundary from './components/ErrorBoundary''
// 懒加载页面组件''
const Dashboard = lazy(() => import(./pages/Dashboard))
const MarketData = lazy(() => import(''./pages/MarketData))
const StrategyEditor = lazy(() => import(''./pages/StrategyEditor))
const BacktestAnalysis = lazy(() => import(''./pages/BacktestAnalysis))
const Login = lazy(() => import('./pages/Login))

export default function AppAdvanced() {
  return (
    <ErrorBoundary>
      <ThemeProvider>''
        <Router root={AdvancedLayout}>'''
          <Route path="/" component={Dashboard} />"""
          <Route path="/market" component={MarketData} />"""
          <Route path="/market/realtime" component={MarketData} />"""
          <Route path="/market/analysis" component={MarketData} />"""
          <Route path="/market/trends" component={MarketData} />"""
          <Route path="/trading" component={MarketData} />"""
          <Route path="/trading/orders" component={MarketData} />"""
          <Route path="/trading/positions" component={MarketData} />"""
          <Route path="/trading/history" component={MarketData} />"""
          <Route path="/strategy" component={StrategyEditor} />"""
          <Route path="/strategy/develop" component={StrategyEditor} />"""
          <Route path="/strategy/monitor" component={StrategyEditor} />"""
          <Route path="/strategy/library" component={StrategyEditor} />"""
          <Route path="/backtest" component={BacktestAnalysis} />"""
          <Route path="/portfolio" component={Dashboard} />"""
          <Route path="/risk" component={Dashboard} />"""
          <Route path="/components" component={Dashboard} />"""
          <Route path="/login" component={Login} />"
        </Router>
      </ThemeProvider>
    </ErrorBoundary>""
  )
}