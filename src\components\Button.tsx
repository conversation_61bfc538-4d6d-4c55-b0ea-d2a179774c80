import { Component, JSX, splitProps } from 'solid-js';
import { css, cx } from '../../styled-system/css';

type ButtonVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  icon?: JSX.Element;
  fullWidth?: boolean;
}

const Button: Component<ButtonProps> = (props) => {
  const [local, others] = splitProps(props, [
    'variant', 
    'size', 
    'loading', 
    'icon', 
    'fullWidth', 
    'children', 
    'class',
    'disabled'
  ]);

  const baseStyles = css({
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    borderRadius: '8px',
    fontWeight: '500',
    transition: 'all 0.2s',
    cursor: 'pointer',
    border: 'none',
    outline: 'none',
    textDecoration: 'none',
    userSelect: 'none',
    _focus: {
      boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
    },
    _disabled: {
      opacity: 0.6,
      cursor: 'not-allowed',
    },
  });

  const variantStyles = {
    primary: css({
      backgroundColor: 'blue.600',
      color: 'white',
      _hover: {
        backgroundColor: 'blue.700',
      },
      _active: {
        backgroundColor: 'blue.800',
      },
    }),
    secondary: css({
      backgroundColor: 'gray.100',
      color: 'gray.900',
      _hover: {
        backgroundColor: 'gray.200',
      },
      _active: {
        backgroundColor: 'gray.300',
      },
    }),
    success: css({
      backgroundColor: 'green.600',
      color: 'white',
      _hover: {
        backgroundColor: 'green.700',
      },
      _active: {
        backgroundColor: 'green.800',
      },
    }),
    warning: css({
      backgroundColor: 'yellow.500',
      color: 'white',
      _hover: {
        backgroundColor: 'yellow.600',
      },
      _active: {
        backgroundColor: 'yellow.700',
      },
    }),
    danger: css({
      backgroundColor: 'red.600',
      color: 'white',
      _hover: {
        backgroundColor: 'red.700',
      },
      _active: {
        backgroundColor: 'red.800',
      },
    }),
    ghost: css({
      backgroundColor: 'transparent',
      color: 'gray.700',
      border: '1px solid',
      borderColor: 'gray.300',
      _hover: {
        backgroundColor: 'gray.50',
        borderColor: 'gray.400',
      },
      _active: {
        backgroundColor: 'gray.100',
      },
    }),
  };

  const sizeStyles = {
    sm: css({
      padding: '6px 12px',
      fontSize: '14px',
      minHeight: '32px',
    }),
    md: css({
      padding: '8px 16px',
      fontSize: '14px',
      minHeight: '40px',
    }),
    lg: css({
      padding: '12px 24px',
      fontSize: '16px',
      minHeight: '48px',
    }),
  };

  const fullWidthStyle = css({
    width: '100%',
  });

  const loadingSpinner = css({
    width: '16px',
    height: '16px',
    border: '2px solid currentColor',
    borderTopColor: 'transparent',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
  });

  const variant = local.variant || 'primary';
  const size = local.size || 'md';

  return (
    <button
      class={cx(
        baseStyles,
        variantStyles[variant],
        sizeStyles[size],
        local.fullWidth && fullWidthStyle,
        local.class
      )}
      disabled={local.disabled || local.loading}
      {...others}
    >
      {local.loading && <div class={loadingSpinner} />}
      {!local.loading && local.icon && local.icon}
      {local.children}
    </button>
  );
};

export default Button;
