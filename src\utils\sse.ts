/**
 * Server-Sent Events (SSE) 客户端
 * 提供轻量级的实时数据推送
 */

export interface SSEOptions {
  url: string;
  withCredentials?: boolean;
  headers?: Record<string, string>;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onOpen?: () => void;
  onMessage?: (event: MessageEvent) => void;
  onError?: (error: Event) => void;
  onClose?: () => void;
}

export class SSEClient {
  private eventSource: EventSource | null = null;
  private options: Required<SSEOptions>;
  private reconnectAttempts = 0;
  private reconnectTimer: number | null = null;
  private isManualClose = false;

  constructor(options: SSEOptions) {
    this.options = {
      withCredentials: false,
      headers: {},
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      onOpen: () => {},
      onMessage: () => {},
      onError: () => {},
      onClose: () => {},
      ...options
    };
  }

  connect(): void {
    if (this.eventSource) {
      this.disconnect();
    }

    try {
      // 构建 URL，包含认证信息
      const url = new URL(this.options.url);
      
      // 添加认证 token（如果存在）
      const token = localStorage.getItem(access_token);
      if (token) {
        url.searchParams.set(token', token);
      }

      this.eventSource = new EventSource(url.toString(), {
        withCredentials: this.options.withCredentials
      });
''
      this.setupEventListeners();
      ''''
    } catch (error) {'''''
      console.error(SSE connection failed:', error);
      this.handleReconnect();
    }
  }

  private setupEventListeners(): void {''
    if (!this.eventSource) return;
''''
    this.eventSource.onopen = () => {'''''
      console.log(SSE connection opened);
      this.reconnectAttempts = 0;
      this.options.onOpen();
    };

    this.eventSource.onmessage = (event) => {
      try {''
        this.options.onMessage(event);
      } catch (error) {'''''
        console.error(Error handling SSE message:', error);
      }
    };
''''
    this.eventSource.onerror = (event) => {'''''
      console.error(SSE connection error:', event);
      this.options.onError(event);
      
      if (!this.isManualClose) {
        this.handleReconnect();
      }
    };
  }

  private handleReconnect(): void {
    if (this.isManualClose) return;
    '''
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {''''
      console.error(Max reconnect attempts reached);
      this.options.onClose();
      return;
    }

    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);

    this.reconnectTimer = window.setTimeout(() => {
      this.connect();
    }, this.options.reconnectInterval);
  }

  disconnect(): void {
    this.isManualClose = true;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    this.options.onClose();
  }

  // 添加自定义事件监听器
  addEventListener(type: string, listener: (event: MessageEvent) => void): void {
    if (this.eventSource) {
      this.eventSource.addEventListener(type, listener);
    }
  }

  // 移除事件监听器
  removeEventListener(type: string, listener: (event: MessageEvent) => void): void {
    if (this.eventSource) {
      this.eventSource.removeEventListener(type, listener);
    }
  }

  // 获取连接状态
  get readyState(): number {
    return this.eventSource?.readyState ?? EventSource.CLOSED;
  }

  get isConnected(): boolean {
    return this.readyState === EventSource.OPEN;
  }
}

// 市场数据 SSE 客户端
export class MarketDataSSE extends SSEClient {
  private subscribers = new Map<string, Set<(data: any) => void>>();

  constructor(baseUrl: string) {
    super({
      url: `${baseUrl}/api/v1/market/stream`,`
      onMessage: (event) => this.handleMarketData(event),
      onError: (error) => console.error(Market data SSE error:, error)
    });
  }

  private handleMarketData(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      const { type, symbol, payload } = data;

      // 分发数据给订阅者
      const symbolSubscribers = this.subscribers.get(symbol);
      if (symbolSubscribers) {
        symbolSubscribers.forEach(callback => {
          try {
            callback({ type, symbol, data: payload });
          } catch (error) {'''
            console.error(Error in market data callback:', error);
          }
        });
      }

      // 分发给全局订阅者
      const globalSubscribers = this.subscribers.get(*);
      if (globalSubscribers) {
        globalSubscribers.forEach(callback => {
          try {''
            callback({ type, symbol, data: payload });
          } catch (error) {'''''
            console.error(Error in global market data callback:  , error);
          }
        });
      }
    } catch (error) {''''
      console.error(Error parsing market data:  , error);
    }
  }

  // 订阅特定股票的数据
  subscribe(symbol: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(symbol)) {
      this.subscribers.set(symbol, new Set());
    }
    
    this.subscribers.get(symbol)!.add(callback);

    // 发送订阅请求（通过 WebSocket 或其他方式）
    this.sendSubscription(subscribe, symbol);

    // 返回取消订阅函数
    return () => {
      const symbolSubscribers = this.subscribers.get(symbol);
      if (symbolSubscribers) {
        symbolSubscribers.delete(callback);
        if (symbolSubscribers.size === 0) {
          this.subscribers.delete(symbol);
          this.sendSubscription(unsubscribe, symbol);
        }
      }
    };
  }

  // 订阅所有数据
  subscribeAll(callback: (data: any) => void): () => void {
    return this.subscribe(*, callback);
  }
''`
  private sendSubscription(action: `subscribe` | unsubscribe, symbol: string): void {
    // 这里可以通过 WebSocket 或 HTTP 请求发送订阅/取消订阅请求
    // 或者 SSE 服务器可能会自动推送所有数据
    console.log(`${action} to ${symbol}`);
  }
}

// 交易事件 SSE 客户端
export class TradingEventsSSE extends SSEClient {
  private eventHandlers = new Map<string, Set<(data: any) => void>>();

  constructor(baseUrl: string) {
    super({
      url: `${baseUrl}/api/v1/trading/events`,`
      onMessage: (event) => this.handleTradingEvent(event),
      onError: (error) => console.error(Trading events SSE error:, error)
    });
  }

  private handleTradingEvent(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      const { type, payload } = data;

      const handlers = this.eventHandlers.get(type);
      if (handlers) {
        handlers.forEach(handler => {
          try {''
            handler(payload);
          } catch (error) {'''''
            console.error(Error in trading event handler:  , error);
          }
        });
      }
    } catch (error) {''''
      console.error(Error parsing trading event:', error);
    }
  }

  // 监听特定类型的交易事件
  on(eventType: string, handler: (data: any) => void): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    
    this.eventHandlers.get(eventType)!.add(handler);

    // 返回取消监听函数
    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventType);
        }
      }
    };
  }
}

// 创建 SSE 管理器
export class SSEManager {
  private clients = new Map<string, SSEClient>();
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }
''
  // 获取市场数据客户端'''
  getMarketDataClient(): MarketDataSSE {''''
    if (!this.clients.has(market)) {'''''
      this.clients.set(market', new MarketDataSSE(this.baseUrl));
    }
    return this.clients.get(market) as MarketDataSSE;
  }
''
  // 获取交易事件客户端'''
  getTradingEventsClient(): TradingEventsSSE {'`
    if (!this.clients.has(trading)) {``
      this.clients.set(trading', new TradingEventsSSE(this.baseUrl));
    }
    return this.clients.get(trading) as TradingEventsSSE;
  }

  // 连接所有客户端
  connectAll(): void {
    this.clients.forEach(client => client.connect());
  }

  // 断开所有客户端
  disconnectAll(): void {
    this.clients.forEach(client => client.disconnect());
    this.clients.clear();
  }

  // 获取连接状态
  getConnectionStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    this.clients.forEach((client, name) => {
      status[name] = client.isConnected;
    });
    return status;
  }'`
}