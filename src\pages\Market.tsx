import { createSignal, onMount, onCleanup } from 'solid-js';
import { css } from '../../styled-system/css';

interface MarketData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  marketCap?: number;
}

export default function Market() {
  const [marketData, setMarketData] = createSignal<MarketData[]>([
    {
      symbol: 'AAPL',
      name: '苹果公司',
      price: 150.25,
      change: 2.15,
      changePercent: 1.45,
      volume: 1200000,
      high: 152.30,
      low: 148.90,
      open: 149.50,
      marketCap: 2500000000000
    },
    {
      symbol: 'TSLA',
      name: '特斯拉',
      price: 245.80,
      change: -3.25,
      changePercent: -1.30,
      volume: 2800000,
      high: 250.10,
      low: 244.50,
      open: 248.90,
      marketCap: 780000000000
    },
    {
      symbol: 'MSFT',
      name: '微软',
      price: 310.45,
      change: 2.85,
      changePercent: 0.93,
      volume: 1500000,
      high: 312.00,
      low: 308.20,
      open: 309.10,
      marketCap: 2300000000000
    },
    {
      symbol: 'GOOGL',
      name: '谷歌',
      price: 2650.30,
      change: 38.45,
      changePercent: 1.47,
      volume: 800000,
      high: 2665.00,
      low: 2635.50,
      open: 2640.00,
      marketCap: 1700000000000
    },
    {
      symbol: 'AMZN',
      name: '亚马逊',
      price: 3245.67,
      change: -15.23,
      changePercent: -0.47,
      volume: 950000,
      high: 3260.00,
      low: 3230.50,
      open: 3255.00,
      marketCap: 1650000000000
    }
  ]);

  const [selectedSymbol, setSelectedSymbol] = createSignal('AAPL');
  const [searchQuery, setSearchQuery] = createSignal('');

  // 模拟实时数据更新
  let updateInterval: NodeJS.Timeout;

  onMount(() => {
    console.log('Market page mounted');
    
    // 每5秒更新一次数据
    updateInterval = setInterval(() => {
      setMarketData(prev => prev.map(item => ({
        ...item,
        price: item.price + (Math.random() - 0.5) * 2,
        change: item.change + (Math.random() - 0.5) * 0.5,
        changePercent: item.changePercent + (Math.random() - 0.5) * 0.2,
        volume: item.volume + Math.floor((Math.random() - 0.5) * 100000)
      })));
    }, 5000);
  });

  onCleanup(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  const filteredData = () => {
    const query = searchQuery().toLowerCase();
    return marketData().filter(item => 
      item.symbol.toLowerCase().includes(query) || 
      item.name.toLowerCase().includes(query)
    );
  };

  const formatNumber = (num: number, decimals = 2) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num);
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  const formatMarketCap = (cap?: number) => {
    if (!cap) return '-';
    if (cap >= 1000000000000) {
      return `${(cap / 1000000000000).toFixed(2)}T`;
    } else if (cap >= 1000000000) {
      return `${(cap / 1000000000).toFixed(2)}B`;
    }
    return `${(cap / 1000000).toFixed(2)}M`;
  };

  return (
    <div class={css({
      padding: '24px',
      maxWidth: '1400px',
      margin: '0 auto'
    })}>
      {/* 页面标题 */}
      <div class={css({
        marginBottom: '32px'
      })}>
        <h1 class={css({
          fontSize: '32px',
          fontWeight: 'bold',
          color: 'gray.900',
          marginBottom: '8px'
        })}>
          📈 市场行情
        </h1>
        <p class={css({
          fontSize: '16px',
          color: 'gray.600'
        })}>
          实时股票行情数据和市场分析
        </p>
      </div>

      {/* 搜索和筛选 */}
      <div class={css({
        marginBottom: '24px',
        display: 'flex',
        gap: '16px',
        alignItems: 'center'
      })}>
        <input
          type="text"
          placeholder="搜索股票代码或名称..."
          value={searchQuery()}
          onInput={(e) => setSearchQuery(e.currentTarget.value)}
          class={css({
            padding: '12px 16px',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            fontSize: '14px',
            width: '300px',
            _focus: {
              outline: 'none',
              borderColor: 'blue.500',
              boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)'
            }
          })}
        />
        
        <button class={css({
          padding: '12px 24px',
          backgroundColor: 'blue.600',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.2s',
          _hover: {
            backgroundColor: 'blue.700'
          }
        })}>
          刷新数据
        </button>
      </div>

      {/* 市场数据表格 */}
      <div class={css({
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        overflow: 'hidden'
      })}>
        <div class={css({
          overflowX: 'auto'
        })}>
          <table class={css({
            width: '100%',
            borderCollapse: 'collapse'
          })}>
            <thead>
              <tr class={css({
                backgroundColor: 'gray.50'
              })}>
                <th class={css({
                  padding: '16px',
                  textAlign: 'left',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  股票
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  价格
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  涨跌额
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  涨跌幅
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  成交量
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  最高
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  最低
                </th>
                <th class={css({
                  padding: '16px',
                  textAlign: 'right',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: 'gray.700'
                })}>
                  市值
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredData().map((item) => (
                <tr 
                  class={css({
                    borderBottom: '1px solid #e5e7eb',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s',
                    backgroundColor: selectedSymbol() === item.symbol ? 'blue.50' : 'transparent',
                    _hover: {
                      backgroundColor: 'gray.50'
                    }
                  })}
                  onClick={() => setSelectedSymbol(item.symbol)}
                >
                  <td class={css({
                    padding: '16px'
                  })}>
                    <div>
                      <div class={css({
                        fontSize: '14px',
                        fontWeight: '600',
                        color: 'gray.900'
                      })}>
                        {item.symbol}
                      </div>
                      <div class={css({
                        fontSize: '12px',
                        color: 'gray.500'
                      })}>
                        {item.name}
                      </div>
                    </div>
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: 'gray.900'
                  })}>
                    ${formatNumber(item.price)}
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: item.change >= 0 ? 'green.600' : 'red.600'
                  })}>
                    {item.change >= 0 ? '+' : ''}{formatNumber(item.change)}
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: item.changePercent >= 0 ? 'green.600' : 'red.600'
                  })}>
                    {item.changePercent >= 0 ? '+' : ''}{formatNumber(item.changePercent)}%
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: 'gray.600'
                  })}>
                    {formatVolume(item.volume)}
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: 'gray.600'
                  })}>
                    ${formatNumber(item.high)}
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: 'gray.600'
                  })}>
                    ${formatNumber(item.low)}
                  </td>
                  <td class={css({
                    padding: '16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: 'gray.600'
                  })}>
                    ${formatMarketCap(item.marketCap)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 选中股票的详细信息 */}
      {selectedSymbol() && (
        <div class={css({
          marginTop: '24px',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb',
          padding: '24px'
        })}>
          <h3 class={css({
            fontSize: '20px',
            fontWeight: 'bold',
            color: 'gray.900',
            marginBottom: '16px'
          })}>
            {selectedSymbol()} 详细信息
          </h3>
          <p class={css({
            color: 'gray.600'
          })}>
            这里可以显示选中股票的K线图、技术指标、新闻资讯等详细信息。
          </p>
        </div>
      )}
    </div>
  );
}
