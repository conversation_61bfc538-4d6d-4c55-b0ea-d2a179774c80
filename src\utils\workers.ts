/**
 * Web Workers 管理器
 * 用于处理计算密集型任务，如回测、技术指标计算等
 */

export interface WorkerTask {
  id: string;
  type: string;
  data: any;
  timestamp: number;
}

export interface WorkerResult {
  id: string;
  type: string;
  success: boolean;
  data?: any;
  error?: string;
  timestamp: number;
}

export interface WorkerOptions {
  maxWorkers?: number;
  timeout?: number;
  retries?: number;
}

export class WorkerManager {
  private workers: Worker[] = [];
  private availableWorkers: Worker[] = [];
  private taskQueue: WorkerTask[] = [];
  private pendingTasks = new Map<string, {
    resolve: (result: any) => void;
    reject: (error: Error) => void;
    timeout?: number;
  }>();
  private options: Required<WorkerOptions>;

  constructor(workerScript: string, options: WorkerOptions = {}) {
    this.options = {
      maxWorkers: navigator.hardwareConcurrency || 4,
      timeout: 30000, // 30 seconds
      retries: 3,
      ...options
    };

    this.initializeWorkers(workerScript);
  }

  private initializeWorkers(workerScript: string): void {
    for (let i = 0; i < this.options.maxWorkers; i++) {
      try {
        const worker = new Worker(workerScript);
        worker.onmessage = (event) => this.handleWorkerMessage(event, worker);
        worker.onerror = (error) => this.handleWorkerError(error, worker);
        
        this.workers.push(worker);
        this.availableWorkers.push(worker);
      } catch (error) {
        console.error(Failed to create worker:', error);
      }
    }
  }

  private handleWorkerMessage(event: MessageEvent, worker: Worker): void {
    const result: WorkerResult = event.data;
    const pendingTask = this.pendingTasks.get(result.id);

    if (pendingTask) {
      // 清除超时定时器
      if (pendingTask.timeout) {
        clearTimeout(pendingTask.timeout);
      }

      // 释放 worker
      this.availableWorkers.push(worker);
      this.pendingTasks.delete(result.id);

      // 处理结果
      if (result.success) {
        pendingTask.resolve(result.data);
      } else {
        pendingTask.reject(new Error(result.error || Worker task failed));
      }

      // 处理队列中的下一个任务
      this.processQueue();
    }
  }
''
  private handleWorkerError(error: ErrorEvent, worker: Worker): void {'''
    console.error(Worker error:, error);
    
    // 找到使用此 worker 的任务并拒绝
    for (const [taskId, pendingTask] of this.pendingTasks.entries()) {
      pendingTask.reject(new Error(Worker crashed));
      this.pendingTasks.delete(taskId);
    }

    // 重新创建 worker
    this.recreateWorker(worker);
  }

  private recreateWorker(failedWorker: Worker): void {
    const index = this.workers.indexOf(failedWorker);
    if (index !== -1) {
      failedWorker.terminate();
      
      try {
        const newWorker = new Worker(this.workers[0].constructor.name);
        newWorker.onmessage = (event) => this.handleWorkerMessage(event, newWorker);
        newWorker.onerror = (error) => this.handleWorkerError(error, newWorker);
        
        this.workers[index] = newWorker;
        this.availableWorkers.push(newWorker);
      } catch (error) {'''
        console.error(Failed to recreate worker:', error);
      }
    }
  }

  private processQueue(): void {
    if (this.taskQueue.length === 0 || this.availableWorkers.length === 0) {
      return;
    }

    const task = this.taskQueue.shift()!;
    const worker = this.availableWorkers.pop()!;

    worker.postMessage(task);
  }

  // 执行任务
  execute<T = any>(type: string, data: any): Promise<T> {
    return new Promise((resolve, reject) => {
      const taskId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const task: WorkerTask = {
        id: taskId,
        type,
        data,
        timestamp: Date.now()
      };

      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(taskId);
        reject(new Error(Worker task timeout));
      }, this.options.timeout);

      this.pendingTasks.set(taskId, { resolve, reject, timeout: timeout as any });

      if (this.availableWorkers.length > 0) {
        const worker = this.availableWorkers.pop()!;
        worker.postMessage(task);
      } else {
        this.taskQueue.push(task);
      }
    });
  }

  // 终止所有 workers
  terminate(): void {
    this.workers.forEach(worker => worker.terminate());
    this.workers = [];
    this.availableWorkers = [];
    this.taskQueue = [];
    
    // 拒绝所有待处理的任务
    this.pendingTasks.forEach(({ reject, timeout }) => {
      if (timeout) clearTimeout(timeout);
      reject(new Error(Worker manager terminated));
    });
    this.pendingTasks.clear();
  }

  // 获取状态
  getStatus() {
    return {
      totalWorkers: this.workers.length,
      availableWorkers: this.availableWorkers.length,
      queuedTasks: this.taskQueue.length,
      pendingTasks: this.pendingTasks.size
    };
  }
}

// 回测 Worker 管理器
export class BacktestWorkerManager extends WorkerManager {
  constructor() {
    super(/workers/backtest-worker.js, {
      maxWorkers: 2, // 回测通常是 CPU 密集型，限制并发数
      timeout: 60000 // 1 minute timeout for backtests
    });
  }

  // 运行回测
  async runBacktest(strategy: any, data: any, config: any): Promise<any> {
    return this.execute(backtest, {
      strategy,
      data,
      config
    });
  }

  // 计算技术指标
  async calculateIndicators(data: any, indicators: string[]): Promise<any> {
    return this.execute(indicators, {
      data,
      indicators
    });
  }

  // 优化参数
  async optimizeParameters(strategy: any, data: any, parameterRanges: any): Promise<any> {
    return this.execute(optimize, {
      strategy,
      data,
      parameterRanges
    });
  }
}

// 数据处理 Worker 管理器
export class DataWorkerManager extends WorkerManager {
  constructor() {
    super(/workers/data-worker.js, {
      maxWorkers: 4,
      timeout: 30000
    });
  }

  // 处理大量市场数据
  async processMarketData(rawData: any): Promise<any> {
    return this.execute(process_market_data, rawData);
  }

  // 计算统计指标
  async calculateStatistics(data: any): Promise<any> {
    return this.execute(calculate_statistics, data);
  }

  // 数据清洗
  async cleanData(data: any, rules: any): Promise<any> {
    return this.execute(clean_data, { data, rules });
  }
}

// 全局 Worker 管理器实例
let backtestWorkerManager: BacktestWorkerManager | null = null;
let dataWorkerManager: DataWorkerManager | null = null;

export function getBacktestWorkerManager(): BacktestWorkerManager {
  if (!backtestWorkerManager) {
    backtestWorkerManager = new BacktestWorkerManager();
  }
  return backtestWorkerManager;
}

export function getDataWorkerManager(): DataWorkerManager {
  if (!dataWorkerManager) {
    dataWorkerManager = new DataWorkerManager();
  }
  return dataWorkerManager;
}

// 清理函数
export function terminateAllWorkers(): void {
  if (backtestWorkerManager) {
    backtestWorkerManager.terminate();
    backtestWorkerManager = null;
  }
  
  if (dataWorkerManager) {
    dataWorkerManager.terminate();
    dataWorkerManager = null;
  }
}

// 在页面卸载时清理 workers`
if (typeof window !== undefined) {'''
  window.addEventListener(beforeunload', terminateAllWorkers);'`
}