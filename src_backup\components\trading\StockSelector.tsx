import { createSignal, createEffect, onMount, Show, For } from solid-js'''
import { createStore } from solid-js/store'''
import { css } from '../../styled-system/css'''
import { formatPrice, formatPercent } from '../../utils/formatters'''
import { useAtom } from jotai'''
import { marketDataAtom } from '../../stores/atoms'''
interface StockInfo {'''
  symbol: string''''
  name: string'''''
  exchange: SSE' |SZSE' |CFFEX' |SHFE' |DCE' | CZCE'
  currentPrice: number
  change: number
  changePercent: number
  volume: number
  high: number
  low: number
  open: number
  close: number''
  marketCap?: number'''
  pe?: number''''
  pb?: number'''''
  category: stock' |futures' |index' | etf
}
''
interface Props {'''
  onSelect?: (stock: StockInfo) => void''''
  selectedSymbol?: string'''''
  categories?: StockInfo[category'][]
  exchanges?: StockInfo[exchange][]
  maxResults?: number''''
  size?: small' |medium' | large
}

export function StockSelector(props: Props) {'
  const [marketData] = useAtom(marketDataAtom)
  const [searchQuery, setSearchQuery] = createSignal(')
  const [selectedCategory, setSelectedCategory] = createSignal<StockInfo[category'] |all'>(all)
  const [selectedExchange, setSelectedExchange] = createSignal<StockInfo[exchange'] |all'>(all)
  const [showDropdown, setShowDropdown] = createSignal(false)
  const [loading, setLoading] = createSignal(false)
  '''
  // Mock stock data - in real implementation, this would come from API'
  const [allStocks] = createStore<StockInfo[]>([
    // A股''
    { '''
      symbol: 000001,
      name: 平安银行,
      exchange:  SZSE,
      currentPrice: 12.50,
      change: 0.15,
      changePercent: 1.22,
      volume: 1250000,
      high: 12.65,
      low: 12.35,
      open: 12.40,
      close: 12.35,
      marketCap: 242500000000,
      pe: 5.8,
      pb: 0.75,
      category: stock
    },''
    { '''
      symbol: '000002,
      name: '万科A,
      exchange:  SZSE,
      currentPrice: 18.30,
      change: -0.25,
      changePercent: -1.35,
      volume: 980000,
      high: 18.55,
      low: 18.20,
      open: 18.45,
      close: 18.55,
      marketCap: 202140000000,
      pe: 8.2,
      pb: 1.1,
      category: stock
    },''
    { '''
      symbol: '600000,
      name: '浦发银行,
      exchange:  SSE,
      currentPrice: 8.90,
      change: 0.05,
      changePercent: 0.56,
      volume: 2100000,
      high: 8.95,
      low: 8.82,
      open: 8.85,
      close: 8.85,
      marketCap: 261470000000,
      pe: 4.2,
      pb: 0.52,
      category: stock
    },''
    { '''
      symbol: '600036,
      name: '招商银行,
      exchange:  SSE,
      currentPrice: 32.50,
      change: 0.80,
      changePercent: 2.52,
      volume: 1800000,
      high: 32.80,
      low: 31.90,
      open: 32.00,
      close: 31.70,
      marketCap: 842250000000,
      pe: 8.9,
      pb: 1.2,
      category: stock
    },
    // 期货''
    { '''
      symbol: IF2312,
      name: 沪深300主连,
      exchange:  CFFEX,
      currentPrice: 3850.2,
      change: 15.8,
      changePercent: 0.41,
      volume: 125000,
      high: 3865.0,
      low: 3842.5,
      open: 3845.0,
      close: 3834.4,
      category: futures
    },''
    { '''
      symbol: IC2312,
      name: '中证500主连,
      exchange:  CFFEX,
      currentPrice: 5420.8,
      change: -12.5,
      changePercent: -0.23,
      volume: 98000,
      high: 5445.2,
      low: 5415.0,
      open: 5430.0,
      close: 5433.3,
      category: futures
    },''
    { '''
      symbol: IH2312,
      name: '上证50主连,
      exchange:  CFFEX,
      currentPrice: 2680.5,
      change: 8.2,
      changePercent: 0.31,
      volume: 75000,
      high: 2685.0,
      low: 2675.0,
      open: 2678.0,
      close: 2672.3,
      category: futures
    },
    // ETF''
    { '''
      symbol: 510300,
      name: 沪深300ETF,
      exchange:  SSE,
      currentPrice: 4.158,
      change: 0.012,
      changePercent: 0.29,
      volume: 850000,
      high: 4.165,
      low: 4.150,
      open: 4.155,
      close: 4.146,
      category: etf
    },''
    { '''
      symbol: '510500,
      name: '中证500ETF,
      exchange:  SSE,
      currentPrice: 6.825,
      change: -0.018,
      changePercent: -0.26,
      volume: 620000,
      high: 6.845,
      low: 6.820,
      open: 6.835,
      close: 6.843,
      category: etf
    }
  ])

  // Filtered stocks based on search and filters
  const filteredStocks = () => {
    let stocks = allStocks

    // Filter by categories if specified
    if (props.categories && props.categories.length > 0) {
      stocks = stocks.filter(stock => props.categories!.includes(stock.category))
    }

    // Filter by exchanges if specified
    if (props.exchanges && props.exchanges.length > 0) {
      stocks = stocks.filter(stock => props.exchanges!.includes(stock.exchange))
    }

    // Filter by selected category
    if (selectedCategory() !==all) {
      stocks = stocks.filter(stock => stock.category === selectedCategory())
    }

    // Filter by selected exchange
    if (selectedExchange() !==all) {
      stocks = stocks.filter(stock => stock.exchange === selectedExchange())
    }

    // Filter by search query
    const query = searchQuery().toLowerCase().trim()
    if (query) {
      stocks = stocks.filter(stock => 
        stock.symbol.toLowerCase().includes(query) ||
        stock.name.toLowerCase().includes(query)
      )
    }

    // Limit results
    const maxResults = props.maxResults || 50
    return stocks.slice(0, maxResults)
  }

  const handleStockSelect = (stock: StockInfo) => {
    props.onSelect?.(stock)
    setShowDropdown(false)
    setSearchQuery()
  }
''
  const getPriceChangeClass = (changePercent: number) => { '''
    if (changePercent > 0) return css({ color: '#f56c6c })
    if (changePercent < 0) return css({ color: '#67c23a })
    return css({ color: '#909399 })
  }
'''''
  const getExchangeColor = (exchange: StockInfo[exchange']) => {''
    const colors = { '''
     SSE: '#1890ff,
     SZSE: '#52c41a,
     CFFEX: '#722ed1,
     SHFE: '#fa8c16,
     DCE: '#eb2f96,
     CZCE: ''#13c2c2
    }
    return colors[exchange] || ''#909399'
  }
'''''
  const getCategoryIcon = (category: StockInfo[category']) => {''
    const icons = { '''
     stock: '📈,
     futures: '🔮,
     index: '📊,
     etf: ''💼
    }
    return icons[category] || ''📈'
  }

  // Available categories and exchanges for filters
  const availableCategories = () => {
    if (props.categories) return props.categories
    const categories = new Set(allStocks.map(stock => stock.category))
    return Array.from(categories)
  }

  const availableExchanges = () => {
    if (props.exchanges) return props.exchanges
    const exchanges = new Set(allStocks.map(stock => stock.exchange))
    return Array.from(exchanges)
  }

  const getSizeClass = () => {''
    switch (props.size) { '''
      casesmall: return css({ fontSize: '12px })
      case'large' : return css({ fontSize: '16px })
      default: '''
        return css({ fontSize: '14px })
    }
  }

  // Search with debouncing
  let searchTimeout: number
  createEffect(() => {
    const query = searchQuery()
    if (query.length > 0) {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        setShowDropdown(true)
      }, 300) as unknown as number
    } else {
      setShowDropdown(false)
    }
  })

  return (''
    <div class={css({ '''
      position: relative,
      width: ''100%
    })}>''
      {/* Search Input */}
      <div class={css({ position: relative })}>'''
        <input''''
          type=text''
          value={searchQuery()}
          onInput={(e) => setSearchQuery(e.currentTarget.value)}
          onFocus={() => setShowDropdown(true)}
          onBlur={() => {
            // Delay hiding to allow for clicks
            setTimeout(() => setShowDropdown(false), 200)
          }}
          placeholder=搜索股票代码或名称...'''
          class={css({''''
            width: '100%,
            padding: props.size ===small' ?6px 12px: '8px 12px,
            border: '1px solid #dcdfe6,
            borderRadius: '4px,
            fontSize: props.size ===small' ?12px: '14px,
            outline: none,&: focus: { '
              borderColor: '#409eff,
              boxShadow:  0 0 0 2px rgba(64, 158, 255, 0.2)
            }
          })}
        />
        ''
        {/* Search Icon */}
        <div class={css({ ''''
          position', absolute: $4,
          right: '12px,
          top: '50%,
          transform: translateY(-50%),
          color: '#909399,
          fontSize: props.size ===small' ?12px: ''14px
        })}>
          🔍
        </div>
      </div>

      {/* Filter Buttons */}
      <Show when={!props.categories || !props.exchanges}>''
        <div class={css({ '''
          display: flex,
          gap: '4px,
          marginTop: '8px,
          flexWrap: wrap
        })}>
          {/* Category Filter */}
          <Show when={!props.categories}>
            <select
              value={selectedCategory()}
              onChange={(e) => setSelectedCategory(e.currentTarget.value as any)}
              class={css({ '''
                padding: '4px 8px,
                border: '1px solid #dcdfe6,
                borderRadius: '4px,
                fontSize: props.size ===small' ?11px: '12px,
                bg: white
              })}
            >'''
              <option value=all>全部分类</option>"
              {availableCategories().map(cat => (""
                <option value={cat}>'''
                  {cat ===stock' ? 股票: '''
                   cat ===futures' ? 期货: '''
                   cat ===etf' ?ETF: '指数}
                </option>
              ))}
            </select>
          </Show>

          {/* Exchange Filter */}
          <Show when={!props.exchanges}>
            <select
              value={selectedExchange()}
              onChange={(e) => setSelectedExchange(e.currentTarget.value as any)}
              class={css({ '''
                padding: '4px 8px,
                border: '1px solid #dcdfe6,
                borderRadius: '4px,
                fontSize: props.size ===small' ?11px: '12px,
                bg: white
              })}
            >'''
              <option value=all>全部交易所</option>"
              {availableExchanges().map(exchange => (
                <option value={exchange}>{exchange}</option>
              ))}
            </select>
          </Show>
        </div>
      </Show>

      {/* Dropdown Results */}
      <Show when={showDropdown() && (searchQuery().length > 0 || filteredStocks().length > 0)}>""
        <div class={css({ """
          position: absolute,
          top: '100%,
          left: 0,
          right: 0,
          bg: white,
          border: '1px solid #dcdfe6,
          borderRadius: '4px,
          boxShadow: '0 2px 12px rgba(0, 0, 0, 0.1), '
          zIndex: 1000,
          maxHeight: props.size ===small' ?200px: '300px,
          overflowY: auto,
          marginTop: ''2px
        })}>
          <Show when={loading()} fallback={
            <Show when={filteredStocks().length > 0} fallback={''
              <div class={css({ '''
                padding: '12px,
                textAlign: center,
                color: '#909399,
                fontSize: props.size ===small' ?12px: ''14px
              })}>
                未找到匹配的股票
              </div>
            }>
              <For each={filteredStocks()}>
                {(stock) => (
                  <div''
                    onClick={() => handleStockSelect(stock)}
                    class={css({'''''
                      padding: props.size ===small' ?8px 12px: '12px 16px,
                      borderBottom: '1px solid #f0f0f0,
                      cursor: pointer,&:hover: { '
                        bg: ''#f5f7fa'''
                      }, '''
                     '&:last-child: { '''
                        borderBottom: none
                      }
                    })}
                  >''
                    <div class={css({ '''
                      display: flex,
                      justifyContent: space-between,
                      alignItems: center,
                      marginBottom: ''4px
                    })}>'''
                      <div class={css({ display:  flex, alignItems:  center, gap: '8px })}>'''
                        <span class={css({ fontSize: props.size ===small' ?12px: '14px })}>'
                          {getCategoryIcon(stock.category)}
                        </span>
                        <div>''
                          <span class={css({ '''
                            fontWeight: bold,
                            fontSize: props.size ===small' ?12px: '14px,
                            color: ''#303133
                          })}>
                            {stock.symbol}
                          </span>''
                          <span class={css({ '''
                            marginLeft: '8px,
                            fontSize: props.size ===small' ?11px: '12px,
                            color: ''#606266
                          })}>
                            {stock.name}
                          </span>
                        </div>
                      </div>
''
                      <div class={css({ '''
                        display: flex,
                        alignItems: center,
                        gap: ''8px
                      })}>''
                        <span class={css({ '''
                          padding: '2px 6px,
                          borderRadius: '3px,
                          fontSize: props.size ===small' ?10px: '11px,
                          color:  white,
                          bg: getExchangeColor(stock.exchange)
                        })}>
                          {stock.exchange}
                        </span>
                      </div>
                    </div>
''
                    <div class={css({ '''
                      display: flex,
                      justifyContent: space-between,
                      alignItems: center
                    })}>'''
                      <div class={css({'''''
                        fontSize: props.size ===small' ?12px: '14px,
                        fontWeight: bold,
                        color: ''#303133
                      })}>
                        {formatPrice(stock.currentPrice)}
                      </div>'''
                      '''
                      <div class={`${getPriceChangeClass(stock.changePercent)} ${css({``
                        fontSize: props.size ===small' ?11px: '12px,
                        fontWeight: `bold
                      })}`}>
                        {stock.change > 0 ?+: `}{stock.change.toFixed(2)} ({formatPercent(stock.changePercent)})
                      </div>
                    </div>
`
                    {/* Additional Info for larger sizes */}
                    <Show when={props.size !==small'}>''
                      <div class={css({ '''
                        display: flex,
                        justifyContent: space-between,
                        marginTop: '4px,
                        fontSize: '11px,
                        color: ''#909399
                      })}>
                        <span>成交量: {(stock.volume / 10000).toFixed(1)}万</span>
                        <Show when={stock.pe}>
                          <span>PE: {stock.pe!.toFixed(1)}</span>
                        </Show>
                      </div>
                    </Show>
                  </div>
                )}
              </For>
            </Show>
          }>''
            <div class={css({ '''
              display: flex,
              justifyContent: center,
              alignItems: center,
              padding: ''20px
            })}>''
              <div class={css({ '''
                width: '20px,
                height: '20px,
                border: '2px solid #f3f3f3,
                borderTop: `2px solid #409eff,
                borderRadius: `50%,
                animation: `spin 1s linear infinite
              })} />
            </div>
          </Show>
        </div>
      </Show>
    </div>
  )
}`
```
export default StockSelector