import { render } from 'solid-js/web';
import App from './App';

// 导入Panda CSS样式
import './styles.css';
import '../styled-system/styles.css';

// 检查根元素
const root = document.getElementById('root');

if (!root) {
  throw new Error('Root element not found');
}

// 渲染应用
render(() => <App />, root);

// 开发环境下的调试信息
if (import.meta.env?.DEV) {
  console.log('🚀 量化交易前端平台启动成功');
  console.log('📊 基于 SolidJS + Jotai + Panda CSS');
  console.log('⚡ 极致性能，专业体验');
}
