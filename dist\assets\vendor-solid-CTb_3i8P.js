const We=(e,t)=>e===t,oe=Symbol("solid-proxy"),Ve=typeof Proxy=="function",G={equals:We};let be=Le;const T=1,H=2,ve={owned:null,cleanups:null,context:null,owner:null};var g=null;let te=null,Xe=null,b=null,E=null,L=null,Z=0;function Ae(e,t){const n=b,r=g,s=e.length===0,i=t===void 0?r:t,l=s?ve:{owned:null,cleanups:null,context:i?i.context:null,owner:i},o=s?e:()=>e(()=>C(()=>K(l)));g=l,b=null;try{return k(o,!0)}finally{b=n,g=r}}function F(e,t){t=t?Object.assign({},G,t):G;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},r=s=>(typeof s=="function"&&(s=s(n.value)),Ce(n,s));return[Re.bind(n),r]}function q(e,t,n){const r=fe(e,t,!1,T);M(r)}function Ge(e,t,n){be=ze;const r=fe(e,t,!1,T);r.user=!0,L?L.push(r):M(r)}function S(e,t,n){n=n?Object.assign({},G,n):G;const r=fe(e,t,!0,0);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,M(r),Re.bind(r)}function He(e){return k(e,!1)}function C(e){if(b===null)return e();const t=b;b=null;try{return e()}finally{b=t}}function ue(e,t,n){const r=Array.isArray(e);let s,i=n&&n.defer;return l=>{let o;if(r){o=Array(e.length);for(let u=0;u<e.length;u++)o[u]=e[u]()}else o=e();if(i)return i=!1,l;const a=C(()=>t(o,s,l));return s=o,a}}function jt(e){Ge(()=>C(e))}function Se(e){return g===null||(g.cleanups===null?g.cleanups=[e]:g.cleanups.push(e)),e}function Ee(){return g}function Pe(e,t){const n=g,r=b;g=e,b=null;try{return k(t,!0)}catch(s){he(s)}finally{g=n,b=r}}function Ye(e){const t=b,n=g;return Promise.resolve().then(()=>{b=t,g=n;let r;return k(e,!1),b=g=null,r?r.done:void 0})}const[Ft,It]=F(!1);function xe(e,t){const n=Symbol("context");return{id:n,Provider:tt(n),defaultValue:e}}function Je(e){let t;return g&&g.context&&(t=g.context[e.id])!==void 0?t:e.defaultValue}function ce(e){const t=S(e),n=S(()=>ie(t()));return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}function Re(){if(this.sources&&this.state)if(this.state===T)M(this);else{const e=E;E=null,k(()=>J(this),!1),E=e}if(b){const e=this.observers?this.observers.length:0;b.sources?(b.sources.push(this),b.sourceSlots.push(e)):(b.sources=[this],b.sourceSlots=[e]),this.observers?(this.observers.push(b),this.observerSlots.push(b.sources.length-1)):(this.observers=[b],this.observerSlots=[b.sources.length-1])}return this.value}function Ce(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&k(()=>{for(let s=0;s<e.observers.length;s+=1){const i=e.observers[s],l=te&&te.running;l&&te.disposed.has(i),(l?!i.tState:!i.state)&&(i.pure?E.push(i):L.push(i),i.observers&&Oe(i)),l||(i.state=T)}if(E.length>1e6)throw E=[],new Error},!1)),t}function M(e){if(!e.fn)return;K(e);const t=Z;Qe(e,e.value,t)}function Qe(e,t,n){let r;const s=g,i=b;b=g=e;try{r=e.fn(t)}catch(l){return e.pure&&(e.state=T,e.owned&&e.owned.forEach(K),e.owned=null),e.updatedAt=n+1,he(l)}finally{b=i,g=s}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?Ce(e,r):e.value=r,e.updatedAt=n)}function fe(e,t,n,r=T,s){const i={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:g,context:g?g.context:null,pure:n};return g===null||g!==ve&&(g.owned?g.owned.push(i):g.owned=[i]),i}function Y(e){if(e.state===0)return;if(e.state===H)return J(e);if(e.suspense&&C(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<Z);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===T)M(e);else if(e.state===H){const r=E;E=null,k(()=>J(e,t[0]),!1),E=r}}function k(e,t){if(E)return e();let n=!1;t||(E=[]),L?n=!0:L=[],Z++;try{const r=e();return Ze(n),r}catch(r){n||(L=null),E=null,he(r)}}function Ze(e){if(E&&(Le(E),E=null),e)return;const t=L;L=null,t.length&&k(()=>be(t),!1)}function Le(e){for(let t=0;t<e.length;t++)Y(e[t])}function ze(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:Y(r)}for(t=0;t<n;t++)Y(e[t])}function J(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const s=r.state;s===T?r!==t&&(!r.updatedAt||r.updatedAt<Z)&&Y(r):s===H&&J(r,t)}}}function Oe(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=H,n.pure?E.push(n):L.push(n),n.observers&&Oe(n))}}function K(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),s=n.observers;if(s&&s.length){const i=s.pop(),l=n.observerSlots.pop();r<s.length&&(i.sourceSlots[l]=r,s[r]=i,n.observerSlots[r]=l)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)K(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)K(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function et(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function he(e,t=g){throw et(e)}function ie(e){if(typeof e=="function"&&!e.length)return ie(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=ie(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function tt(e,t){return function(r){let s;return q(()=>s=C(()=>(g.context={...g.context,[e]:r.value},ce(()=>r.children))),void 0),s}}function O(e,t){return C(()=>e(t||{}))}function V(){return!0}const nt={get(e,t,n){return t===oe?n:e.get(t)},has(e,t){return t===oe?!0:e.has(t)},set:V,deleteProperty:V,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:V,deleteProperty:V}},ownKeys(e){return e.keys()}};function ne(e){return(e=typeof e=="function"?e():e)?e:{}}function rt(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function st(...e){let t=!1;for(let l=0;l<e.length;l++){const o=e[l];t=t||!!o&&oe in o,e[l]=typeof o=="function"?(t=!0,S(o)):o}if(Ve&&t)return new Proxy({get(l){for(let o=e.length-1;o>=0;o--){const a=ne(e[o])[l];if(a!==void 0)return a}},has(l){for(let o=e.length-1;o>=0;o--)if(l in ne(e[o]))return!0;return!1},keys(){const l=[];for(let o=0;o<e.length;o++)l.push(...Object.keys(ne(e[o])));return[...new Set(l)]}},nt);const n={},r=Object.create(null);for(let l=e.length-1;l>=0;l--){const o=e[l];if(!o)continue;const a=Object.getOwnPropertyNames(o);for(let u=a.length-1;u>=0;u--){const c=a[u];if(c==="__proto__"||c==="constructor")continue;const h=Object.getOwnPropertyDescriptor(o,c);if(!r[c])r[c]=h.get?{enumerable:!0,configurable:!0,get:rt.bind(n[c]=[h.get.bind(o)])}:h.value!==void 0?h:void 0;else{const p=n[c];p&&(h.get?p.push(h.get.bind(o)):h.value!==void 0&&p.push(()=>h.value))}}}const s={},i=Object.keys(r);for(let l=i.length-1;l>=0;l--){const o=i[l],a=r[o];a&&a.get?Object.defineProperty(s,o,a):s[o]=a?a.value:void 0}return s}const ot=e=>`Stale read from <${e}>.`;function Te(e){const t=e.keyed,n=S(()=>e.when,void 0,void 0),r=t?n:S(n,void 0,{equals:(s,i)=>!s==!i});return S(()=>{const s=r();if(s){const i=e.children;return typeof i=="function"&&i.length>0?C(()=>i(t?s:()=>{if(!C(r))throw ot("Show");return n()})):i}return e.fallback},void 0,void 0)}const it=e=>S(()=>e());function lt(e,t,n){let r=n.length,s=t.length,i=r,l=0,o=0,a=t[s-1].nextSibling,u=null;for(;l<s||o<i;){if(t[l]===n[o]){l++,o++;continue}for(;t[s-1]===n[i-1];)s--,i--;if(s===l){const c=i<r?o?n[o-1].nextSibling:n[i-o]:a;for(;o<i;)e.insertBefore(n[o++],c)}else if(i===o)for(;l<s;)(!u||!u.has(t[l]))&&t[l].remove(),l++;else if(t[l]===n[i-1]&&n[o]===t[s-1]){const c=t[--s].nextSibling;e.insertBefore(n[o++],t[l++].nextSibling),e.insertBefore(n[--i],c),t[s]=n[i]}else{if(!u){u=new Map;let h=o;for(;h<i;)u.set(n[h],h++)}const c=u.get(t[l]);if(c!=null)if(o<c&&c<i){let h=l,p=1,m;for(;++h<s&&h<i&&!((m=u.get(t[h]))==null||m!==c+p);)p++;if(p>c-o){const P=t[l];for(;o<c;)e.insertBefore(n[o++],P)}else e.replaceChild(n[o++],t[l++])}else l++;else t[l++].remove()}}}const me="_$DX_DELEGATE";function Bt(e,t,n,r={}){let s;return Ae(i=>{s=i,t===document?e():ut(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{s(),t.textContent=""}}function qt(e,t,n,r){let s;const i=()=>{const o=document.createElement("template");return o.innerHTML=e,o.content.firstChild},l=()=>(s||(s=i())).cloneNode(!0);return l.cloneNode=l,l}function at(e,t=window.document){const n=t[me]||(t[me]=new Set);for(let r=0,s=e.length;r<s;r++){const i=e[r];n.has(i)||(n.add(i),t.addEventListener(i,ct))}}function Kt(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function Mt(e,t){t==null?e.removeAttribute("class"):e.className=t}function ut(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return Q(e,t,r,n);q(s=>Q(e,t(),s,n),r)}function ct(e){let t=e.target;const n=`$$${e.type}`,r=e.target,s=e.currentTarget,i=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),l=()=>{const a=t[n];if(a&&!t.disabled){const u=t[`${n}Data`];if(u!==void 0?a.call(t,u,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&i(t.host),!0},o=()=>{for(;l()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const a=e.composedPath();i(a[0]);for(let u=0;u<a.length-2&&(t=a[u],!!l());u++){if(t._$host){t=t._$host,o();break}if(t.parentNode===s)break}}else o();i(r)}function Q(e,t,n,r,s){for(;typeof n=="function";)n=n();if(t===n)return n;const i=typeof t,l=r!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,i==="string"||i==="number"){if(i==="number"&&(t=t.toString(),t===n))return n;if(l){let o=n[0];o&&o.nodeType===3?o.data!==t&&(o.data=t):o=document.createTextNode(t),n=j(e,n,r,o)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||i==="boolean")n=j(e,n,r);else{if(i==="function")return q(()=>{let o=t();for(;typeof o=="function";)o=o();n=Q(e,o,n,r)}),()=>n;if(Array.isArray(t)){const o=[],a=n&&Array.isArray(n);if(le(o,t,n,s))return q(()=>n=Q(e,o,n,r,!0)),()=>n;if(o.length===0){if(n=j(e,n,r),l)return n}else a?n.length===0?ye(e,o,r):lt(e,n,o):(n&&j(e),ye(e,o));n=o}else if(t.nodeType){if(Array.isArray(n)){if(l)return n=j(e,n,r,t);j(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function le(e,t,n,r){let s=!1;for(let i=0,l=t.length;i<l;i++){let o=t[i],a=n&&n[e.length],u;if(!(o==null||o===!0||o===!1))if((u=typeof o)=="object"&&o.nodeType)e.push(o);else if(Array.isArray(o))s=le(e,o,a)||s;else if(u==="function")if(r){for(;typeof o=="function";)o=o();s=le(e,Array.isArray(o)?o:[o],Array.isArray(a)?a:[a])||s}else e.push(o),s=!0;else{const c=String(o);a&&a.nodeType===3&&a.data===c?e.push(a):e.push(document.createTextNode(c))}}return s}function ye(e,t,n=null){for(let r=0,s=t.length;r<s;r++)e.insertBefore(t[r],n)}function j(e,t,n,r){if(n===void 0)return e.textContent="";const s=r||document.createTextNode("");if(t.length){let i=!1;for(let l=t.length-1;l>=0;l--){const o=t[l];if(s!==o){const a=o.parentNode===e;!i&&!l?a?e.replaceChild(s,o):e.insertBefore(s,n):a&&o.remove()}else i=!0}}else e.insertBefore(s,n);return[s]}const ft=!1;function ke(){let e=new Set;function t(s){return e.add(s),()=>e.delete(s)}let n=!1;function r(s,i){if(n)return!(n=!1);const l={to:s,options:i,defaultPrevented:!1,preventDefault:()=>l.defaultPrevented=!0};for(const o of e)o.listener({...l,from:o.location,retry:a=>{a&&(n=!0),o.navigate(s,{...i,resolve:!1})}});return!l.defaultPrevented}return{subscribe:t,confirm:r}}let ae;function de(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),ae=window.history.state._depth}de();function ht(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function dt(e,t){let n=!1;return()=>{const r=ae;de();const s=r==null?null:ae-r;if(n){n=!1;return}s&&t(s)?(n=!0,window.history.go(-s)):e()}}const pt=/^(?:[a-z0-9]+:)?\/\//i,gt=/^\/+|(\/)\/+$/g,De="http://sr";function B(e,t=!1){const n=e.replace(gt,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function X(e,t,n){if(pt.test(t))return;const r=B(e),s=n&&B(n);let i="";return!s||t.startsWith("/")?i=r:s.toLowerCase().indexOf(r.toLowerCase())!==0?i=r+s:i=s,(i||"/")+B(t,!i)}function mt(e,t){return B(e).replace(/\/*(\*.*)?$/g,"")+B(t)}function Ue(e){const t={};return e.searchParams.forEach((n,r)=>{t[r]=n}),t}function yt(e,t,n){const[r,s]=e.split("/*",2),i=r.split("/").filter(Boolean),l=i.length;return o=>{const a=o.split("/").filter(Boolean),u=a.length-l;if(u<0||u>0&&s===void 0&&!t)return null;const c={path:l?"":"/",params:{}},h=p=>n===void 0?void 0:n[p];for(let p=0;p<l;p++){const m=i[p],P=a[p],f=m[0]===":",d=f?m.slice(1):m;if(f&&re(P,h(d)))c.params[d]=P;else if(f||!re(P,m))return null;c.path+=`/${P}`}if(s){const p=u?a.slice(-u).join("/"):"";if(re(p,h(s)))c.params[s]=p;else return null}return c}}function re(e,t){const n=r=>r.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function wt(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((s,i)=>s+(i.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function _e(e){const t=new Map,n=Ee();return new Proxy({},{get(r,s){return t.has(s)||Pe(n,()=>t.set(s,S(()=>e()[s]))),t.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function Ne(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const s=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)s.push(n+=t[1]),r=r.slice(t[0].length);return Ne(r).reduce((i,l)=>[...i,...s.map(o=>o+l)],[])}const bt=100,vt=xe(),$e=xe();function At(e,t=""){const{component:n,load:r,children:s,info:i}=e,l=!s||Array.isArray(s)&&!s.length,o={key:e,component:n,load:r,info:i};return je(e.path).reduce((a,u)=>{for(const c of Ne(u)){const h=mt(t,c);let p=l?h:h.split("/*",1)[0];p=p.split("/").map(m=>m.startsWith(":")||m.startsWith("*")?m:encodeURIComponent(m)).join("/"),a.push({...o,originalPath:u,pattern:p,matcher:yt(p,!l,e.matchFilters)})}return a},[])}function St(e,t=0){return{routes:e,score:wt(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let s=e.length-1;s>=0;s--){const i=e[s],l=i.matcher(n);if(!l)return null;r.unshift({...l,route:i})}return r}}}function je(e){return Array.isArray(e)?e:[e]}function Fe(e,t="",n=[],r=[]){const s=je(e);for(let i=0,l=s.length;i<l;i++){const o=s[i];if(o&&typeof o=="object"){o.hasOwnProperty("path")||(o.path="");const a=At(o,t);for(const u of a){n.push(u);const c=Array.isArray(o.children)&&o.children.length===0;if(o.children&&!c)Fe(o.children,u.pattern,n,r);else{const h=St([...n],r.length);r.push(h)}n.pop()}}}return n.length?r:r.sort((i,l)=>l.score-i.score)}function se(e,t){for(let n=0,r=e.length;n<r;n++){const s=e[n].matcher(t);if(s)return s}return[]}function Et(e,t){const n=new URL(De),r=S(a=>{const u=e();try{return new URL(u,n)}catch{return console.error(`Invalid path ${u}`),a}},n,{equals:(a,u)=>a.href===u.href}),s=S(()=>r().pathname),i=S(()=>r().search,!0),l=S(()=>r().hash),o=()=>"";return{get pathname(){return s()},get search(){return i()},get hash(){return l()},get state(){return t()},get key(){return o()},query:_e(ue(i,()=>Ue(r())))}}let _;function Pt(){return _}function xt(e,t,n,r={}){const{signal:[s,i],utils:l={}}=e,o=l.parsePath||(w=>w),a=l.renderPath||(w=>w),u=l.beforeLeave||ke(),c=X("",r.base||"");if(c===void 0)throw new Error(`${c} is not a valid base path`);c&&!s().value&&i({value:c,replace:!0,scroll:!1});const[h,p]=F(!1);let m;const P=(w,A)=>{A.value===f()&&A.state===v()||(m===void 0&&p(!0),_=w,m=A,Ye(()=>{m===A&&(d(m.value),y(m.state),D[1]([]))}).finally(()=>{m===A&&He(()=>{_=void 0,w==="navigate"&&Ke(m),p(!1),m=void 0})}))},[f,d]=F(s().value),[v,y]=F(s().state),N=Et(f,v),R=[],D=F([]),I=S(()=>typeof r.transformUrl=="function"?se(t(),r.transformUrl(N.pathname)):se(t(),N.pathname)),Ie=_e(()=>{const w=I(),A={};for(let x=0;x<w.length;x++)Object.assign(A,w[x].params);return A}),pe={pattern:c,path:()=>c,outlet:()=>null,resolvePath(w){return X(c,w)}};return q(ue(s,w=>P("native",w),{defer:!0})),{base:pe,location:N,params:Ie,isRouting:h,renderPath:a,parsePath:o,navigatorFactory:qe,matches:I,beforeLeave:u,preloadRoute:Me,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:D};function Be(w,A,x){C(()=>{if(typeof A=="number"){A&&(l.go?l.go(A):console.warn("Router integration does not support relative routing"));return}const{replace:z,resolve:ee,scroll:$,state:W}={replace:!1,resolve:!0,scroll:!0,...x},U=ee?w.resolvePath(A):X("",A);if(U===void 0)throw new Error(`Path '${A}' is not a routable path`);if(R.length>=bt)throw new Error("Too many redirects");const ge=f();(U!==ge||W!==v())&&(ft||u.confirm(U,x)&&(R.push({value:ge,replace:z,scroll:$,state:v()}),P("navigate",{value:U,state:W})))})}function qe(w){return w=w||Je($e)||pe,(A,x)=>Be(w,A,x)}function Ke(w){const A=R[0];A&&(i({...w,replace:A.replace,scroll:A.scroll}),R.length=0)}function Me(w,A={}){const x=se(t(),w.pathname),z=_;_="preload";for(let ee in x){const{route:$,params:W}=x[ee];$.component&&$.component.preload&&$.component.preload();const{load:U}=$;A.preloadData&&U&&Pe(n(),()=>U({params:W,location:{pathname:w.pathname,search:w.search,hash:w.hash,query:Ue(w),state:null,key:""},intent:"preload"}))}_=z}}function Rt(e,t,n,r){const{base:s,location:i,params:l}=e,{pattern:o,component:a,load:u}=r().route,c=S(()=>r().path);a&&a.preload&&a.preload();const h=u?u({params:l,location:i,intent:_||"initial"}):void 0;return{parent:t,pattern:o,path:c,outlet:()=>a?O(a,{params:l,location:i,data:h,get children(){return n()}}):n(),resolvePath(m){return X(s.path(),m,c())}}}const Ct=e=>t=>{const{base:n}=t,r=ce(()=>t.children),s=S(()=>Fe(r(),t.base||""));let i;const l=xt(e,s,()=>i,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(l),O(vt.Provider,{value:l,get children(){return O(Lt,{routerState:l,get root(){return t.root},get load(){return t.rootLoad},get children(){return[it(()=>(i=Ee())&&null),O(Ot,{routerState:l,get branches(){return s()}})]}})}})};function Lt(e){const t=e.routerState.location,n=e.routerState.params,r=S(()=>e.load&&C(()=>{e.load({params:n,location:t,intent:Pt()||"initial"})}));return O(Te,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:s=>O(s,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function Ot(e){const t=[];let n;const r=S(ue(e.routerState.matches,(s,i,l)=>{let o=i&&s.length===i.length;const a=[];for(let u=0,c=s.length;u<c;u++){const h=i&&i[u],p=s[u];l&&h&&p.route.key===h.route.key?a[u]=l[u]:(o=!1,t[u]&&t[u](),Ae(m=>{t[u]=m,a[u]=Rt(e.routerState,a[u-1]||e.routerState.base,we(()=>r()[u+1]),()=>e.routerState.matches()[u])}))}return t.splice(s.length).forEach(u=>u()),l&&o?l:(n=a[0],a)}));return we(()=>r()&&n)()}const we=e=>()=>O(Te,{get when(){return e()},keyed:!0,children:t=>O($e.Provider,{value:t,get children(){return t.outlet()}})}),Wt=e=>{const t=ce(()=>e.children);return st(e,{get children(){return t()}})};function Tt([e,t],n,r){return[e,r?s=>t(r(s)):t]}function kt(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function Dt(e){let t=!1;const n=s=>typeof s=="string"?{value:s}:s,r=Tt(F(n(e.get()),{equals:(s,i)=>s.value===i.value&&s.state===i.state}),void 0,s=>(!t&&e.set(s),s));return e.init&&Se(e.init((s=e.get())=>{t=!0,r[1](n(s)),t=!1})),Ct({signal:r,create:e.create,utils:e.utils})}function Ut(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function _t(e,t){const n=kt(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const Nt=new Map;function $t(e=!0,t=!1,n="/_server",r){return s=>{const i=s.base.path(),l=s.navigatorFactory(s.base);let o={};function a(f){return f.namespaceURI==="http://www.w3.org/2000/svg"}function u(f){if(f.defaultPrevented||f.button!==0||f.metaKey||f.altKey||f.ctrlKey||f.shiftKey)return;const d=f.composedPath().find(I=>I instanceof Node&&I.nodeName.toUpperCase()==="A");if(!d||t&&!d.hasAttribute("link"))return;const v=a(d),y=v?d.href.baseVal:d.href;if((v?d.target.baseVal:d.target)||!y&&!d.hasAttribute("state"))return;const R=(d.getAttribute("rel")||"").split(/\s+/);if(d.hasAttribute("download")||R&&R.includes("external"))return;const D=v?new URL(y,document.baseURI):new URL(y);if(!(D.origin!==window.location.origin||i&&D.pathname&&!D.pathname.toLowerCase().startsWith(i.toLowerCase())))return[d,D]}function c(f){const d=u(f);if(!d)return;const[v,y]=d,N=s.parsePath(y.pathname+y.search+y.hash),R=v.getAttribute("state");f.preventDefault(),l(N,{resolve:!1,replace:v.hasAttribute("replace"),scroll:!v.hasAttribute("noscroll"),state:R&&JSON.parse(R)})}function h(f){const d=u(f);if(!d)return;const[v,y]=d;typeof r=="function"&&(y.pathname=r(y.pathname)),o[y.pathname]||s.preloadRoute(y,{preloadData:v.getAttribute("preload")!=="false"})}function p(f){const d=u(f);if(!d)return;const[v,y]=d;typeof r=="function"&&(y.pathname=r(y.pathname)),!o[y.pathname]&&(o[y.pathname]=setTimeout(()=>{s.preloadRoute(y,{preloadData:v.getAttribute("preload")!=="false"}),delete o[y.pathname]},200))}function m(f){const d=u(f);if(!d)return;const[,v]=d;typeof r=="function"&&(v.pathname=r(v.pathname)),o[v.pathname]&&(clearTimeout(o[v.pathname]),delete o[v.pathname])}function P(f){if(f.defaultPrevented)return;let d=f.submitter&&f.submitter.hasAttribute("formaction")?f.submitter.getAttribute("formaction"):f.target.getAttribute("action");if(!d)return;if(!d.startsWith("https://action/")){const y=new URL(d,De);if(d=s.parsePath(y.pathname+y.search),!d.startsWith(n))return}if(f.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const v=Nt.get(d);if(v){f.preventDefault();const y=new FormData(f.target);f.submitter&&f.submitter.name&&y.append(f.submitter.name,f.submitter.value),v.call({r:s,f:f.target},y)}}at(["click","submit"]),document.addEventListener("click",c),e&&(document.addEventListener("mouseover",p),document.addEventListener("mouseout",m),document.addEventListener("focusin",h),document.addEventListener("touchstart",h)),document.addEventListener("submit",P),Se(()=>{document.removeEventListener("click",c),e&&(document.removeEventListener("mouseover",p),document.removeEventListener("mouseout",m),document.removeEventListener("focusin",h),document.removeEventListener("touchstart",h)),document.removeEventListener("submit",P)})}}function Vt(e){const t=()=>{const r=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(r)+window.location.hash:r+window.location.hash,state:window.history.state}},n=ke();return Dt({get:t,set({value:r,replace:s,scroll:i,state:l}){s?window.history.replaceState(ht(l),"",r):window.history.pushState(l,"",r),_t(decodeURIComponent(window.location.hash.slice(1)),i),de()},init:r=>Ut(window,"popstate",dt(r,s=>{if(s&&s<0)return!n.confirm(s);{const i=t();return!n.confirm(i.value,{state:i.state})}})),create:$t(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}export{Vt as R,O as a,xe as b,F as c,at as d,Wt as e,q as f,Mt as g,ut as i,jt as o,Bt as r,Kt as s,qt as t};
