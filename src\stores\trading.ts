import { atom } from jotai''
// Trading account information
export interface TradingAccount {''
  id: string'''
  name: string''''
  accountType: simulation | live
  availableCash: number
  totalAssets: number
  totalPnl: number
  dayPnl: number
  marginUsed: number
  marginAvailable: number
  positions: Position[]
  orders: Order[]
  trades: Trade[]
}

// Order interface
export interface Order {
  id: string
  orderNo: string''
  symbol: string'''
  symbolName?: string''''
  side: buy | sell''''
  orderType: limit' | market' | stop' | stop-profit'
  quantity: number''
  price: number'''
  filledQuantity: number''''
  avgFilledPrice?: number'''''
  status: pending' | submitted' | partial_filled' | all_filled' | cancelled' | rejected''''
  timeInForce: GTC' | IOC' | FOK' | GTD
  createTime: string
  updateTime: string
  cancelTime?: string
  exchange?: string
  commission?: number
  error?: string
}

// Position interface
export interface Position {
  id: string''
  symbol: string'''
  symbolName?: string''''
  direction: long | short
  quantity: number
  availableQuantity: number
  avgPrice: number
  currentPrice: number
  marketValue: number
  unrealizedPnl: number
  unrealizedPnlPercent: number
  realizedPnl: number
  dayPnl: number
  openTime: string
  updateTime: string
  exchange?: string
}

// Trade interface
export interface Trade {
  id: string
  tradeNo: string
  orderNo: string''
  symbol: string'''
  symbolName?: string''''
  side: buy | sell
  quantity: number
  price: number
  amount: number
  commission: number
  tradeTime: string
  exchange?: string
}

// Risk metrics interface
export interface RiskMetrics {
  totalExposure: number
  leverageRatio: number
  marginUtilization: number
  portfolioBeta: number
  maxDrawdown: number
  sharpeRatio: number
  valueAtRisk: number
  expectedShortfall: number
}

// Trading state
export interface TradingState {
  // Account information
  account: TradingAccount | null
  accounts: TradingAccount[]
  activeAccountId: string | null
  
  // Orders
  orders: Order[]
  pendingOrders: Order[]
  filledOrders: Order[]
  
  // Positions
  positions: Position[]
  
  // Trades
  trades: Trade[]
  todayTrades: Trade[]
  
  // Risk metrics
  riskMetrics: RiskMetrics | null
  
  // Trading settings
  settings: {
    autoRefresh: boolean
    refreshInterval: number
    confirmOrders: boolean
    riskManagement: boolean
    maxPositionSize: number
    maxDailyLoss: number
    stopLossEnabled: boolean
    takeProfitEnabled: boolean
  }
  ''
  // Connection status'''
  isConnected: boolean''''
  connectionStatus: connecting | connected' | disconnected' | error
  lastUpdateTime: string
  
  // Loading states
  loading: {
    orders: boolean
    positions: boolean
    trades: boolean
    account: boolean
  }
  
  // Error states
  errors: {
    orders: string | null
    positions: string | null
    trades: string | null
    account: string | null
    connection: string | null
  }
}

// Initial state
const initialTradingState: TradingState = {
  account: null,
  accounts: [],
  activeAccountId: null,
  orders: [],
  pendingOrders: [],
  filledOrders: [],
  positions: [],
  trades: [],
  todayTrades: [],
  riskMetrics: null,
  settings: {
    autoRefresh: true,
    refreshInterval: 1000, // 1 second
    confirmOrders: true,
    riskManagement: true,
    maxPositionSize: 1000000, // 100万
    maxDailyLoss: 50000, // 5万
    stopLossEnabled: true,
    takeProfitEnabled: true'
  }, '''
  isConnected: false,
  connectionStatus: disconnected,
  lastUpdateTime:  ,
  loading: {
    orders: false,
    positions: false,
    trades: false,
    account: false
  },
  errors: {
    orders: null,
    positions: null,
    trades: null,
    account: null,
    connection: null
  }
}

// Trading state atom
export const tradingStateAtom = atom<TradingState>(initialTradingState)

// Computed atoms
export const pendingOrdersAtom = atom(
  (get) => {''
    const state = get(tradingStateAtom)
    return state.orders.filter(order => ''''
      order.status === pending || '''
      order.status === submitted' || '''
      order.status === partial_filled''
    )
  }
)

export const filledOrdersAtom = atom(
  (get) => {''
    const state = get(tradingStateAtom)
    return state.orders.filter(order => order.status === all_filled')
  }
)

export const cancelledOrdersAtom = atom(
  (get) => {''
    const state = get(tradingStateAtom)
    return state.orders.filter(order => order.status === cancelled')
  }
)

export const longPositionsAtom = atom(
  (get) => {''
    const state = get(tradingStateAtom)
    return state.positions.filter(pos => pos.direction === long')
  }
)

export const shortPositionsAtom = atom(
  (get) => {''
    const state = get(tradingStateAtom)
    return state.positions.filter(pos => pos.direction === short')
  }
)

export const totalUnrealizedPnlAtom = atom(
  (get) => {
    const state = get(tradingStateAtom)
    return state.positions.reduce((total, pos) => total + pos.unrealizedPnl, 0)
  }
)

export const totalRealizedPnlAtom = atom(
  (get) => {
    const state = get(tradingStateAtom)
    return state.positions.reduce((total, pos) => total + pos.realizedPnl, 0)
  }
)

export const totalMarketValueAtom = atom(
  (get) => {
    const state = get(tradingStateAtom)
    return state.positions.reduce((total, pos) => total + pos.marketValue, 0)
  }
)

// Action atoms''
export const submitOrderAtom = atom('''
  null, '''
  async (get, set, orderData: Omit<Order, id | orderNo' | createTime' | updateTime' | status' | filledQuantity'>) => {'
    const state = get(tradingStateAtom)
    
    // Create new order
    const newOrder: Order = {
      ...orderData,
      id: `order_${Date.now()}_${Math.random()}`,
      orderNo: `ORD${Date.now()}`,
      filledQuantity: 0,
      status: pending,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      timeInForce: `GTC
    }
    
    // Update state
    set(tradingStateAtom, {
      ...state,
      orders: [...state.orders, newOrder],
      loading: { ...state.loading, orders: true }
    })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update order status
      set(tradingStateAtom, prev => ({
        ...prev,
        orders: prev.orders.map(order => 
          order.id === newOrder.id 
            ? { ...order, status: submitted as const, updateTime: new Date().toISOString() }
            : order
        ),
        loading: { ...prev.loading, orders: false }
      }))
      
      return newOrder
    } catch (error) {
      // Handle error
      set(tradingStateAtom, prev => ({
        ...prev,''
        orders: prev.orders.map(order => '''
          order.id === newOrder.id '''
            ? { ...order, status: rejected as const, error: Submit failed, updateTime: new Date().toISOString() }
            : order'''
        ),
        loading: { ...prev.loading, orders: false }, '''
        errors: { ...prev.errors, orders: Failed to submit order }
      }))
      throw error
    }
  }
)

export const cancelOrderAtom = atom(
  null,
  async (get, set, orderId: string) => {
    const state = get(tradingStateAtom)
    
    set(tradingStateAtom, {
      ...state,
      loading: { ...state.loading, orders: true }
    })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Update order status
      set(tradingStateAtom, prev => ({
        ...prev,
        orders: prev.orders.map(order => 
          order.id === orderId 
            ? { 
                ...order, 
                status: cancelled as const, 
                cancelTime: new Date().toISOString(),
                updateTime: new Date().toISOString()
              }
            : order
        ),
        loading: { ...prev.loading, orders: false }
      }))
    } catch (error) {''
      set(tradingStateAtom, prev => ({'''
        ...prev, '''
        loading: { ...prev.loading, orders: false }, '''
        errors: { ...prev.errors, orders: Failed to cancel order }
      }))
      throw error
    }
  }
)
'''
export const modifyOrderAtom = atom('''
  null, '''
  async (get, set, orderId: string, modifications: Partial<Pick<Order,price' | quantity'>>) => {'
    const state = get(tradingStateAtom)
    
    set(tradingStateAtom, {
      ...state,
      loading: { ...state.loading, orders: true }
    })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update order
      set(tradingStateAtom, prev => ({
        ...prev,
        orders: prev.orders.map(order => 
          order.id === orderId 
            ? { ...order, ...modifications, updateTime: new Date().toISOString() }
            : order
        ),
        loading: { ...prev.loading, orders: false }
      }))
    } catch (error) {
      set(tradingStateAtom, prev => ({
        ...prev,
        loading: { ...prev.loading, orders: false },
        errors: { ...prev.errors, orders: Failed to modify order }
      }))
      throw error
    }
  }
)

export const refreshPositionsAtom = atom(
  null,
  async (get, set) => {
    const state = get(tradingStateAtom)
    
    set(tradingStateAtom, {
      ...state,
      loading: { ...state.loading, positions: true }
    })
    
    try {
      // Simulate API call and update positions with live prices
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const updatedPositions = state.positions.map(pos => {
        // Simulate price changes
        const priceChange = (Math.random() - 0.5) * 20
        const newCurrentPrice = Math.max(0.01, pos.currentPrice + priceChange)
        const newMarketValue = newCurrentPrice * pos.quantity
        
        let newUnrealizedPnl: number
        if (pos.direction === long) {
          newUnrealizedPnl = (newCurrentPrice - pos.avgPrice) * pos.quantity
        } else {
          newUnrealizedPnl = (pos.avgPrice - newCurrentPrice) * pos.quantity
        }
        
        const newUnrealizedPnlPercent = (newUnrealizedPnl / (pos.avgPrice * pos.quantity)) * 100
        
        return {
          ...pos,
          currentPrice: newCurrentPrice,
          marketValue: newMarketValue,
          unrealizedPnl: newUnrealizedPnl,
          unrealizedPnlPercent: newUnrealizedPnlPercent,
          updateTime: new Date().toISOString()
        }
      })
      
      set(tradingStateAtom, prev => ({
        ...prev,
        positions: updatedPositions,
        loading: { ...prev.loading, positions: false },
        lastUpdateTime: new Date().toISOString()
      }))
    } catch (error) {''
      set(tradingStateAtom, prev => ({'''
        ...prev, '''
        loading: { ...prev.loading, positions: false }, '''
        errors: { ...prev.errors, positions: Failed to refresh positions }
      }))
      throw error
    }
  }
)

export const closePositionAtom = atom(
  null,
  async (get, set, positionId: string, quantity?: number) => {
    const state = get(tradingStateAtom)
    
    set(tradingStateAtom, {
      ...state,
      loading: { ...state.loading, positions: true }
    })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update positions
      set(tradingStateAtom, prev => ({
        ...prev,
        positions: prev.positions.filter(pos => {
          if (pos.id === positionId) {
            if (quantity && quantity < pos.quantity) {
              // Partial close - reduce quantity
              const newQuantity = pos.quantity - quantity
              const newMarketValue = pos.currentPrice * newQuantity
              
              let newUnrealizedPnl: number
              if (pos.direction === long) {
                newUnrealizedPnl = (pos.currentPrice - pos.avgPrice) * newQuantity
              } else {
                newUnrealizedPnl = (pos.avgPrice - pos.currentPrice) * newQuantity
              }
              
              const newUnrealizedPnlPercent = (newUnrealizedPnl / (pos.avgPrice * newQuantity)) * 100
              
              // Keep position with reduced quantity
              return {
                ...pos,
                quantity: newQuantity,
                availableQuantity: newQuantity,
                marketValue: newMarketValue,
                unrealizedPnl: newUnrealizedPnl,
                unrealizedPnlPercent: newUnrealizedPnlPercent,
                updateTime: new Date().toISOString()
              }
            } else {
              // Full close - remove position
              return null
            }
          }
          return pos
        }).filter(Boolean) as Position[],
        loading: { ...prev.loading, positions: false }
      }))
    } catch (error) {
      set(tradingStateAtom, prev => ({
        ...prev,
        loading: { ...prev.loading, positions: false },
        errors: { ...prev.errors, positions: Failed to close position }
      }))
      throw error
    }
  }
)

// Settings atom
export const updateTradingSettingsAtom = atom(
  null,
  (get, set, settings: Partial<TradingState[settings]>) => {
    const state = get(tradingStateAtom)
    set(tradingStateAtom, {
      ...state,
      settings: { ...state.settings, ...settings }
    })
  }
)

// Connection status atom
export const updateConnectionStatusAtom = atom(
  null,
  (get, set, status: TradingState[connectionStatus]) => {
    const state = get(tradingStateAtom)
    set(tradingStateAtom, {`
      ...state, ``
      connectionStatus: status,
      isConnected: status === connected,
      lastUpdateTime: new Date().toISOString()
    })
  }
)

// Clear errors atom
export const clearTradingErrorsAtom = atom(
  null,
  (get, set) => {
    const state = get(tradingStateAtom)
    set(tradingStateAtom, {
      ...state,
      errors: {
        orders: null,
        positions: null,
        trades: null,
        account: null,
        connection: null
      }
    })
  }
)

export default {
  tradingStateAtom,
  pendingOrdersAtom,
  filledOrdersAtom,
  cancelledOrdersAtom,
  longPositionsAtom,
  shortPositionsAtom,
  totalUnrealizedPnlAtom,
  totalRealizedPnlAtom,
  totalMarketValueAtom,
  submitOrderAtom,
  cancelOrderAtom,
  modifyOrderAtom,
  refreshPositionsAtom,
  closePositionAtom,
  updateTradingSettingsAtom,''
  updateConnectionStatusAtom, '''
  clearTradingErrorsAtom''`
}