/**
 * 交易状态管理 - 基于 SolidJS Store
 */
import { createStore } from 'solid-js/store';

// Trading account information
export interface TradingAccount {
  id: string;
  name: string;
  accountType: 'simulation' | 'live';
  availableCash: number;
  totalAssets: number;
  totalPnl: number;
  dayPnl: number;
  marginUsed: number;
  marginAvailable: number;
  positions: Position[];
  orders: Order[];
  trades: Trade[];
}

// Order interface
export interface Order {
  id: string;
  orderNo: string;
  symbol: string;
  symbolName?: string;
  side: 'buy' | 'sell';
  orderType: 'limit' | 'market' | 'stop' | 'stop-profit';
  quantity: number;
  price: number;
  filledQuantity: number;
  avgFilledPrice?: number;
  status: 'pending' | 'submitted' | 'partial_filled' | 'all_filled' | 'cancelled' | 'rejected';
  timeInForce: 'GTC' | 'IOC' | 'FOK' | 'GTD';
  createTime: string;
  updateTime: string;
  cancelTime?: string;
  exchange?: string;
  commission?: number;
  error?: string;
}

// Position interface
export interface Position {
  id: string;
  symbol: string;
  symbolName?: string;
  direction: 'long' | 'short';
  quantity: number;
  availableQuantity: number;
  avgPrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnl: number;
  unrealizedPnlPercent: number;
  realizedPnl: number;
  dayPnl: number;
  openTime: string;
  updateTime: string;
  exchange?: string;
}

// Trade interface
export interface Trade {
  id: string;
  tradeNo: string;
  orderNo: string;
  symbol: string;
  symbolName?: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  amount: number;
  commission: number;
  tradeTime: string;
  exchange?: string;
}

// Risk metrics interface
export interface RiskMetrics {
  totalExposure: number;
  leverageRatio: number;
  marginUtilization: number;
  portfolioBeta: number;
  maxDrawdown: number;
  sharpeRatio: number;
  valueAtRisk: number;
  expectedShortfall: number;
}

// Trading state
export interface TradingState {
  // Account information
  account: TradingAccount | null;
  accounts: TradingAccount[];
  activeAccountId: string | null;
  
  // Orders
  orders: Order[];
  pendingOrders: Order[];
  filledOrders: Order[];
  
  // Positions
  positions: Position[];
  
  // Trades
  trades: Trade[];
  todayTrades: Trade[];
  
  // Risk metrics
  riskMetrics: RiskMetrics | null;
  
  // Trading settings
  settings: {
    autoRefresh: boolean;
    refreshInterval: number;
    confirmOrders: boolean;
    riskManagement: boolean;
    maxPositionSize: number;
    maxDailyLoss: number;
    stopLossEnabled: boolean;
    takeProfitEnabled: boolean;
  };
  
  // Connection status
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastUpdateTime: string;
  
  // Loading states
  loading: {
    orders: boolean;
    positions: boolean;
    trades: boolean;
    account: boolean;
  };
  
  // Error states
  errors: {
    orders: string | null;
    positions: string | null;
    trades: string | null;
    account: string | null;
    connection: string | null;
  };
}

// Initial state
const initialTradingState: TradingState = {
  account: null,
  accounts: [],
  activeAccountId: null,
  orders: [],
  pendingOrders: [],
  filledOrders: [],
  positions: [],
  trades: [],
  todayTrades: [],
  riskMetrics: null,
  settings: {
    autoRefresh: true,
    refreshInterval: 1000, // 1 second
    confirmOrders: true,
    riskManagement: true,
    maxPositionSize: 1000000, // 100万
    maxDailyLoss: 50000, // 5万
    stopLossEnabled: true,
    takeProfitEnabled: true
  },
  isConnected: false,
  connectionStatus: 'disconnected',
  lastUpdateTime: '',
  loading: {
    orders: false,
    positions: false,
    trades: false,
    account: false
  },
  errors: {
    orders: null,
    positions: null,
    trades: null,
    account: null,
    connection: null
  }
};

// Trading state store
export const [tradingStore, setTradingStore] = createStore<TradingState>(initialTradingState);

// 交易操作函数
export const submitOrder = async (orderData: Omit<Order, 'id' | 'orderNo' | 'createTime' | 'updateTime' | 'status' | 'filledQuantity'>) => {
  // Create new order
  const newOrder: Order = {
    ...orderData,
    id: `order_${Date.now()}_${Math.random()}`,
    orderNo: `ORD${Date.now()}`,
    filledQuantity: 0,
    status: 'pending',
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString(),
    timeInForce: 'GTC'
  };
  
  // Update state
  setTradingStore('orders', orders => [...orders, newOrder]);
  setTradingStore('loading', 'orders', true);
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update order status
    setTradingStore('orders', order => order.id === newOrder.id, {
      status: 'submitted',
      updateTime: new Date().toISOString()
    });
    
    setTradingStore('loading', 'orders', false);
    return newOrder;
  } catch (error) {
    // Handle error
    setTradingStore('orders', order => order.id === newOrder.id, {
      status: 'rejected',
      error: 'Submit failed',
      updateTime: new Date().toISOString()
    });
    
    setTradingStore('loading', 'orders', false);
    setTradingStore('errors', 'orders', 'Failed to submit order');
    throw error;
  }
};

export const cancelOrder = async (orderId: string) => {
  setTradingStore('loading', 'orders', true);
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Update order status
    setTradingStore('orders', order => order.id === orderId, {
      status: 'cancelled',
      cancelTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    });
    
    setTradingStore('loading', 'orders', false);
  } catch (error) {
    setTradingStore('loading', 'orders', false);
    setTradingStore('errors', 'orders', 'Failed to cancel order');
    throw error;
  }
};

export const refreshPositions = async () => {
  setTradingStore('loading', 'positions', true);
  
  try {
    // Simulate API call and update positions with live prices
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const updatedPositions = tradingStore.positions.map(pos => {
      // Simulate price changes
      const priceChange = (Math.random() - 0.5) * 20;
      const newCurrentPrice = Math.max(0.01, pos.currentPrice + priceChange);
      const newMarketValue = newCurrentPrice * pos.quantity;
      
      let newUnrealizedPnl: number;
      if (pos.direction === 'long') {
        newUnrealizedPnl = (newCurrentPrice - pos.avgPrice) * pos.quantity;
      } else {
        newUnrealizedPnl = (pos.avgPrice - newCurrentPrice) * pos.quantity;
      }
      
      const newUnrealizedPnlPercent = (newUnrealizedPnl / (pos.avgPrice * pos.quantity)) * 100;
      
      return {
        ...pos,
        currentPrice: newCurrentPrice,
        marketValue: newMarketValue,
        unrealizedPnl: newUnrealizedPnl,
        unrealizedPnlPercent: newUnrealizedPnlPercent,
        updateTime: new Date().toISOString()
      };
    });
    
    setTradingStore('positions', updatedPositions);
    setTradingStore('loading', 'positions', false);
    setTradingStore('lastUpdateTime', new Date().toISOString());
  } catch (error) {
    setTradingStore('loading', 'positions', false);
    setTradingStore('errors', 'positions', 'Failed to refresh positions');
    throw error;
  }
};

// Settings functions
export const updateTradingSettings = (settings: Partial<TradingState['settings']>) => {
  setTradingStore('settings', current => ({ ...current, ...settings }));
};

// Connection status functions
export const updateConnectionStatus = (status: TradingState['connectionStatus']) => {
  setTradingStore('connectionStatus', status);
  setTradingStore('isConnected', status === 'connected');
  setTradingStore('lastUpdateTime', new Date().toISOString());
};

// Clear errors
export const clearTradingErrors = () => {
  setTradingStore('errors', {
    orders: null,
    positions: null,
    trades: null,
    account: null,
    connection: null
  });
};

// Export store and actions
export default {
  tradingStore,
  setTradingStore,
  submitOrder,
  cancelOrder,
  refreshPositions,
  updateTradingSettings,
  updateConnectionStatus,
  clearTradingErrors
};