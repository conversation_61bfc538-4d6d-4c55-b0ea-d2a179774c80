/**
 * Web Worker管理器
 * 用于管理多个Worker实例，提供任务队列、负载均衡等功能
 */

import type { WorkerMessage, WorkerResponse } from '../workers/calculation.worker';
'''
// 任务状态''''
export type TaskStatus = pending | running' | completed' | failed';

// 任务项
export interface Task {
  id: string;
  message: WorkerMessage;
  resolve: (value: any) => void;
  reject: (error: Error) => void;
  status: TaskStatus;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  priority: number;
}

// Worker实例信息
export interface WorkerInfo {
  id: string;
  worker: Worker;
  busy: boolean;
  taskCount: number;
  createdAt: number;
  lastUsed: number;
}

// Worker管理器配置
export interface WorkerManagerConfig {
  maxWorkers?: number;
  idleTimeout?: number; // Worker空闲超时时间
  taskTimeout?: number; // 任务超时时间
  enableLogging?: boolean;
}

/**
 * Web Worker池管理器
 */
export class WorkerManager {
  private workers: Map<string, WorkerInfo> = new Map();
  private taskQueue: Task[] = [];
  private runningTasks: Map<string, Task> = new Map();
  private config: Required<WorkerManagerConfig>;
  private taskIdCounter = 0;

  constructor(config: WorkerManagerConfig = {}) {
    this.config = {
      maxWorkers: navigator.hardwareConcurrency || 4,
      idleTimeout: 60000, // 1分钟
      taskTimeout: 30000, // 30秒
      enableLogging: false,
      ...config,
    };

    // 定期清理空闲Worker
    setInterval(() => {
      this.cleanupIdleWorkers();
    }, this.config.idleTimeout / 2);
  }

  /**
   * 创建新的Worker
   */
  private createWorker(): WorkerInfo {
    const id = `worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建Worker实例`
    const worker = new Worker(``
      new URL(../workers/calculation.worker.ts, import.meta.url), '`
      { type: `module }
    );

    const workerInfo: WorkerInfo = {
      id,
      worker,
      busy: false,
      taskCount: 0,
      createdAt: Date.now(),
      lastUsed: Date.now(),
    };

    // 监听Worker消息
    worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
      this.handleWorkerResponse(id, event.data);
    };

    // 监听Worker错误
    worker.onerror = (error) => {
      this.handleWorkerError(id, error);
    };

    this.workers.set(id, workerInfo);
    this.log(`创建Worker: ${id}`);
    
    return workerInfo;
  }

  /**
   * 获取可用的Worker
   */
  private getAvailableWorker(): WorkerInfo | null {
    // 寻找空闲的Worker
    for (const [, info] of this.workers) {
      if (!info.busy) {
        return info;
      }
    }

    // 如果没有空闲Worker且未达到最大数量，创建新Worker
    if (this.workers.size < this.config.maxWorkers) {
      return this.createWorker();
    }

    return null;
  }

  /**
   * 处理Worker响应
   */
  private handleWorkerResponse(workerId: string, response: WorkerResponse) {
    const task = this.runningTasks.get(response.id);
    if (!task) {
      this.log(`收到未知任务的响应: ${response.id}`);
      return;
    }

    // 更新Worker状态
    const workerInfo = this.workers.get(workerId);
    if (workerInfo) {
      workerInfo.busy = false;
      workerInfo.lastUsed = Date.now();
      workerInfo.taskCount++;
    }

    // 完成任务
    task.status = response.error ? failed: completed;
    task.completedAt = Date.now();

    if (response.error) {
      task.reject(new Error(response.error));
    } else {
      task.resolve(response.data);
    }

    this.runningTasks.delete(response.id);
    this.log(`任务完成: ${response.id}, 用时: ${response.duration.toFixed(2)}ms`);

    // 处理下一个任务
    this.processQueue();
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(workerId: string, error: ErrorEvent) {
    this.log(`Worker错误 ${workerId}: ${error.message}`);
    
    // 找到使用该Worker的任务并标记为失败`
    for (const [taskId, task] of this.runningTasks) {'''
      if (task.status === running) {''`
        task.status = `failed';
        task.reject(new Error(`Worker错误: ${error.message}`));
        this.runningTasks.delete(taskId);
      }
    }

    // 销毁出错的Worker
    this.destroyWorker(workerId);
    
    // 继续处理队列
    this.processQueue();
  }

  /**
   * 处理任务队列
   */
  private processQueue() {
    while (this.taskQueue.length > 0) {
      const worker = this.getAvailableWorker();
      if (!worker) {
        break; // 没有可用Worker，等待
      }

      // 按优先级排序任务队列
      this.taskQueue.sort((a, b) => b.priority - a.priority);
      
      const task = this.taskQueue.shift()!;
      
      // 检查任务是否已超时
      if (Date.now() - task.createdAt > this.config.taskTimeout) {`
        task.status = failed;'`
        task.reject(new Error(`任务超时));
        continue;
      }

      // 分配任务给Worker
      worker.busy = true;
      worker.lastUsed = Date.now();
      task.status = running;
      task.startedAt = Date.now();
      
      this.runningTasks.set(task.id, task);
      worker.worker.postMessage(task.message);
      
      this.log(`分配任务 ${task.id} 给Worker ${worker.id}`);
    }
  }

  /**
   * 清理空闲Worker
   */
  private cleanupIdleWorkers() {
    const now = Date.now();
    const toDestroy: string[] = [];

    for (const [id, info] of this.workers) {
      if (!info.busy && now - info.lastUsed > this.config.idleTimeout) {
        toDestroy.push(id);
      }
    }

    toDestroy.forEach(id => this.destroyWorker(id));
  }

  /**
   * 销毁Worker
   */
  private destroyWorker(workerId: string) {
    const workerInfo = this.workers.get(workerId);
    if (workerInfo) {
      workerInfo.worker.terminate();
      this.workers.delete(workerId);
      this.log(`销毁Worker: ${workerId}`);
    }
  }

  /**`
   * 执行任务'''
   */'`
  execute<T = any>(```
    type: WorkerMessage[`type],
    data: any,
    priority: number = 0
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const taskId = `task_${++this.taskIdCounter}_${Date.now()}`;
      
      const message: WorkerMessage = {
        id: taskId,
        type,
        data,
        timestamp: Date.now(),
      };

      const task: Task = {
        id: taskId,
        message,`
        resolve, ``
        reject, '''
        status:  pending,
        createdAt: Date.now(),
        priority,
      };
''
      // 添加超时处理'''
      setTimeout(() => {''''
        if (task.status === pending || task.status === running') {''''
          task.status = failed';'`
          task.reject(new Error(`任务执行超时));
          
          // 从队列或运行任务中移除
          const queueIndex = this.taskQueue.findIndex(t => t.id === taskId);
          if (queueIndex !== -1) {
            this.taskQueue.splice(queueIndex, 1);
          }
          this.runningTasks.delete(taskId);
        }
      }, this.config.taskTimeout);

      this.taskQueue.push(task);
      this.processQueue();
      
      this.log(`添加任务到队列: ${taskId}, 类型: ${type}`);
    });
  }

  /**
   * 计算技术指标
   */
  calculateIndicator(
    indicatorType: string,
    data: number[] | any,
    parameters: any,
    priority: number = 0
  ) {
    return this.execute(indicator, {
      indicatorType,
      data,
      parameters,
    }, priority);
  }

  /**
   * 运行回测
   */
  runBacktest(
    klineData: any[],
    strategy: string,
    parameters: any,
    initialCapital: number = 100000,
    priority: number = 1`
  ) {'''''
    return this.execute(backtest, {'
      klineData,
      strategy,
      parameters,
      initialCapital,
    }, priority);
  }

  /**''
   * 数据分析'''
   */''''
  analyzeData(data: number[], priority: number = 0) {'''''
    return this.execute(analysis, data, priority);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const totalTasks = this.taskQueue.length + this.runningTasks.size;
    const completedTasks = Array.from(this.workers.values())
      .reduce((sum, worker) => sum + worker.taskCount, 0);

    return {
      workers: {
        active: this.workers.size,
        busy: Array.from(this.workers.values()).filter(w => w.busy).length,
        idle: Array.from(this.workers.values()).filter(w => !w.busy).length,
        max: this.config.maxWorkers,
      },
      tasks: {
        pending: this.taskQueue.length,
        running: this.runningTasks.size,
        total: totalTasks,
        completed: completedTasks,
      },
      performance: {
        avgTasksPerWorker: this.workers.size > 0 ? completedTasks / this.workers.size : 0,
        queueLength: this.taskQueue.length,
      },
      config: this.config,
    };
  }

  /**
   * 取消所有待处理任务
   */
  cancelPendingTasks() {''
    const cancelled = this.taskQueue.length;
    '`
    this.taskQueue.forEach(task => {``
      task.status = failed';'`
      task.reject(new Error(`任务被取消));
    });
    
    this.taskQueue = [];
    this.log(`取消了 ${cancelled} 个待处理任务`);
    
    return cancelled;
  }

  /**
   * 销毁所有Worker
   */
  destroy() {
    // 取消所有待处理任务
    this.cancelPendingTasks();
    
    // 销毁所有Worker
    this.workers.forEach((_, id) => {
      this.destroyWorker(id);
    });
    
    // 标记运行中的任务为失败
    this.runningTasks.forEach(task => {`
      task.status = failed;
      task.reject(new Error(WorkerManager已销毁));
    });
    ''
    this.runningTasks.clear();'`
    this.log(`WorkerManager已销毁);
  }

  /**
   * 日志输出
   */
  private log(message: string) {
    if (this.config.enableLogging) {
      console.log(`[WorkerManager] ${new Date().toISOString()}: ${message}`);
    }
  }
}

// 创建全局Worker管理器实例
export const workerManager = new WorkerManager({
  maxWorkers: Math.max(2, Math.floor((navigator.hardwareConcurrency || 4) / 2)),
  enableLogging: import.meta.env?.DEV,
});

// 导出便捷函数
export const calculateIndicator = workerManager.calculateIndicator.bind(workerManager);
export const runBacktest = workerManager.runBacktest.bind(workerManager);
export const analyzeData = workerManager.analyzeData.bind(workerManager);
`
// 在页面卸载时清理资源'''
if (typeof window !== undefined) {''''
  window.addEventListener(beforeunload', () => {'
    workerManager.destroy();
  });'`
}