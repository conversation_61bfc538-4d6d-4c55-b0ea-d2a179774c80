const q=(e,t)=>e===t,A={equals:q};let P=F;const d=1,x=2,j={owned:null,cleanups:null,context:null,owner:null};var c=null;let O=null,Q=null,u=null,h=null,g=null,v=0;function W(e,t){const s=u,n=c,i=e.length===0,o=t===void 0?n:t,r=i?j:{owned:null,cleanups:null,context:o?o.context:null,owner:o},l=i?e:()=>e(()=>N(()=>y(r)));c=r,u=null;try{return E(l,!0)}finally{u=s,c=n}}function se(e,t){t=t?Object.assign({},A,t):A;const s={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},n=i=>(typeof i=="function"&&(i=i(s.value)),V(s,i));return[I.bind(s),n]}function B(e,t,s){const n=L(e,t,!1,d);S(n)}function X(e,t,s){P=Y;const n=L(e,t,!1,d);n.user=!0,g?g.push(n):S(n)}function k(e,t,s){s=s?Object.assign({},A,s):A;const n=L(e,t,!0,0);return n.observers=null,n.observerSlots=null,n.comparator=s.equals||void 0,S(n),I.bind(n)}function N(e){if(u===null)return e();const t=u;u=null;try{return e()}finally{u=t}}function ne(e){X(()=>N(e))}function le(e){return c===null||(c.cleanups===null?c.cleanups=[e]:c.cleanups.push(e)),e}function I(){if(this.sources&&this.state)if(this.state===d)S(this);else{const e=h;h=null,E(()=>m(this),!1),h=e}if(u){const e=this.observers?this.observers.length:0;u.sources?(u.sources.push(this),u.sourceSlots.push(e)):(u.sources=[this],u.sourceSlots=[e]),this.observers?(this.observers.push(u),this.observerSlots.push(u.sources.length-1)):(this.observers=[u],this.observerSlots=[u.sources.length-1])}return this.value}function V(e,t,s){let n=e.value;return(!e.comparator||!e.comparator(n,t))&&(e.value=t,e.observers&&e.observers.length&&E(()=>{for(let i=0;i<e.observers.length;i+=1){const o=e.observers[i],r=O&&O.running;r&&O.disposed.has(o),(r?!o.tState:!o.state)&&(o.pure?h.push(o):g.push(o),o.observers&&G(o)),r||(o.state=d)}if(h.length>1e6)throw h=[],new Error},!1)),t}function S(e){if(!e.fn)return;y(e);const t=v;J(e,e.value,t)}function J(e,t,s){let n;const i=c,o=u;u=c=e;try{n=e.fn(t)}catch(r){return e.pure&&(e.state=d,e.owned&&e.owned.forEach(y),e.owned=null),e.updatedAt=s+1,R(r)}finally{u=o,c=i}(!e.updatedAt||e.updatedAt<=s)&&(e.updatedAt!=null&&"observers"in e?V(e,n):e.value=n,e.updatedAt=s)}function L(e,t,s,n=d,i){const o={fn:e,state:n,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:c,context:c?c.context:null,pure:s};return c===null||c!==j&&(c.owned?c.owned.push(o):c.owned=[o]),o}function C(e){if(e.state===0)return;if(e.state===x)return m(e);if(e.suspense&&N(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<v);)e.state&&t.push(e);for(let s=t.length-1;s>=0;s--)if(e=t[s],e.state===d)S(e);else if(e.state===x){const n=h;h=null,E(()=>m(e,t[0]),!1),h=n}}function E(e,t){if(h)return e();let s=!1;t||(h=[]),g?s=!0:g=[],v++;try{const n=e();return K(s),n}catch(n){s||(g=null),h=null,R(n)}}function K(e){if(h&&(F(h),h=null),e)return;const t=g;g=null,t.length&&E(()=>P(t),!1)}function F(e){for(let t=0;t<e.length;t++)C(e[t])}function Y(e){let t,s=0;for(t=0;t<e.length;t++){const n=e[t];n.user?e[s++]=n:C(n)}for(t=0;t<s;t++)C(e[t])}function m(e,t){e.state=0;for(let s=0;s<e.sources.length;s+=1){const n=e.sources[s];if(n.sources){const i=n.state;i===d?n!==t&&(!n.updatedAt||n.updatedAt<v)&&C(n):i===x&&m(n,t)}}}function G(e){for(let t=0;t<e.observers.length;t+=1){const s=e.observers[t];s.state||(s.state=x,s.pure?h.push(s):g.push(s),s.observers&&G(s))}}function y(e){let t;if(e.sources)for(;e.sources.length;){const s=e.sources.pop(),n=e.sourceSlots.pop(),i=s.observers;if(i&&i.length){const o=i.pop(),r=s.observerSlots.pop();n<i.length&&(o.sourceSlots[r]=n,i[n]=o,s.observerSlots[n]=r)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)y(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)y(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function Z(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function R(e,t=c){throw Z(e)}function ie(e,t){return N(()=>e(t||{}))}const oe=e=>k(()=>e());function z(e,t,s){let n=s.length,i=t.length,o=n,r=0,l=0,f=t[i-1].nextSibling,a=null;for(;r<i||l<o;){if(t[r]===s[l]){r++,l++;continue}for(;t[i-1]===s[o-1];)i--,o--;if(i===r){const p=o<n?l?s[l-1].nextSibling:s[o-l]:f;for(;l<o;)e.insertBefore(s[l++],p)}else if(o===l)for(;r<i;)(!a||!a.has(t[r]))&&t[r].remove(),r++;else if(t[r]===s[o-1]&&s[l]===t[i-1]){const p=t[--i].nextSibling;e.insertBefore(s[l++],t[r++].nextSibling),e.insertBefore(s[--o],p),t[i]=s[o]}else{if(!a){a=new Map;let w=l;for(;w<o;)a.set(s[w],w++)}const p=a.get(t[r]);if(p!=null)if(l<p&&p<o){let w=r,$=1,U;for(;++w<i&&w<o&&!((U=a.get(t[w]))==null||U!==p+$);)$++;if($>p-l){const H=t[r];for(;l<p;)e.insertBefore(s[l++],H)}else e.replaceChild(s[l++],t[r++])}else r++;else t[r++].remove()}}}const _="_$DX_DELEGATE";function re(e,t,s,n={}){let i;return W(o=>{i=o,t===document?e():ee(t,e(),t.firstChild?null:void 0,s)},n.owner),()=>{i(),t.textContent=""}}function fe(e,t,s,n){let i;const o=()=>{const l=document.createElement("template");return l.innerHTML=e,l.content.firstChild},r=()=>(i||(i=o())).cloneNode(!0);return r.cloneNode=r,r}function ue(e,t=window.document){const s=t[_]||(t[_]=new Set);for(let n=0,i=e.length;n<i;n++){const o=e[n];s.has(o)||(s.add(o),t.addEventListener(o,te))}}function ce(e,t){t==null?e.removeAttribute("class"):e.className=t}function ee(e,t,s,n){if(s!==void 0&&!n&&(n=[]),typeof t!="function")return T(e,t,n,s);B(i=>T(e,t(),i,s),n)}function te(e){let t=e.target;const s=`$$${e.type}`,n=e.target,i=e.currentTarget,o=f=>Object.defineProperty(e,"target",{configurable:!0,value:f}),r=()=>{const f=t[s];if(f&&!t.disabled){const a=t[`${s}Data`];if(a!==void 0?f.call(t,a,e):f.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&o(t.host),!0},l=()=>{for(;r()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const f=e.composedPath();o(f[0]);for(let a=0;a<f.length-2&&(t=f[a],!!r());a++){if(t._$host){t=t._$host,l();break}if(t.parentNode===i)break}}else l();o(n)}function T(e,t,s,n,i){for(;typeof s=="function";)s=s();if(t===s)return s;const o=typeof t,r=n!==void 0;if(e=r&&s[0]&&s[0].parentNode||e,o==="string"||o==="number"){if(o==="number"&&(t=t.toString(),t===s))return s;if(r){let l=s[0];l&&l.nodeType===3?l.data!==t&&(l.data=t):l=document.createTextNode(t),s=b(e,s,n,l)}else s!==""&&typeof s=="string"?s=e.firstChild.data=t:s=e.textContent=t}else if(t==null||o==="boolean")s=b(e,s,n);else{if(o==="function")return B(()=>{let l=t();for(;typeof l=="function";)l=l();s=T(e,l,s,n)}),()=>s;if(Array.isArray(t)){const l=[],f=s&&Array.isArray(s);if(D(l,t,s,i))return B(()=>s=T(e,l,s,n,!0)),()=>s;if(l.length===0){if(s=b(e,s,n),r)return s}else f?s.length===0?M(e,l,n):z(e,s,l):(s&&b(e),M(e,l));s=l}else if(t.nodeType){if(Array.isArray(s)){if(r)return s=b(e,s,n,t);b(e,s,null,t)}else s==null||s===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);s=t}}return s}function D(e,t,s,n){let i=!1;for(let o=0,r=t.length;o<r;o++){let l=t[o],f=s&&s[e.length],a;if(!(l==null||l===!0||l===!1))if((a=typeof l)=="object"&&l.nodeType)e.push(l);else if(Array.isArray(l))i=D(e,l,f)||i;else if(a==="function")if(n){for(;typeof l=="function";)l=l();i=D(e,Array.isArray(l)?l:[l],Array.isArray(f)?f:[f])||i}else e.push(l),i=!0;else{const p=String(l);f&&f.nodeType===3&&f.data===p?e.push(f):e.push(document.createTextNode(p))}}return i}function M(e,t,s=null){for(let n=0,i=t.length;n<i;n++)e.insertBefore(t[n],s)}function b(e,t,s,n){if(s===void 0)return e.textContent="";const i=n||document.createTextNode("");if(t.length){let o=!1;for(let r=t.length-1;r>=0;r--){const l=t[r];if(i!==l){const f=l.parentNode===e;!o&&!r?f?e.replaceChild(i,l):e.insertBefore(i,s):f&&l.remove()}else o=!0}}else e.insertBefore(i,s);return[i]}export{B as a,ce as b,se as c,ue as d,le as e,ie as f,ee as i,oe as m,ne as o,re as r,fe as t};
