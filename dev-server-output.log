
> quant-frontend@1.0.0 dev
> vite


  [32m[1mVITE[22m v5.4.19[39m  [2mready in [0m[1m585[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3000[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://**********:[1m3000[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://*************:[1m3000[22m/[39m
[2m13:23:30[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m13:25:26[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mtest-page.html[22m
[2m13:41:47[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mtest-status.html[22m
[2m13:46:26[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m13:46:26[22m [36m[1m[vite][22m[39m server restarted.
[2m14:26:30[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m14:26:30[22m [36m[1m[vite][22m[39m server restarted.
[2m14:43:39[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mtest-render.html[22m
[2m15:00:06[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mpublic/index.html[22m
[2m15:00:23[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
Re-optimizing dependencies because vite config has changed
[2m15:00:23[22m [36m[1m[vite][22m[39m server restarted.
Error: [31m  Failed to scan for dependencies from entries:
  C:/Users/<USER>/Desktop/frontend/b/index.html
C:/Users/<USER>/Desktop/frontend/b/test-page.html
C:/Users/<USER>/Desktop/frontend/b/test-render.html
C:/Users/<USER>/Desktop/frontend/b/test-status.html

  [39m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mExpected ":" but found "backtest"[0m

    src/pages/BacktestAnalysis.tsx:272:65:
[37m      272 │ ... ==='completed' ?'#f6ffed' [32mbacktest[37m.status ==='running' ? '#e6...
          │                               [32m~~~~~~~~[37m
          ╵                               [32m:[0m


[31mX [41;31m[[41;97mERROR[41;31m][0m [1mUnterminated string literal[0m

    src/pages/MarketData.tsx:28:23:
[37m      28 │  '// 行业板块数据[32m[37m
         ╵            [32m^[0m


[31mX [41;31m[[41;97mERROR[41;31m][0m [1mExpected ">" but found "'/>\n\n      {/* 优化建议 */}\n      <div class={css({\n        bg: '"[0m

    src/pages/ParameterOptimization.tsx:200:77:
[37m      200 │ ...rOptimizer onOptimizationComplete={handleOptimizationComplete}[32m'/>[37m
          │                                                                  [32m~~~[37m
          ╵                                                                  [32m>[0m


[31mX [41;31m[[41;97mERROR[41;31m][0m [1mUnterminated string literal[0m

    src/pages/StrategyEditor.tsx:44:24:
[37m      44 │      'profit': ''+8.3%',[32m[37m
         ╵                         [32m^[0m


    at failureErrorWithLog (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:1472:15)
    at C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:945:25
    at runOnEndCallbacks (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:1315:45)
    at buildResponseToResult (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:943:7)
    at C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:955:9
    at new Promise (<anonymous>)
    at requestCallbacks.on-end (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:954:54)
    at handleRequest (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:647:17)
    at handleIncomingPacket (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:672:7)
    at Socket.readFromStdout (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:600:7)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Readable.push (node:internal/streams/readable:278:10)
    at Pipe.onStreamRead (node:internal/stream_base_commons:190:23)
[2m15:30:52[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
▲ [WARNING] Duplicate key "build" in object literal [duplicate-object-key]

    vite.config.ts:28:2:
      28 │   build: {
         ╵   ~~~~~

  The original key "build" is here:

    vite.config.ts:9:2:
      9 │   build: {
        ╵   ~~~~~

[2m15:30:52[22m [36m[1m[vite][22m[39m server restarted.
[2m15:31:14[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
Re-optimizing dependencies because vite config has changed
[2m15:31:14[22m [36m[1m[vite][22m[39m server restarted.
[2m15:40:30[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
Re-optimizing dependencies because lockfile has changed
Failed to resolve dependency: [36mjotai[39m, present in 'optimizeDeps.include'
[2m15:40:30[22m [36m[1m[vite][22m[39m server restarted.
[2m15:40:35[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m15:40:35[22m [36m[1m[vite][22m[39m server restarted.
