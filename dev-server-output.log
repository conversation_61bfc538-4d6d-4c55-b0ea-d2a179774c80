
> quant-frontend@1.0.0 dev
> vite


  [32m[1mVITE[22m v5.4.19[39m  [2mready in [0m[1m585[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3000[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://**********:[1m3000[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://*************:[1m3000[22m/[39m
[2m13:23:30[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m13:25:26[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mtest-page.html[22m
[2m13:41:47[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mtest-status.html[22m
[2m13:46:26[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m13:46:26[22m [36m[1m[vite][22m[39m server restarted.
[2m14:26:30[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m14:26:30[22m [36m[1m[vite][22m[39m server restarted.
[2m14:43:39[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mtest-render.html[22m
[2m15:00:06[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mpublic/index.html[22m
[2m15:00:23[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
Re-optimizing dependencies because vite config has changed
[2m15:00:23[22m [36m[1m[vite][22m[39m server restarted.
Error: [31m  Failed to scan for dependencies from entries:
  C:/Users/<USER>/Desktop/frontend/b/index.html
C:/Users/<USER>/Desktop/frontend/b/test-page.html
C:/Users/<USER>/Desktop/frontend/b/test-render.html
C:/Users/<USER>/Desktop/frontend/b/test-status.html

  [39m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mExpected ":" but found "backtest"[0m

    src/pages/BacktestAnalysis.tsx:272:65:
[37m      272 │ ... ==='completed' ?'#f6ffed' [32mbacktest[37m.status ==='running' ? '#e6...
          │                               [32m~~~~~~~~[37m
          ╵                               [32m:[0m


[31mX [41;31m[[41;97mERROR[41;31m][0m [1mUnterminated string literal[0m

    src/pages/MarketData.tsx:28:23:
[37m      28 │  '// 行业板块数据[32m[37m
         ╵            [32m^[0m


[31mX [41;31m[[41;97mERROR[41;31m][0m [1mExpected ">" but found "'/>\n\n      {/* 优化建议 */}\n      <div class={css({\n        bg: '"[0m

    src/pages/ParameterOptimization.tsx:200:77:
[37m      200 │ ...rOptimizer onOptimizationComplete={handleOptimizationComplete}[32m'/>[37m
          │                                                                  [32m~~~[37m
          ╵                                                                  [32m>[0m


[31mX [41;31m[[41;97mERROR[41;31m][0m [1mUnterminated string literal[0m

    src/pages/StrategyEditor.tsx:44:24:
[37m      44 │      'profit': ''+8.3%',[32m[37m
         ╵                         [32m^[0m


    at failureErrorWithLog (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:1472:15)
    at C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:945:25
    at runOnEndCallbacks (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:1315:45)
    at buildResponseToResult (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:943:7)
    at C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:955:9
    at new Promise (<anonymous>)
    at requestCallbacks.on-end (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:954:54)
    at handleRequest (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:647:17)
    at handleIncomingPacket (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:672:7)
    at Socket.readFromStdout (C:\Users\<USER>\Desktop\frontend\b\node_modules\esbuild\lib\main.js:600:7)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Readable.push (node:internal/streams/readable:278:10)
    at Pipe.onStreamRead (node:internal/stream_base_commons:190:23)
[2m15:30:52[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
▲ [WARNING] Duplicate key "build" in object literal [duplicate-object-key]

    vite.config.ts:28:2:
      28 │   build: {
         ╵   ~~~~~

  The original key "build" is here:

    vite.config.ts:9:2:
      9 │   build: {
        ╵   ~~~~~

[2m15:30:52[22m [36m[1m[vite][22m[39m server restarted.
[2m15:31:14[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
Re-optimizing dependencies because vite config has changed
[2m15:31:14[22m [36m[1m[vite][22m[39m server restarted.
[2m15:40:30[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
Re-optimizing dependencies because lockfile has changed
Failed to resolve dependency: [36mjotai[39m, present in 'optimizeDeps.include'
[2m15:40:30[22m [36m[1m[vite][22m[39m server restarted.
[2m15:40:35[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m15:40:35[22m [36m[1m[vite][22m[39m server restarted.
[2m16:12:45[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./App.tsx" from "src/index.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/index.tsx[39m:2:16
[33m  1  |  import { createComponent as _$createComponent } from "solid-js/web";
  2  |  import { render } from "solid-js/web";
  3  |  import App from "./App.tsx";
     |                   ^
  4  |  import "./styles.css";
  5  |  import "../styled-system/styles.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 2)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
[2m16:13:49[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/App.tsx[22m
[2m16:14:33[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m16:14:33[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to load url /src/App.tsx (resolved id: C:/Users/<USER>/Desktop/frontend/b/src/App.tsx) in C:/Users/<USER>/Desktop/frontend/b/src/index.tsx. Does the file exist?
[2m16:14:33[22m [36m[1m[vite][22m[39m [33mhmr invalidate [39m[2m/src/App.tsx[22m
[2m16:14:33[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/App.tsx[22m
[2m16:14:34[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./App.tsx" from "src/index.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/index.tsx[39m:2:16
[33m  1  |  import { createComponent as _$createComponent } from "solid-js/web";
  2  |  import { render } from "solid-js/web";
  3  |  import App from "./App.tsx";
     |                   ^
  4  |  import "./styles.css";
  5  |  import "../styled-system/styles.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 2)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:62105:24)
[2m16:15:17[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./App.tsx" from "src/index.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/index.tsx[39m:2:16
[33m  1  |  import { createComponent as _$createComponent } from "solid-js/web";
  2  |  import { render } from "solid-js/web";
  3  |  import App from "./App.tsx";
     |                   ^
  4  |  import "./styles.css";
  5  |  import "../styled-system/styles.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 2)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:62105:24)
[2m16:15:28[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./App.tsx" from "src/index.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/index.tsx[39m:2:16
[33m  1  |  import { createComponent as _$createComponent } from "solid-js/web";
  2  |  import { render } from "solid-js/web";
  3  |  import App from "./App.tsx";
     |                   ^
  4  |  import "./styles.css";
  5  |  import "../styled-system/styles.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 2)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:62105:24)
[2m16:15:46[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/App.tsx[22m
[2m16:16:27[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m16:19:47[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
