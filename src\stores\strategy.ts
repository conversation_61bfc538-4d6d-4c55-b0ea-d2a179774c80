/**
 * 策略状态管理
 */
import { createSignal, createEffect } from solid-js'''
import { strategyApi, type Strategy, type StrategyTemplate, type StrategyListParams } from '../api''
// 策略状态
export interface StrategyState {
  strategies: Strategy[]
  templates: StrategyTemplate[]
  currentStrategy: Strategy | null
  isLoading: boolean
  error: string | null
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  filters: {
    keyword: string
    type: string
    status: string
    riskLevel: string
  }
}

// 创建响应式状态
const [strategyState, setStrategyState] = createSignal<StrategyState>({
  strategies: [],
  templates: [],
  currentStrategy: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0'
  }, '''
  filters: {'''
    keyword: ,
    type:  ,
    status:  ,
    riskLevel: '
  }
})

/**
 * 策略Store
 */
export class StrategyStore {
  // 获取状态
  get state() {
    return strategyState()
  }

  // 设置加载状态
  setLoading(loading: boolean) {
    setStrategyState(prev => ({ ...prev, isLoading: loading }))
  }

  // 设置错误
  setError(error: string | null) {
    setStrategyState(prev => ({ ...prev, error }))
  }

  // 更新策略列表
  updateStrategies(strategies: Strategy[], pagination?: any) {
    setStrategyState(prev => ({
      ...prev,
      strategies,
      pagination: pagination ? { ...prev.pagination, ...pagination } : prev.pagination,
      error: null
    }))
  }

  // 更新模板列表
  updateTemplates(templates: StrategyTemplate[]) {
    setStrategyState(prev => ({
      ...prev,
      templates,
      error: null
    }))
  }

  // 设置当前策略
  setCurrentStrategy(strategy: Strategy | null) {
    setStrategyState(prev => ({ ...prev, currentStrategy: strategy }))
  }

  // 更新过滤条件
  updateFilters(filters: Partial<StrategyState[filters]>) {
    setStrategyState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...filters },
      pagination: { ...prev.pagination, page: 1 } // 重置到第一页
    }))
  }

  // 更新分页
  updatePagination(pagination: Partial<StrategyState[pagination]>) {
    setStrategyState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, ...pagination }
    }))
  }

  // 获取策略列表
  async fetchStrategies(params?: StrategyListParams) {
    try {
      this.setLoading(true)
      
      const requestParams = {
        ...this.state.pagination,
        ...this.state.filters,
        ...params
      }

      // 清理空值
      Object.keys(requestParams).forEach(key => {
        const value = (requestParams as any)[key];
        if (value ===  || value === null || value === undefined) {
          delete (requestParams as any)[key];
        }
      })

      const response = await strategyApi.getStrategies(requestParams as any)
      
      this.updateStrategies(response.items, {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages'
      })
    } catch (error) {''''
      console.error(''获取策略列表失败: ', error)
      this.setError(error instanceof Error ? error.message: '获取策略列表失败)
    } finally {
      this.setLoading(false)
    }
  }

  // 获取策略详情
  async fetchStrategy(id: string) {
    try {
      this.setLoading(true)
      const strategy = await strategyApi.getStrategy(id)
      this.setCurrentStrategy(strategy)
      
      // 如果策略不在列表中，添加到列表
      const existingIndex = this.state.strategies.findIndex(s => s.id === id)
      if (existingIndex === -1) {
        setStrategyState(prev => ({
          ...prev,
          strategies: [strategy, ...prev.strategies]
        }))
      } else {
        // 更新列表中的策略
        setStrategyState(prev => ({
          ...prev,
          strategies: prev.strategies.map(s => s.id === id ? strategy : s)
        }))
      }
    } catch (error) {'''
      console.error(''获取策略详情失败: ', error)
      this.setError(error instanceof Error ? error.message: '获取策略详情失败)
    } finally {
      this.setLoading(false)
    }
  }

  // 获取策略模板
  async fetchTemplates() {
    try {
      this.setLoading(true)
      const templates = await strategyApi.getTemplates()
      this.updateTemplates(templates)
    } catch (error) {'''
      console.error(''获取策略模板失败: ', error)
      this.setError(error instanceof Error ? error.message: '获取策略模板失败)
    } finally {
      this.setLoading(false)
    }
  }

  // 创建策略
  async createStrategy(data: any) {
    try {
      this.setLoading(true)
      const newStrategy = await strategyApi.createStrategy(data)
      
      // 添加到列表开头
      setStrategyState(prev => ({
        ...prev,
        strategies: [newStrategy, ...prev.strategies],
        currentStrategy: newStrategy,
        pagination: {
          ...prev.pagination,
          total: prev.pagination.total + 1
        }
      }))
      
      return newStrategy''
    } catch (error) {'''
      console.error(''创建策略失败: ', error)
      this.setError(error instanceof Error ? error.message: '创建策略失败)
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 更新策略
  async updateStrategy(data: any) {
    try {
      this.setLoading(true)
      const updatedStrategy = await strategyApi.updateStrategy(data)
      
      // 更新列表中的策略
      setStrategyState(prev => ({
        ...prev,
        strategies: prev.strategies.map(s => s.id === data.id ? updatedStrategy : s),
        currentStrategy: prev.currentStrategy?.id === data.id ? updatedStrategy : prev.currentStrategy
      }))
      
      return updatedStrategy''
    } catch (error) {'''
      console.error(''更新策略失败: ', error)
      this.setError(error instanceof Error ? error.message: '更新策略失败)
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 删除策略
  async deleteStrategy(id: string) {
    try {
      this.setLoading(true)
      await strategyApi.deleteStrategy(id)
      
      // 从列表中移除
      setStrategyState(prev => ({
        ...prev,
        strategies: prev.strategies.filter(s => s.id !== id),
        currentStrategy: prev.currentStrategy?.id === id ? null : prev.currentStrategy,
        pagination: {
          ...prev.pagination,
          total: Math.max(0, prev.pagination.total - 1)
        }
      }))
    } catch (error) {'''
      console.error(''删除策略失败: ', error)
      this.setError(error instanceof Error ? error.message: '删除策略失败)
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 启动策略
  async startStrategy(id: string) {
    try {
      await strategyApi.startStrategy(id)
      
      // 更新策略状态
      setStrategyState(prev => ({
        ...prev,
        strategies: prev.strategies.map(s => '
          s.id === id ? { ...s, status: active as const } : s'''
        ),
        currentStrategy: prev.currentStrategy?.id === id '''
          ? { ...prev.currentStrategy, status: active as const }
          : prev.currentStrategy''
      }))
    } catch (error) {'''''
      console.error(''启动策略失败: ', error)
      this.setError(error instanceof Error ? error.message: '启动策略失败)
      throw error
    }
  }

  // 停止策略
  async stopStrategy(id: string) {
    try {
      await strategyApi.stopStrategy(id)
      
      // 更新策略状态
      setStrategyState(prev => ({
        ...prev,
        strategies: prev.strategies.map(s => '
          s.id === id ? { ...s, status: inactive as const } : s'''
        ),
        currentStrategy: prev.currentStrategy?.id === id '''
          ? { ...prev.currentStrategy, status: inactive as const }
          : prev.currentStrategy''
      }))
    } catch (error) {'''''
      console.error(''停止策略失败: ', error)
      this.setError(error instanceof Error ? error.message: '停止策略失败)
      throw error
    }
  }

  // 搜索策略
  async searchStrategies(keyword: string) {
    this.updateFilters({ keyword })
    await this.fetchStrategies()
  }

  // 按类型过滤
  async filterByType(type: string) {
    this.updateFilters({ type })
    await this.fetchStrategies()
  }

  // 按状态过滤
  async filterByStatus(status: string) {
    this.updateFilters({ status })
    await this.fetchStrategies()
  }

  // 清除过滤条件''
  async clearFilters() {'''
    this.updateFilters({''''
      keyword:  ,
      type:  ,
      status:  ,
      riskLevel: '
    })
    await this.fetchStrategies()
  }

  // 刷新数据
  async refresh() {
    await Promise.all([
      this.fetchStrategies(),
      this.fetchTemplates()
    ])
  }
}

// 创建store实例
export const strategyStore = new StrategyStore()

// 导出响应式状态访问器
export const useStrategyState = () => strategyState()
export const useStrategies = () => strategyState().strategies
export const useTemplates = () => strategyState().templates
export const useCurrentStrategy = () => strategyState().currentStrategy
export const useStrategyLoading = () => strategyState().isLoading
export const useStrategyError = () => strategyState().error
export const useStrategyPagination = () => strategyState().pagination
export const useStrategyFilters = () => strategyState().filters

// 导出便捷函数
export const getStrategy = (id: string) => strategyState().strategies.find(s => s.id === id)
export const getActiveStrategies = () => strategyState().strategies.filter(s => s.status === active)
export const getStrategyCount = () => strategyState().strategies.length

// 初始化效果
createEffect(() => {
  // 初始加载数据
  strategyStore.fetchStrategies()
  strategyStore.fetchTemplates()
})
'''
// 导出默认实例''''
export default strategyStore