// 技术指标计算工具
import { PrecisionCalculator } from './formatters;

export interface CandleData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

/**
 * 简单移动平均线 (SMA)
 */
export function calculateSMA(data: number[], period: number): number[] {
  if (data.length < period) return [];
  
  const result: number[] = [];
  
  for (let i = period - 1; i < data.length; i++) {
    const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val, 0);
    result.push(PrecisionCalculator.divide(sum, period));
  }
  
  return result;
}

/**
 * 指数移动平均线 (EMA)
 */
export function calculateEMA(data: number[], period: number): number[] {
  if (data.length < period) return [];
  
  const result: number[] = [];
  const multiplier = PrecisionCalculator.divide(2, period + 1);
  
  // 第一个EMA值使用SMA
  const firstSMA = data.slice(0, period).reduce((acc, val) => acc + val, 0) / period;
  result.push(firstSMA);
  
  // 计算后续EMA值
  for (let i = period; i < data.length; i++) {
    const ema = PrecisionCalculator.add(
      PrecisionCalculator.multiply(data[i], multiplier),
      PrecisionCalculator.multiply(result[result.length - 1], 1 - multiplier)
    );
    result.push(ema);
  }
  
  return result;
}

/**
 * 相对强弱指数 (RSI)
 */
export function calculateRSI(data: number[], period: number = 14): number[] {
  if (data.length < period + 1) return [];
  
  const gains: number[] = [];
  const losses: number[] = [];
  
  // 计算价格变化
  for (let i = 1; i < data.length; i++) {
    const change = PrecisionCalculator.subtract(data[i], data[i - 1]);
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }
  
  const result: number[] = [];
  
  // 计算第一个RSI值
  const avgGain = gains.slice(0, period).reduce((acc, val) => acc + val, 0) / period;
  const avgLoss = losses.slice(0, period).reduce((acc, val) => acc + val, 0) / period;
  
  if (avgLoss === 0) {
    result.push(100);
  } else {
    const rs = PrecisionCalculator.divide(avgGain, avgLoss);
    const rsi = PrecisionCalculator.subtract(100, PrecisionCalculator.divide(100, 1 + rs));
    result.push(rsi);
  }
  
  // 计算后续RSI值
  let currentAvgGain = avgGain;
  let currentAvgLoss = avgLoss;
  
  for (let i = period; i < gains.length; i++) {
    currentAvgGain = PrecisionCalculator.divide(
      PrecisionCalculator.add(
        PrecisionCalculator.multiply(currentAvgGain, period - 1),
        gains[i]
      ),
      period
    );
    
    currentAvgLoss = PrecisionCalculator.divide(
      PrecisionCalculator.add(
        PrecisionCalculator.multiply(currentAvgLoss, period - 1),
        losses[i]
      ),
      period
    );
    
    if (currentAvgLoss === 0) {
      result.push(100);
    } else {
      const rs = PrecisionCalculator.divide(currentAvgGain, currentAvgLoss);
      const rsi = PrecisionCalculator.subtract(100, PrecisionCalculator.divide(100, 1 + rs));
      result.push(rsi);
    }
  }
  
  return result;
}

/**
 * 布林带 (Bollinger Bands)
 */
export function calculateBollingerBands(
  data: number[], 
  period: number = 20, 
  stdDev: number = 2
): { upper: number[]; middle: number[]; lower: number[] } {
  const sma = calculateSMA(data, period);
  const upper: number[] = [];
  const lower: number[] = [];
  
  for (let i = 0; i < sma.length; i++) {
    const dataSlice = data.slice(i, i + period);
    const mean = sma[i];
    
    // 计算标准差
    const variance = dataSlice.reduce((acc, val) => {
      const diff = PrecisionCalculator.subtract(val, mean);
      return PrecisionCalculator.add(acc, PrecisionCalculator.multiply(diff, diff));
    }, 0) / period;
    
    const standardDeviation = Math.sqrt(variance);
    
    upper.push(PrecisionCalculator.add(mean, PrecisionCalculator.multiply(standardDeviation, stdDev)));
    lower.push(PrecisionCalculator.subtract(mean, PrecisionCalculator.multiply(standardDeviation, stdDev)));
  }
  
  return {
    upper,
    middle: sma,
    lower
  };
}

/**
 * MACD (Moving Average Convergence Divergence)
 */
export function calculateMACD(
  data: number[], 
  fastPeriod: number = 12, 
  slowPeriod: number = 26, 
  signalPeriod: number = 9
): { macd: number[]; signal: number[]; histogram: number[] } {
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);
  
  // 对齐数组长度
  const startIndex = slowPeriod - fastPeriod;
  const alignedFastEMA = fastEMA.slice(startIndex);
  
  // 计算MACD线
  const macd: number[] = [];
  for (let i = 0; i < Math.min(alignedFastEMA.length, slowEMA.length); i++) {
    macd.push(PrecisionCalculator.subtract(alignedFastEMA[i], slowEMA[i]));
  }
  
  // 计算信号线
  const signal = calculateEMA(macd, signalPeriod);
  
  // 计算柱状图
  const histogram: number[] = [];
  const signalStartIndex = macd.length - signal.length;
  
  for (let i = 0; i < signal.length; i++) {
    histogram.push(PrecisionCalculator.subtract(macd[signalStartIndex + i], signal[i]));
  }
  
  return { macd, signal, histogram };
}

/**
 * 随机指标 (Stochastic Oscillator)
 */
export function calculateStochastic(
  candles: CandleData[], 
  kPeriod: number = 14, 
  dPeriod: number = 3
): { k: number[]; d: number[] } {
  if (candles.length < kPeriod) return { k: [], d: [] };
  
  const k: number[] = [];
  
  for (let i = kPeriod - 1; i < candles.length; i++) {
    const slice = candles.slice(i - kPeriod + 1, i + 1);
    const highest = Math.max(...slice.map(c => c.high));
    const lowest = Math.min(...slice.map(c => c.low));
    const current = candles[i].close;
    
    if (highest === lowest) {
      k.push(50); // 避免除零
    } else {
      const kValue = PrecisionCalculator.multiply(
        PrecisionCalculator.divide(
          PrecisionCalculator.subtract(current, lowest),
          PrecisionCalculator.subtract(highest, lowest)
        ),
        100
      );
      k.push(kValue);
    }
  }
  
  const d = calculateSMA(k, dPeriod);
  
  return { k, d };
}

/**
 * 威廉指标 (Williams %R)
 */
export function calculateWilliamsR(candles: CandleData[], period: number = 14): number[] {
  if (candles.length < period) return [];
  
  const result: number[] = [];
  
  for (let i = period - 1; i < candles.length; i++) {
    const slice = candles.slice(i - period + 1, i + 1);
    const highest = Math.max(...slice.map(c => c.high));
    const lowest = Math.min(...slice.map(c => c.low));
    const current = candles[i].close;
    
    if (highest === lowest) {
      result.push(-50); // 避免除零
    } else {
      const williamsR = PrecisionCalculator.multiply(
        PrecisionCalculator.divide(
          PrecisionCalculator.subtract(highest, current),
          PrecisionCalculator.subtract(highest, lowest)
        ),
        -100
      );
      result.push(williamsR);
    }
  }
  
  return result;
}

/**
 * 平均真实波幅 (ATR)
 */
export function calculateATR(candles: CandleData[], period: number = 14): number[] {
  if (candles.length < 2) return [];
  
  const trueRanges: number[] = [];
  
  for (let i = 1; i < candles.length; i++) {
    const current = candles[i];
    const previous = candles[i - 1];
    
    const tr1 = PrecisionCalculator.subtract(current.high, current.low);
    const tr2 = Math.abs(PrecisionCalculator.subtract(current.high, previous.close));
    const tr3 = Math.abs(PrecisionCalculator.subtract(current.low, previous.close));
    
    trueRanges.push(Math.max(tr1, tr2, tr3));
  }
  
  return calculateSMA(trueRanges, period);
}

/**
 * 成交量加权平均价格 (VWAP)
 */
export function calculateVWAP(candles: CandleData[]): number[] {
  const result: number[] = [];
  let cumulativeVolume = 0;
  let cumulativeVolumePrice = 0;
  
  for (const candle of candles) {
    const typicalPrice = PrecisionCalculator.divide(
      PrecisionCalculator.add(
        PrecisionCalculator.add(candle.high, candle.low),
        candle.close
      ),
      3
    );
    
    const volumePrice = PrecisionCalculator.multiply(typicalPrice, candle.volume);
    
    cumulativeVolumePrice = PrecisionCalculator.add(cumulativeVolumePrice, volumePrice);
    cumulativeVolume = PrecisionCalculator.add(cumulativeVolume, candle.volume);
    
    if (cumulativeVolume > 0) {
      result.push(PrecisionCalculator.divide(cumulativeVolumePrice, cumulativeVolume));
    } else {
      result.push(typicalPrice);
    }
  }
  
  return result;
}

/**
 * 抛物线SAR
 */
export function calculateParabolicSAR(
  candles: CandleData[], 
  acceleration: number = 0.02, 
  maximum: number = 0.2
): number[] {
  if (candles.length < 2) return [];
  
  const result: number[] = [];
  let isUptrend = candles[1].close > candles[0].close;
  let sar = isUptrend ? candles[0].low : candles[0].high;
  let ep = isUptrend ? candles[1].high : candles[1].low;
  let af = acceleration;
  
  result.push(sar);
  
  for (let i = 1; i < candles.length; i++) {
    const current = candles[i];
    
    // 计算新的SAR值
    sar = PrecisionCalculator.add(sar, PrecisionCalculator.multiply(af, PrecisionCalculator.subtract(ep, sar)));
    
    if (isUptrend) {
      // 上升趋势
      if (current.low <= sar) {
        // 趋势反转
        isUptrend = false;
        sar = ep;
        ep = current.low;
        af = acceleration;
      } else {
        // 继续上升趋势
        if (current.high > ep) {
          ep = current.high;
          af = Math.min(af + acceleration, maximum);
        }
        // 确保SAR不高于前两个周期的最低价
        if (i >= 2) {
          sar = Math.min(sar, candles[i - 1].low, candles[i - 2].low);
        }
      }
    } else {
      // 下降趋势
      if (current.high >= sar) {
        // 趋势反转
        isUptrend = true;
        sar = ep;
        ep = current.high;
        af = acceleration;
      } else {
        // 继续下降趋势
        if (current.low < ep) {
          ep = current.low;
          af = Math.min(af + acceleration, maximum);
        }
        // 确保SAR不低于前两个周期的最高价
        if (i >= 2) {
          sar = Math.max(sar, candles[i - 1].high, candles[i - 2].high);
        }
      }
    }
    
    result.push(sar);
  }
  
  return result;
}