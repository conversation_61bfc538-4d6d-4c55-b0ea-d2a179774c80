import { createSignal, createMemo, Show, For } from solid-js'''
import { createStore } from solid-js/store'''
import { css } from '../../styled-system/css'''
import { formatCurrency, formatPercent } from '../../utils/formatters'''
import { useAtom } from jotai'''
import { tradingAtom } from '../../stores/atoms''
interface RiskMetrics {
  totalExposure: number
  leverageRatio: number
  marginUtilization: number
  portfolioBeta: number
  maxDrawdown: number
  sharpeRatio: number
  valueAtRisk: number
  expectedShortfall: number
}
''
interface RiskLimit {'''
  id: string''''
  name: string'''''
  type: position' |exposure' |loss' | drawdown'
  limit: number'''
  current: number''''
  warningThreshold: number'''''
  unit: amount' | percent
  enabled: boolean
}

interface Props {
  onRiskAlert?: (alert: RiskAlert) => void
  onLimitUpdate?: (limitId: string, newLimit: number) => void
}
'''
interface RiskAlert {''''
  id: string'''''
  level: warning' | critical
  message: string
  timestamp: string
  acknowledged: boolean
}

export function RiskControlPanel(props: Props) {
  const [trading] = useAtom(tradingAtom)
  const [showSettings, setShowSettings] = createSignal(false)
  const [alerts, setAlerts] = createStore<RiskAlert[]>([])
  
  // Mock risk metrics
  const [riskMetrics, setRiskMetrics] = createStore<RiskMetrics>({
    totalExposure: 2500000,
    leverageRatio: 2.5,
    marginUtilization: 65,
    portfolioBeta: 1.2,
    maxDrawdown: -8.5,
    sharpeRatio: 1.8,
    valueAtRisk: -45000,
    expectedShortfall: -68000
  })

  // Risk limits configuration
  const [riskLimits, setRiskLimits] = createStore<RiskLimit[]>([''
    { '''
      id: max_position_size,
      name: 单笔最大持仓,
      type: position,
      limit: 500000,
      current: 385000,
      warningThreshold: 80,
      unit:  amount,
      enabled: true
    },''
    { '''
      id: total_exposure,
      name: '总敞口限制,
      type: exposure,
      limit: 5000000,
      current: 2500000,
      warningThreshold: 85,
      unit:  amount,
      enabled: true
    },''
    { '''
      id: daily_loss,
      name: '日最大亏损,
      type: loss,
      limit: 50000,
      current: 12000,
      warningThreshold: 80,
      unit:  amount,
      enabled: true
    },''
    { '''
      id: max_drawdown,
      name: '最大回撤,
      type: drawdown,
      limit: 15,
      current: 8.5,
      warningThreshold: 80,
      unit:  percent,
      enabled: true
    }
  ])

  // Check for risk violations
  const checkRiskLimits = () => {
    riskLimits.forEach(limit => {
      if (!limit.enabled) return

      const utilizationPercent = (limit.current / limit.limit) * 100

      if (utilizationPercent >= 100) {
        const alert: RiskAlert = {'
          id: `alert_${Date.now()}_${limit.id}`, ``
          level: critical,
          message: `${limit.name}超限：当前${limit.unit ===`amount' ? formatCurrency(limit.current)': formatPercent(limit.current)}，限额${limit.unit ===amount' ? formatCurrency(limit.limit) : formatPercent(limit.limit)}`, `
          timestamp: new Date().toISOString(),
          acknowledged: false
        }
        setAlerts(prev => [...prev, alert])
        props.onRiskAlert?.(alert)
      } else if (utilizationPercent >= limit.warningThreshold) {'''
        const alert: RiskAlert = {`
          id: `alert_${Date.now()}_${limit.id}`,```
          level:  warning,
          message: `${limit.name}接近限额：当前利用率${utilizationPercent.toFixed(1)}%`,
          timestamp: new Date().toISOString(),
          acknowledged: false
        }
        setAlerts(prev => [...prev, alert])
        props.onRiskAlert?.(alert)
      }
    })
  }

  // Risk level calculation
  const overallRiskLevel = createMemo(() => {`
    const criticalAlerts = alerts.filter(alert => alert.level ===critical && !alert.acknowledged)
    const warningAlerts = alerts.filter(alert => alert.level ===warning' && !alert.acknowledged)
'''''
    if (criticalAlerts.length > 0) return'critical''''''
    if (warningAlerts.length > 0) return'warning''''''
    return'normal''
  })
'''
  const getRiskLevelColor = (level: string) => {''''
    switch (level) {'''''
      case'critical' : return'#ff4757''''''
      case'warning' : return'#ffa502''''''
      default: return'#2ed573'
    }
  }
'''
  const getRiskLevelText = (level: string) => {''''
    switch (level) {'''`
      case`critical' : return'高风险'```
      case`warning' : return'中风险``
      default: return'低风险'
    }
  }
'`
  const getUtilizationColor = (utilization: number, warningThreshold: number) => {```
    if (utilization >= 100) return`#ff4757```
    if (utilization >= warningThreshold) return`#ffa502``
    return'#2ed573'
  }

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ))
  }

  const updateRiskLimit = (limitId: string, newLimit: number) => {
    setRiskLimits(prev => prev.map(limit =>
      limit.id === limitId ? { ...limit, limit: newLimit } : limit
    ))
    props.onLimitUpdate?.(limitId, newLimit)
  }

  const toggleRiskLimit = (limitId: string) => {
    setRiskLimits(prev => prev.map(limit =>
      limit.id === limitId ? { ...limit, enabled: !limit.enabled } : limit
    ))
  }

  return (''
    <div class={css({ '''
      bg: white,
      borderRadius: '8px,
      padding: '20px,
      boxShadow:  0 2px 8px rgba(0, 0, 0, 0.1)
    })}>''
      {/* Header */}
      <div class={css({ ''''
        display', flex: $4,
        justifyContent: space-between,
        alignItems: center,
        marginBottom: '20px,
        paddingBottom: '12px,
        borderBottom: ''1px solid #ebeef5
      })}>'''
        <div class={css({ display:  flex, alignItems:  center, gap: '12px })}>'''
          <h3 class={css({ margin: 0, fontSize:  18px, color: '#303133 })}>'
            风险控制面板
          </h3>''
          <div class={css({ '''
            padding: '4px 12px,
            borderRadius: '16px,
            fontSize: '12px,
            fontWeight: bold,
            color:  white,
            bg: getRiskLevelColor(overallRiskLevel())
          })}>
            {getRiskLevelText(overallRiskLevel())}
          </div>
        </div>`
``
        <div class={css({ display:  flex, gap: '8px })}>'
          <button
            onClick={() => checkRiskLimits()}
            class={css({ '''
              padding: '6px 12px,
              border: '1px solid #dcdfe6,
              borderRadius: '4px,
              fontSize: '12px,
              cursor: pointer,
              bg: white,
              color: ''#606266
            })}
          >
            检查风险
          </button>
          <button
            onClick={() => setShowSettings(!showSettings())}
            class={css({ '''
              padding: '6px 12px,
              border: '1px solid #dcdfe6,
              borderRadius: '4px,
              fontSize: '12px,
              cursor: pointer,
              bg: showSettings() ?#409eff: white,
              color: showSettings() ?white: ''#606266
            })}
          >
            设置
          </button>
        </div>
      </div>

      {/* Risk Alerts */}
      <Show when={alerts.filter(a => !a.acknowledged).length > 0}>''
        <div class={css({ '''
          marginBottom: '20px,
          padding: '16px,
          bg: '#fef0e6,
          border: '1px solid #f5dab1,
          borderRadius: ''6px
        })}>''
          <h4 class={css({ '''
            margin: '0 0 12px 0,
            fontSize: '14px,
            color: '#e6a23c,
            fontWeight: bold
          })}>
            ⚠️ 风险警告''
          </h4>'''
          <div class={css({ display:  flex, flexDirection:  column, gap: '8px })}>'
            <For each={alerts.filter(alert => !alert.acknowledged)}>
              {(alert) => (''
                <div class={css({ '''
                  display: flex,
                  justifyContent: space-between,
                  alignItems: center,
                  padding: '8px 12px,
                  bg: alert.level ===critical' ?#fdf2f2: `#fff7ed,
                  border: `1px solid ${alert.level ===`critical' ?'#f5c6cb: '#f5dab1}`, `
                  borderRadius: '4px,
                  fontSize: ''14px
                })}>'''
                  <span class={css({'''''
                    color: alert.level ===critical' ?'#dc3545: ''#e6a23c
                  })}>
                    {alert.message}
                  </span>
                  <button
                    onClick={() => acknowledgeAlert(alert.id)}
                    class={css({ '''
                      padding: '4px 8px,
                      border: none,
                      bg: transparent,
                      color: '#909399,
                      fontSize: '12px,
                      cursor: pointer
                    })}
                  >
                    确认
                  </button>
                </div>
              )}
            </For>
          </div>
        </div>
      </Show>

      {/* Risk Metrics Overview */}
      <div class={css({ '''
        display: grid,
        gridTemplateColumns: repeat(4, 1fr), ''
        gap:  16px,
        marginBottom: '20px,@media (max-width: 768px)': { '''
          gridTemplateColumns: repeat(2, 1fr)
        }
      })}>'''
        <div class={css({ ''''
          padding', 16px: $4,
          bg: '#f5f7fa,
          borderRadius: '6px,
          textAlign: center
        })}>''
          <div class={css({ '''
            fontSize: '12px,
            color: '#909399,
            marginBottom: ''4px
          })}>
            总敞口
          </div>''
          <div class={css({ '''
            fontSize: '18px,
            fontWeight: bold,
            color: ''#303133
          })}>
            {formatCurrency(riskMetrics.totalExposure)}
          </div>
        </div>
''
        <div class={css({ '''
          padding: '16px,
          bg: '#f5f7fa,
          borderRadius: '6px,
          textAlign: center
        })}>''
          <div class={css({ '''
            fontSize: '12px,
            color: '#909399,
            marginBottom: ''4px
          })}>
            杠杆倍数
          </div>''
          <div class={css({ '''
            fontSize: '18px,
            fontWeight: bold,
            color: riskMetrics.leverageRatio > 3 ?'#f56c6c: ''#303133
          })}>
            {riskMetrics.leverageRatio.toFixed(1)}x
          </div>
        </div>
''
        <div class={css({ '''
          padding: '16px,
          bg: '#f5f7fa,
          borderRadius: '6px,
          textAlign: center
        })}>''
          <div class={css({ '''
            fontSize: '12px,
            color: '#909399,
            marginBottom: ''4px
          })}>
            保证金利用率
          </div>''
          <div class={css({ '''
            fontSize: '18px,
            fontWeight: bold,
            color: riskMetrics.marginUtilization > 80 ?'#f56c6c: ''#303133
          })}>
            {riskMetrics.marginUtilization}%
          </div>
        </div>
''
        <div class={css({ '''
          padding: '16px,
          bg: '#f5f7fa,
          borderRadius: '6px,
          textAlign: center
        })}>''
          <div class={css({ '''
            fontSize: '12px,
            color: '#909399,
            marginBottom: ''4px
          })}>
            夏普比率
          </div>''
          <div class={css({ '''
            fontSize: '18px,
            fontWeight: bold,
            color: riskMetrics.sharpeRatio > 1 ?#67c23a: ''#303133
          })}>
            {riskMetrics.sharpeRatio.toFixed(2)}
          </div>
        </div>
      </div>
''
      {/* Risk Limits */}
      <div class={css({ marginBottom: '20px })}>''
        <h4 class={css({ '''
          margin: '0 0 16px 0,
          fontSize: '16px,
          color: ''#303133
        })}>
          风险限额监控
        </h4>`
``
        <div class={css({ display:  flex, flexDirection:  column, gap: '12px })}>'
          <For each={riskLimits}>
            {(limit) => {
              const utilization = (limit.current / limit.limit) * 100
              return (''
                <div class={css({ '''
                  padding: '16px,
                  border: '1px solid #ebeef5,
                  borderRadius:  6px,
                  opacity: limit.enabled ? 1 : 0.6
                })}>''
                  <div class={css({ '''
                    display: flex,
                    justifyContent: space-between,
                    alignItems: center,
                    marginBottom: ''8px
                  })}>'''
                    <div class={css({ display:  flex, alignItems:  center, gap: '8px })}>'''
                      <span class={css({ fontSize:  14px, fontWeight: bold })}>'
                        {limit.name}
                      </span>''
                      <label class={css({ '''
                        display: flex,
                        alignItems: center,
                        gap: '4px,
                        fontSize: '12px,
                        color: '#909399,
                        cursor: pointer
                      })}>'''
                        <input''''
                          type=checkbox''
                          checked={limit.enabled}
                          onChange={() => toggleRiskLimit(limit.id)}
                        />
                        启用
                      </label>
                    </div>''
                    <div class={css({ '''
                      fontSize:  14px,
                      color: getUtilizationColor(utilization, limit.warningThreshold)
                    })}>
                      {utilization.toFixed(1)}%
                    </div>
                  </div>
''
                  <div class={css({ '''
                    display: flex,
                    justifyContent: space-between,
                    alignItems: center,
                    marginBottom: '8px,
                    fontSize: '12px,
                    color: ''#606266
                  })}>''
                    <span>'`
                      当前` : {limit.unit ===amount' ? formatCurrency(limit.current) : `${limit.current}%`}
                    </span>`
                    <span>'`
                      限额` : {limit.unit ===amount' ? formatCurrency(limit.limit) : `${limit.limit}%`}
                    </span>`
                  </div>'''
''''
                  <div class={css({''`
                    width: `100%,
                    height: `6px,
                    bg: '#f5f7fa,
                    borderRadius: '3px,
                    overflow: hidden
                  })}>'''
                    <div''''
                      class={css({''`
                        height: `100%,
                        borderRadius: `3px,
                        transition:  width 0.3s ease,
                        bg: getUtilizationColor(utilization, limit.warningThreshold)
                      })}
                      style={{ width: `${Math.min(utilization, 100)}%` }}
                    />
                  </div>

                  <Show when={showSettings()}>`
                    <div class={css({ '''
                      marginTop: '12px,
                      display: flex,
                      alignItems: center,
                      gap: ''8px
                    })}>'''
                      <label class={css({ fontSize:  12px, color: '#606266 })}>'
                        限额:''
                      </label>'''
                      <input''''
                        type=number''
                        value={limit.limit}
                        onInput={(e) => updateRiskLimit(limit.id, Number(e.currentTarget.value))}
                        class={css({ '''
                          width: '120px,
                          padding: '4px 8px,
                          border: '1px solid #dcdfe6,
                          borderRadius: '4px,
                          fontSize: ''12px
                        })}
                      />
                    </div>
                  </Show>
                </div>
              )
            }}
          </For>
        </div>
      </div>

      {/* Quick Actions */}
      <div class={css({ '''
        display: flex,
        gap: '8px,
        justifyContent: center
      })}>
        <button
          onClick={() => {
            // Emergency stop all trading
            console.log(Emergency stop triggered)
          }}
          class={css({ '''
            padding: '8px 16px,
            bg: '#f56c6c,
            color: white,
            border: none,
            borderRadius: '4px,
            fontSize: '14px,
            cursor: pointer,
            fontWeight: bold
          })}
        >
          紧急停止
        </button>
        <button
          onClick={() => {
            // Force close all positions
            console.log(Force close all positions)
          }}
          class={css({ '''
            padding: '8px 16px,
            bg: '#e6a23c,
            color: white,
            border: none,
            borderRadius: '4px,
            fontSize: '14px,
            cursor: pointer,
            fontWeight: bold
          })}
        >
          强制平仓
        </button>
      </div>
    </div>
  )
}`
```
export default RiskControlPanel