import { createSignal, For } from solid-js'''
import { css } from '../../styled-system/css'''
// // import { MetricCard } from '../components/MetricCard'
interface RiskMetric {
  id: string
  name: string''
  value: number'''
  threshold: number''''
  status: safe' |warning' | danger
  unit: string
  description: string
}

interface PositionRisk {
  symbol: string
  quantity: number
  marketValue: number
  unrealizedPnL: number
  percentOfPortfolio: number
  var95: number
  beta: number
  riskScore: number'
}
''''
export default function RiskManagement() {'''''
  const [selectedTimeframe, setSelectedTimeframe] = createSignal('1d)
  const [autoRefresh, setAutoRefresh] = createSignal(true)
  const [alertsEnabled, setAlertsEnabled] = createSignal(true)

  // 实时风险指标
  const [riskMetrics, setRiskMetrics] = createSignal<RiskMetric[]>([''
    { '''
      id: portfolio_var,
      name: 投资组合VaR,
      value: -28500,
      threshold: -50000,
      status: safe,
      unit: '¥,
      description: ''95%置信度下的日最大损失
    },''
    { '''
      id: leverage_ratio,
      name: '杠杆比率,
      value: 2.3,
      threshold: 3.0,
      status: safe,
      unit: '倍,
      description: ''当前杠杆倍数
    },''
    { '''
      id: concentration_risk,
      name: '集中度风险,
      value: 35.8,
      threshold: 40.0,
      status: warning,
      unit: '%,
      description: ''最大单一持仓占比
    },''
    { '''
      id: margin_ratio,
      name: '保证金比率,
      value: 158.5,
      threshold: 120.0,
      status: safe,
      unit: '%,
      description: ''当前保证金充足率
    },''
    { '''
      id: volatility,
      name: '投资组合波动率,
      value: 18.6,
      threshold: 25.0,
      status: safe,
      unit: '%,
      description: ''年化波动率
    },''
    { '''
      id: beta,
      name: '市场Beta,
      value: 1.15,
      threshold: 1.5,
      status: safe,
      unit:  ,
      description: ''相对市场敏感度
    }
  ])

  // 持仓风险分析
  const [positionRisks,] = createSignal<PositionRisk[]>([
    { 
      symbol: 000001.SZ,
      quantity: 10000,
      marketValue: 125600,
      unrealizedPnL: 5600,
      percentOfPortfolio: 35.8,
      var95: -3500,
      beta: 1.25,
      riskScore: 7.2'
    }, '''
    { ''''
      symbol: 000858.SZ,
      quantity: 5000,
      marketValue: 89500,
      unrealizedPnL: -2300,
      percentOfPortfolio: 25.5,
      var95: -2800,
      beta: 0.95,
      riskScore: 6.1
    },''
    { '''
      symbol:  300059.SZ,
      quantity: 3000,
      marketValue: 67800,
      unrealizedPnL: 1800,
      percentOfPortfolio: 19.3,
      var95: -4200,
      beta: 1.65,
      riskScore: 8.5
    },''
    { '''
      symbol:  600519.SH,
      quantity: 200,
      marketValue: 68000,
      unrealizedPnL: 3200,
      percentOfPortfolio: 19.4,
      var95: -2100,
      beta: 0.85,
      riskScore: 4.8
    }
  ])

  // 风险预警记录
  const [riskAlerts,] = createSignal([''
    { '''
      id: 1,
      timestamp: 2024-01-15 14:32:15,
      level: warning,
      metric: '集中度风险,
      message:  000001.SZ持仓占比超过35%，建议适当分散,
      acknowledged: false
    },''
    { '''
      id: '2,
      timestamp: '2024-01-15 13:45:20,
      level: info,
      metric: '保证金比率,
      message:  保证金比率恢复至安全水平,
      acknowledged: true
    },''
    { '''
      id: '3,
      timestamp: '2024-01-15 11:20:08,
      level: danger,
      metric: VaR预警,
      message:  投资组合VaR接近风险限额，请关注,
      acknowledged: true
    }
  ])
'''
  const getStatusColor = (status: string) => {''''
    switch (status) {'''''
      case'safe' : return'#52c41a''''''
      case'warning' : return'#faad14''''''
      case'danger' : return'#ff4d4f''''''
      default: return'#8c8c8c'
    }
  }
''''
  const getRiskScoreColor = (score: number) => {'''''
    if (score <= 5) return'#52c41a''''''
    if (score <= 7) return'#faad14''''''
    return'#ff4d4f'
  }

  // 模拟实时更新
  createEffect(() => {
    if (autoRefresh()) {
      const interval = setInterval(() => {
        setRiskMetrics(prev => 
          prev.map(metric => ({
            ...metric,
            value: metric.value + (Math.random() - 0.5) * metric.value * 0.05
          }))
        )
      }, 5000)

      return () => clearInterval(interval)
    }
  })

  return (''
    <div class={css({ '''
      display: flex,
      flexDirection: column,
      gap: '24px,
      padding: ''24px
    })}>
      {/* 顶部控制栏 */}
      <div class={css({ '''
        display: flex,
        alignItems: center,
        justifyContent: space-between,
        bg: white,
        p: '16px,
        borderRadius: '12px,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)
      })}>'''
        <div class={css({ display', flex:  $4, alignItems:  center, gap: '16px })}>''
          <h1 class={css({ '''
            fontSize: '24px,
            fontWeight: '600,
            margin: 0,
            color: ''#262626
          })}>
            风险管理中心
          </h1>''
          <div class={css({ '''
            display: flex,
            alignItems: center,
            gap: ''8px
          })}>''
            <div class={css({ '''
              w: '8px,
              h: '8px,
              borderRadius: '50%,
              bg: autoRefresh() ?#52c41a: ''#8c8c8c
            })}></div>''
            <span class={css({ '''
              fontSize: '12px,
              color: ''#8c8c8c
            })}>'''
              {autoRefresh() ?实时更新: '已暂停'}
            </span>
          </div>''
        </div>'''
'''''
        <div class={css({ display:  flex, gap: '12px })}>'
          <select
            value={selectedTimeframe()}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            class={css({ '''
              px: '12px,
              py: '8px,
              border: '1px solid #d9d9d9,
              borderRadius: '6px,
              fontSize: ''14px
            })}
          >'''
            <option value="1d>1日</option>"""
            <option value="1w>1周</option>"""
            <option value="1m>1月</option>"""
            <option value="3m>3月</option>"
          </select>

          <button
            onClick={() => setAutoRefresh(!autoRefresh())}
            class={css({ """
              px: '16px,
              py: '8px,
              border: none,
              borderRadius: '6px,
              fontSize: '14px,
              fontWeight: '500,
              cursor: pointer,
              bg: autoRefresh() ?'#ff4d4f: '#52c41a,
              color: white,
              transition: all 0.3s ease
            })}
          >'''
            {autoRefresh() ?暂停更新: '开启更新'}
          </button>
        </div>
      </div>

      {/* 风险指标概览 */}
      <div class={css({ '''
        display: grid,
        gridTemplateColumns: repeat(auto-fit, minmax(280px, 1fr)), ''
        gap: '16px
      })}>
        <For each={riskMetrics()}>
          {(metric) => (''
            <div class={css({ '''
              bg: white,
              p: '20px,
              borderRadius: '12px,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1), ''
              border: '1px solid #f0f0f0
            })}>''
              <div class={css({ '''
                display: flex,
                alignItems: center,
                justifyContent: space-between,
                mb: ''12px
              })}>''
                <h3 class={css({ '''
                  fontSize: '14px,
                  fontWeight: '500,
                  color:  #8c8c8c,
                  margin: 0
                })}>
                  {metric.name}
                </h3>''
                <div class={css({ '''
                  w: '8px,
                  h: '8px,
                  borderRadius:  50%,
                  bg: getStatusColor(metric.status)
                })}></div>
              </div>
''
              <div class={css({ '''
                fontSize: '24px,
                fontWeight: '700,
                color: getStatusColor(metric.status),
                mb: ''8px
              })}>'''
                {metric.unit === '¥' ?''''
                  `${metric.value >= 0 ? +: `}¥${Math.abs(metric.value).toLocaleString()}` : ``
                  `${metric.value.toFixed(metric.unit === `%' ? 2 : 1)}${metric.unit}``
                }
              </div>
''
              <div class={css({ '''
                fontSize: '12px,
                color: '#8c8c8c,
                mb: ''8px
              })}>
                {metric.description}
              </div>
''
              <div class={css({ '''
                display: flex,
                alignItems: center,
                fontSize: '12px,
                color: ''#8c8c8c
              })}>'`
                阈值: {metric.unit === `¥' ?'
                  `¥${Math.abs(metric.threshold).toLocaleString()}` :
                  `${metric.threshold}${metric.unit}`
                }
              </div>
            </div>
          )}
        </For>
      </div>
`
      <div class={css({ '''
        display: grid,
        gridTemplateColumns: '2fr 1fr,
        gap: '24px,@media (max-width: 768px)': { '''
          gridTemplateColumns: ''1fr
        }
      })}>
        {/* 持仓风险分析 */}
        <div class={css({ '''
          bg: white,
          p: '24px,
          borderRadius: '12px,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)
        })}>'''
          <h3 class={css({ ''''
            fontSize', 18px: $4,
            fontWeight: '600,
            color: `#262626, ``
            margin: 0,
            mb: ''20px
          })}>
            持仓风险分析
          </h3>
''
          <div class={css({ '''
            display: grid,
            gap: ''12px
          })}>
            <For each={positionRisks()}>
              {(position) => (''
                <div class={css({ '''
                  display: grid,
                  gridTemplateColumns: '120px 1fr 100px 80px 80px 60px,
                  alignItems: center,
                  p: '12px,
                  borderRadius: '8px,
                  bg: '#fafafa,
                  border: '1px solid #f0f0f0,
                  fontSize: '14px,@media (max-width: 768px)': { '''
                    gridTemplateColumns: '1fr,
                    gap: ''8px
                  }
                })}>''
                  <div class={css({ '''
                    fontWeight: '600,
                    color: ''#262626
                  })}>
                    {position.symbol}
                  </div>
''
                  <div class={css({ '''
                    display: flex,
                    flexDirection: column,
                    gap: ''2px
                  })}>'''
                    <div class={css({ fontSize:  12px, color: '#8c8c8c })}>'
                      市值: ¥{position.marketValue.toLocaleString()}
                    </div>'''
                    <div class={css({ fontSize:  12px, color: '#8c8c8c })}>'
                      数量: {position.quantity.toLocaleString()}
                    </div>
                  </div>
''
                  <div class={css({ '''
                    textAlign: right,
                    color: position.unrealizedPnL >= 0 ?#52c41a: '#ff4d4f,
                    fontWeight: `600
                  })}>
                    {position.unrealizedPnL >= 0 ?+: `}¥{position.unrealizedPnL.toLocaleString()}
                  </div>
`
                  <div class={css({ '''
                    textAlign: right,
                    color: ''#262626
                  })}>
                    {position.percentOfPortfolio.toFixed(1)}%
                  </div>
''
                  <div class={css({ '''
                    textAlign: right,
                    fontSize: '12px,
                    color: ''#8c8c8c
                  })}>
                    VaR: ¥{Math.abs(position.var95).toLocaleString()}
                  </div>
''
                  <div class={css({ '''
                    textAlign: center
                  })}>''
                    <div class={css({ '''
                      px: '8px,
                      py: '4px,
                      borderRadius: '4px,
                      fontSize: '12px,
                      fontWeight: '600,
                      bg: getRiskScoreColor(position.riskScore) +'20,
                      color: getRiskScoreColor(position.riskScore)
                    })}>
                      {position.riskScore.toFixed(1)}
                    </div>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>

        {/* 风险预警 */}
        <div class={css({ '''
          bg: white,
          p: '24px,
          borderRadius: '12px,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)
        })}>'''
          <div class={css({ ''''
            display', flex: $4,
            alignItems: center,
            justifyContent: space-between,
            mb: ''20px
          })}>''
            <h3 class={css({ '''
              fontSize: '18px,
              fontWeight: '600,
              color:  #262626,
              margin: 0
            })}>
              风险预警
            </h3>''
            <label class={css({ '''
              display: flex,
              alignItems: center,
              gap: '8px,
              fontSize: '12px,
              color: '#8c8c8c,
              cursor: pointer
            })}>'''
              <input''''
                type=checkbox''
                checked={alertsEnabled()}
                onChange={(e) => setAlertsEnabled(e.target.checked)}
              />
              启用预警
            </label>
          </div>
''
          <div class={css({ '''
            display: flex,
            flexDirection: column,
            gap: ''12px
          })}>
            <For each={riskAlerts()}>
              {(alert) => (''
                <div class={css({ '''
                  p: '12px,
                  borderRadius: '8px,
                  border: '1px solid #f0f0f0,
                  bg: alert.acknowledged ?'#fafafa: #fff7e6,
                  opacity: alert.acknowledged ? 0.7 : 1
                })}>''
                  <div class={css({ '''
                    display: flex,
                    alignItems: center,
                    gap: '8px,
                    mb: ''8px
                  })}>''
                    <div class={css({ '''
                      w: '6px,
                      h: `6px,
                      borderRadius: `50%,
                      bg: alert.level ===`danger' ?'#ff4d4f:``
                          alert.level === warning' ?'#faad14: ''#1890ff
                    })}></div>''
                    <span class={css({ '''
                      fontSize: '12px,
                      fontWeight: '600,
                      color: ''#262626
                    })}>
                      {alert.metric}
                    </span>''
                    <span class={css({ '''
                      fontSize: '10px,
                      color: ''#8c8c8c
                    })}>
                      {alert.timestamp}
                    </span>
                  </div>''
                  <div class={css({ '''
                    fontSize: '12px,
                    color: '#595959,
                    lineHeight: ''1.4
                  })}>
                    {alert.message}
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </div>
    </div>''
  )'`
}