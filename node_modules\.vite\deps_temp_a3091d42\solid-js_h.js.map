{"version": 3, "sources": ["../../solid-js/h/dist/h.js"], "sourcesContent": ["import { spread, assign, insert, createComponent, dynamicProperty, SVGElements } from 'solid-js/web';\n\nconst $ELEMENT = Symbol(\"hyper-element\");\nfunction createHyperScript(r) {\n  function h() {\n    let args = [].slice.call(arguments),\n      e,\n      classes = [],\n      multiExpression = false;\n    while (Array.isArray(args[0])) args = args[0];\n    if (args[0][$ELEMENT]) args.unshift(h.Fragment);\n    typeof args[0] === \"string\" && detectMultiExpression(args);\n    const ret = () => {\n      while (args.length) item(args.shift());\n      if (e instanceof Element && classes.length) e.classList.add(...classes);\n      return e;\n    };\n    ret[$ELEMENT] = true;\n    return ret;\n    function item(l) {\n      const type = typeof l;\n      if (l == null) ;else if (\"string\" === type) {\n        if (!e) parseClass(l);else e.appendChild(document.createTextNode(l));\n      } else if (\"number\" === type || \"boolean\" === type || \"bigint\" === type || \"symbol\" === type || l instanceof Date || l instanceof RegExp) {\n        e.appendChild(document.createTextNode(l.toString()));\n      } else if (Array.isArray(l)) {\n        for (let i = 0; i < l.length; i++) item(l[i]);\n      } else if (l instanceof Element) {\n        r.insert(e, l, multiExpression ? null : undefined);\n      } else if (\"object\" === type) {\n        let dynamic = false;\n        const d = Object.getOwnPropertyDescriptors(l);\n        for (const k in d) {\n          if (k === \"class\" && classes.length !== 0) {\n            const fixedClasses = classes.join(\" \"),\n              value = typeof d[\"class\"].value === \"function\" ? () => fixedClasses + \" \" + d[\"class\"].value() : fixedClasses + \" \" + l[\"class\"];\n            Object.defineProperty(l, \"class\", {\n              ...d[k],\n              value\n            });\n            classes = [];\n          }\n          if (k !== \"ref\" && k.slice(0, 2) !== \"on\" && typeof d[k].value === \"function\") {\n            r.dynamicProperty(l, k);\n            dynamic = true;\n          } else if (d[k].get) dynamic = true;\n        }\n        dynamic ? r.spread(e, l, e instanceof SVGElement, !!args.length) : r.assign(e, l, e instanceof SVGElement, !!args.length);\n      } else if (\"function\" === type) {\n        if (!e) {\n          let props,\n            next = args[0];\n          if (next == null || typeof next === \"object\" && !Array.isArray(next) && !(next instanceof Element)) props = args.shift();\n          props || (props = {});\n          if (args.length) {\n            props.children = args.length > 1 ? args : args[0];\n          }\n          const d = Object.getOwnPropertyDescriptors(props);\n          for (const k in d) {\n            if (Array.isArray(d[k].value)) {\n              const list = d[k].value;\n              props[k] = () => {\n                for (let i = 0; i < list.length; i++) {\n                  while (list[i][$ELEMENT]) list[i] = list[i]();\n                }\n                return list;\n              };\n              r.dynamicProperty(props, k);\n            } else if (typeof d[k].value === \"function\" && !d[k].value.length) r.dynamicProperty(props, k);\n          }\n          e = r.createComponent(l, props);\n          args = [];\n        } else {\n          while (l[$ELEMENT]) l = l();\n          r.insert(e, l, multiExpression ? null : undefined);\n        }\n      }\n    }\n    function parseClass(string) {\n      const m = string.split(/([\\.#]?[^\\s#.]+)/);\n      if (/^\\.|#/.test(m[1])) e = document.createElement(\"div\");\n      for (let i = 0; i < m.length; i++) {\n        const v = m[i],\n          s = v.substring(1, v.length);\n        if (!v) continue;\n        if (!e) e = r.SVGElements.has(v) ? document.createElementNS(\"http://www.w3.org/2000/svg\", v) : document.createElement(v);else if (v[0] === \".\") classes.push(s);else if (v[0] === \"#\") e.setAttribute(\"id\", s);\n      }\n    }\n    function detectMultiExpression(list) {\n      for (let i = 1; i < list.length; i++) {\n        if (typeof list[i] === \"function\") {\n          multiExpression = true;\n          return;\n        } else if (Array.isArray(list[i])) {\n          detectMultiExpression(list[i]);\n        }\n      }\n    }\n  }\n  h.Fragment = props => props.children;\n  return h;\n}\n\nconst h = createHyperScript({\n  spread,\n  assign,\n  insert,\n  createComponent,\n  dynamicProperty,\n  SVGElements\n});\n\nexport { h as default };\n"], "mappings": ";;;;;;;;;;;;;AAEA,IAAM,WAAW,OAAO,eAAe;AACvC,SAAS,kBAAkB,GAAG;AAC5B,WAASA,KAAI;AACX,QAAI,OAAO,CAAC,EAAE,MAAM,KAAK,SAAS,GAChC,GACA,UAAU,CAAC,GACX,kBAAkB;AACpB,WAAO,MAAM,QAAQ,KAAK,CAAC,CAAC,EAAG,QAAO,KAAK,CAAC;AAC5C,QAAI,KAAK,CAAC,EAAE,QAAQ,EAAG,MAAK,QAAQA,GAAE,QAAQ;AAC9C,WAAO,KAAK,CAAC,MAAM,YAAY,sBAAsB,IAAI;AACzD,UAAM,MAAM,MAAM;AAChB,aAAO,KAAK,OAAQ,MAAK,KAAK,MAAM,CAAC;AACrC,UAAI,aAAa,WAAW,QAAQ,OAAQ,GAAE,UAAU,IAAI,GAAG,OAAO;AACtE,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,IAAI;AAChB,WAAO;AACP,aAAS,KAAK,GAAG;AACf,YAAM,OAAO,OAAO;AACpB,UAAI,KAAK,KAAM;AAAA,eAAU,aAAa,MAAM;AAC1C,YAAI,CAAC,EAAG,YAAW,CAAC;AAAA,YAAO,GAAE,YAAY,SAAS,eAAe,CAAC,CAAC;AAAA,MACrE,WAAW,aAAa,QAAQ,cAAc,QAAQ,aAAa,QAAQ,aAAa,QAAQ,aAAa,QAAQ,aAAa,QAAQ;AACxI,UAAE,YAAY,SAAS,eAAe,EAAE,SAAS,CAAC,CAAC;AAAA,MACrD,WAAW,MAAM,QAAQ,CAAC,GAAG;AAC3B,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,MAAK,EAAE,CAAC,CAAC;AAAA,MAC9C,WAAW,aAAa,SAAS;AAC/B,UAAE,OAAO,GAAG,GAAG,kBAAkB,OAAO,MAAS;AAAA,MACnD,WAAW,aAAa,MAAM;AAC5B,YAAI,UAAU;AACd,cAAM,IAAI,OAAO,0BAA0B,CAAC;AAC5C,mBAAW,KAAK,GAAG;AACjB,cAAI,MAAM,WAAW,QAAQ,WAAW,GAAG;AACzC,kBAAM,eAAe,QAAQ,KAAK,GAAG,GACnC,QAAQ,OAAO,EAAE,OAAO,EAAE,UAAU,aAAa,MAAM,eAAe,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,eAAe,MAAM,EAAE,OAAO;AACjI,mBAAO,eAAe,GAAG,SAAS;AAAA,cAChC,GAAG,EAAE,CAAC;AAAA,cACN;AAAA,YACF,CAAC;AACD,sBAAU,CAAC;AAAA,UACb;AACA,cAAI,MAAM,SAAS,EAAE,MAAM,GAAG,CAAC,MAAM,QAAQ,OAAO,EAAE,CAAC,EAAE,UAAU,YAAY;AAC7E,cAAE,gBAAgB,GAAG,CAAC;AACtB,sBAAU;AAAA,UACZ,WAAW,EAAE,CAAC,EAAE,IAAK,WAAU;AAAA,QACjC;AACA,kBAAU,EAAE,OAAO,GAAG,GAAG,aAAa,YAAY,CAAC,CAAC,KAAK,MAAM,IAAI,EAAE,OAAO,GAAG,GAAG,aAAa,YAAY,CAAC,CAAC,KAAK,MAAM;AAAA,MAC1H,WAAW,eAAe,MAAM;AAC9B,YAAI,CAAC,GAAG;AACN,cAAI,OACF,OAAO,KAAK,CAAC;AACf,cAAI,QAAQ,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI,KAAK,EAAE,gBAAgB,SAAU,SAAQ,KAAK,MAAM;AACvH,oBAAU,QAAQ,CAAC;AACnB,cAAI,KAAK,QAAQ;AACf,kBAAM,WAAW,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC;AAAA,UAClD;AACA,gBAAM,IAAI,OAAO,0BAA0B,KAAK;AAChD,qBAAW,KAAK,GAAG;AACjB,gBAAI,MAAM,QAAQ,EAAE,CAAC,EAAE,KAAK,GAAG;AAC7B,oBAAM,OAAO,EAAE,CAAC,EAAE;AAClB,oBAAM,CAAC,IAAI,MAAM;AACf,yBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,yBAAO,KAAK,CAAC,EAAE,QAAQ,EAAG,MAAK,CAAC,IAAI,KAAK,CAAC,EAAE;AAAA,gBAC9C;AACA,uBAAO;AAAA,cACT;AACA,gBAAE,gBAAgB,OAAO,CAAC;AAAA,YAC5B,WAAW,OAAO,EAAE,CAAC,EAAE,UAAU,cAAc,CAAC,EAAE,CAAC,EAAE,MAAM,OAAQ,GAAE,gBAAgB,OAAO,CAAC;AAAA,UAC/F;AACA,cAAI,EAAE,gBAAgB,GAAG,KAAK;AAC9B,iBAAO,CAAC;AAAA,QACV,OAAO;AACL,iBAAO,EAAE,QAAQ,EAAG,KAAI,EAAE;AAC1B,YAAE,OAAO,GAAG,GAAG,kBAAkB,OAAO,MAAS;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AACA,aAAS,WAAW,QAAQ;AAC1B,YAAM,IAAI,OAAO,MAAM,kBAAkB;AACzC,UAAI,QAAQ,KAAK,EAAE,CAAC,CAAC,EAAG,KAAI,SAAS,cAAc,KAAK;AACxD,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAM,IAAI,EAAE,CAAC,GACX,IAAI,EAAE,UAAU,GAAG,EAAE,MAAM;AAC7B,YAAI,CAAC,EAAG;AACR,YAAI,CAAC,EAAG,KAAI,EAAE,YAAY,IAAI,CAAC,IAAI,SAAS,gBAAgB,8BAA8B,CAAC,IAAI,SAAS,cAAc,CAAC;AAAA,iBAAW,EAAE,CAAC,MAAM,IAAK,SAAQ,KAAK,CAAC;AAAA,iBAAW,EAAE,CAAC,MAAM,IAAK,GAAE,aAAa,MAAM,CAAC;AAAA,MAC/M;AAAA,IACF;AACA,aAAS,sBAAsB,MAAM;AACnC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,OAAO,KAAK,CAAC,MAAM,YAAY;AACjC,4BAAkB;AAClB;AAAA,QACF,WAAW,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AACjC,gCAAsB,KAAK,CAAC,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAA,GAAE,WAAW,WAAS,MAAM;AAC5B,SAAOA;AACT;AAEA,IAAM,IAAI,kBAAkB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;", "names": ["h"]}