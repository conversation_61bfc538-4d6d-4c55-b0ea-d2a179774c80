import{c as T,a as S,b as M,d as U,o as Q,t as _,i as k,R as Z,e as z,f as C,g as c,s as J,r as rr}from"./vendor-solid-CTb_3i8P.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const t of document.querySelectorAll('link[rel="modulepreload"]'))i(t);new MutationObserver(t=>{for(const o of t)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&i(a)}).observe(document,{childList:!0,subtree:!0});function n(t){const o={};return t.integrity&&(o.integrity=t.integrity),t.referrerPolicy&&(o.referrerPolicy=t.referrerPolicy),t.crossOrigin==="use-credentials"?o.credentials="include":t.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(t){if(t.ep)return;t.ep=!0;const o=n(t);fetch(t.href,o)}})();const er=M(),tr=r=>{const[e,n]=T(r.defaultTheme||"light"),t={theme:e,setTheme:n,toggleTheme:()=>{n(e()==="light"?"dark":"light")}};return S(er.Provider,{value:t,get children(){return r.children}})},or={"zh-CN":{dashboard:"仪表板",trading:"交易中心",strategy:"策略管理",welcome:"欢迎使用量化交易平台",loading:"加载中...",error:"发生错误",success:"操作成功"},"en-US":{dashboard:"Dashboard",trading:"Trading",strategy:"Strategy",welcome:"Welcome to Quant Trading Platform",loading:"Loading...",error:"Error occurred",success:"Operation successful"}},nr=M(),ir=r=>{const[e,n]=T(r.defaultLanguage||"zh-CN"),t={language:e,setLanguage:n,t:o=>{const a=e();return or[a]?.[o]||o}};return S(nr.Provider,{value:t,get children(){return r.children}})};function I(r){return typeof r=="object"&&r!=null&&!Array.isArray(r)}function ar(r){return Object.fromEntries(Object.entries(r??{}).filter(([e,n])=>n!==void 0))}var lr=r=>r==="base";function dr(r){return r.slice().filter(e=>!lr(e))}function L(r){return String.fromCharCode(r+(r>25?39:97))}function sr(r){let e="",n;for(n=Math.abs(r);n>52;n=n/52|0)e=L(n%52)+e;return L(n%52)+e}function cr(r,e){let n=e.length;for(;n;)r=r*33^e.charCodeAt(--n);return r}function gr(r){return sr(cr(5381,r)>>>0)}var D=/\s*!(important)?/i;function br(r){return typeof r=="string"?D.test(r):!1}function ur(r){return typeof r=="string"?r.replace(D,"").trim():r}function X(r){return typeof r=="string"?r.replaceAll(" ","_"):r}var E=r=>{const e=new Map;return(...i)=>{const t=JSON.stringify(i);if(e.has(t))return e.get(t);const o=r(...i);return e.set(t,o),o}};function $(...r){return r.filter(Boolean).reduce((n,i)=>(Object.keys(i).forEach(t=>{const o=n[t],a=i[t];I(o)&&I(a)?n[t]=$(o,a):n[t]=a}),n),{})}var mr=r=>r!=null;function F(r,e,n={}){const{stop:i,getKey:t}=n;function o(a,b=[]){if(I(a)||Array.isArray(a)){const u={};for(const[l,m]of Object.entries(a)){const p=t?.(l,m)??l,d=[...b,p];if(i?.(a,d))return e(a,b);const g=o(m,d);mr(g)&&(u[p]=g)}return u}return e(a,b)}return o(r)}function pr(r,e){return r.reduce((n,i,t)=>{const o=e[t];return i!=null&&(n[o]=i),n},{})}function Y(r,e,n=!0){const{utility:i,conditions:t}=e,{hasShorthand:o,resolveShorthand:a}=i;return F(r,b=>Array.isArray(b)?pr(b,t.breakpoints.keys):b,{stop:b=>Array.isArray(b),getKey:n?b=>o?a(b):b:void 0})}var hr={shift:r=>r,finalize:r=>r,breakpoints:{keys:[]}},fr=r=>typeof r=="string"?r.replaceAll(/[\n\s]+/g," "):r;function xr(r){const{utility:e,hash:n,conditions:i=hr}=r,t=a=>[e.prefix,a].filter(Boolean).join("-"),o=(a,b)=>{let u;if(n){const l=[...i.finalize(a),b];u=t(e.toHash(l,gr))}else u=[...i.finalize(a),t(b)].join(":");return u};return E(({base:a,...b}={})=>{const u=Object.assign(b,a),l=Y(u,r),m=new Set;return F(l,(p,d)=>{const g=br(p);if(p==null)return;const[h,...y]=i.shift(d),f=dr(y),v=e.transform(h,ur(fr(p)));let x=o(f,v.className);g&&(x=`${x}!`),m.add(x)}),Array.from(m).join(" ")})}function vr(...r){return r.flat().filter(e=>I(e)&&Object.keys(ar(e)).length>0)}function yr(r){function e(t){const o=vr(...t);return o.length===1?o:o.map(a=>Y(a,r))}function n(...t){return $(...e(t))}function i(...t){return Object.assign({},...e(t))}return{mergeCss:E(n),assignCss:i}}var Sr=/([A-Z])/g,kr=/^ms-/,_r=E(r=>r.startsWith("--")?r:r.replace(Sr,"-$1").replace(kr,"-ms-").toLowerCase()),Cr="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Cr.split(",").join("|")}`;const wr="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",H=new Set(wr.split(","));function A(r){return H.has(r)||/^@|&|&$/.test(r)}const Br=/^_/,Rr=/&|@/;function Tr(r){return r.map(e=>H.has(e)?e.replace(Br,""):Rr.test(e)?`[${X(e.trim())}]`:e)}function Ir(r){return r.sort((e,n)=>{const i=A(e),t=A(n);return i&&!t?1:!i&&t?-1:0})}const zr="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",q=new Map,G=new Map;zr.split(",").forEach(r=>{const[e,n]=r.split(":"),[i,...t]=n.split("/");q.set(e,i),t.length&&t.forEach(o=>{G.set(o==="1"?i:o,e)})});const N=r=>G.get(r)||r,V={conditions:{shift:Ir,finalize:Tr,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(r,e)=>{const n=N(r);return{className:`${q.get(n)||_r(n)}_${X(e)}`}},hasShorthand:!0,toHash:(r,e)=>e(r.join(":")),resolveShorthand:N}},Er=xr(V),s=(...r)=>Er(K(...r));s.raw=(...r)=>K(...r);const{mergeCss:K}=yr(V);var Pr=_("<div><h2>📊 量化交易仪表板</h2><div><div><h3>账户总资产</h3><div>¥1,234,567.89</div></div><div><h3>今日盈亏</h3><div>-¥2,345.67</div></div><div><h3>持仓数量</h3><div>8"),Or=_("<div><h2>💼 交易中心</h2><div><div><h3>📋 委托订单</h3><p>暂无委托订单</p></div><div><h3>📊 持仓明细</h3><p>暂无持仓"),Wr=_("<div><h2>🤖 策略管理</h2><div><h3>📈 策略列表</h3><p>暂无策略"),Lr=_("<nav><div><div>🚀 </div><div><div></div><div><button></button><button>"),Ar=_("<a><span>"),Nr=_("<main>"),jr=_("<div>");const Mr=()=>(()=>{var r=Pr(),e=r.firstChild,n=e.nextSibling,i=n.firstChild,t=i.firstChild,o=t.nextSibling,a=i.nextSibling,b=a.firstChild,u=b.nextSibling,l=a.nextSibling,m=l.firstChild,p=m.nextSibling;return C(d=>{var g=s({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),h=s({fontSize:"24px",fontWeight:"bold",color:"blue.600",marginBottom:"24px"}),y=s({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"16px"}),f=s({padding:"16px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px"}),v=s({margin:"0 0 8px 0",color:"gray.700"}),x=s({fontSize:"24px",fontWeight:"bold",color:"green.600"}),w=s({padding:"16px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px"}),B=s({margin:"0 0 8px 0",color:"gray.700"}),R=s({fontSize:"24px",fontWeight:"bold",color:"red.600"}),P=s({padding:"16px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px"}),O=s({margin:"0 0 8px 0",color:"gray.700"}),W=s({fontSize:"24px",fontWeight:"bold",color:"blue.600"});return g!==d.e&&c(r,d.e=g),h!==d.t&&c(e,d.t=h),y!==d.a&&c(n,d.a=y),f!==d.o&&c(i,d.o=f),v!==d.i&&c(t,d.i=v),x!==d.n&&c(o,d.n=x),w!==d.s&&c(a,d.s=w),B!==d.h&&c(b,d.h=B),R!==d.r&&c(u,d.r=R),P!==d.d&&c(l,d.d=P),O!==d.l&&c(m,d.l=O),W!==d.u&&c(p,d.u=W),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0}),r})(),Dr=()=>(()=>{var r=Or(),e=r.firstChild,n=e.nextSibling,i=n.firstChild,t=i.firstChild,o=t.nextSibling,a=i.nextSibling,b=a.firstChild,u=b.nextSibling;return C(l=>{var m=s({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),p=s({fontSize:"24px",fontWeight:"bold",color:"blue.600",marginBottom:"24px"}),d=s({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"}),g=s({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px"}),h=s({margin:"0 0 16px 0",color:"gray.700"}),y=s({color:"gray.500",margin:0}),f=s({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px"}),v=s({margin:"0 0 16px 0",color:"gray.700"}),x=s({color:"gray.500",margin:0});return m!==l.e&&c(r,l.e=m),p!==l.t&&c(e,l.t=p),d!==l.a&&c(n,l.a=d),g!==l.o&&c(i,l.o=g),h!==l.i&&c(t,l.i=h),y!==l.n&&c(o,l.n=y),f!==l.s&&c(a,l.s=f),v!==l.h&&c(b,l.h=v),x!==l.r&&c(u,l.r=x),l},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0}),r})(),Xr=()=>(()=>{var r=Wr(),e=r.firstChild,n=e.nextSibling,i=n.firstChild,t=i.nextSibling;return C(o=>{var a=s({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),b=s({fontSize:"24px",fontWeight:"bold",color:"blue.600",marginBottom:"24px"}),u=s({padding:"20px",background:"white",border:"1px solid #e2e8f0",borderRadius:"8px"}),l=s({margin:"0 0 16px 0",color:"gray.700"}),m=s({color:"gray.500",margin:0});return a!==o.e&&c(r,o.e=a),b!==o.t&&c(e,o.t=b),u!==o.a&&c(n,o.a=u),l!==o.o&&c(i,o.o=l),m!==o.i&&c(t,o.i=m),o},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),r})(),$r=()=>{const[r]=T(window.location.pathname),[e,n]=T("light"),[i,t]=T("zh-CN");return(()=>{var o=Lr(),a=o.firstChild,b=a.firstChild;b.firstChild;var u=b.nextSibling,l=u.firstChild,m=l.nextSibling,p=m.firstChild,d=p.nextSibling;return k(b,()=>i()==="zh-CN"?"量化交易平台":"Quant Trading Platform",null),k(l,()=>[{path:"/",label:i()==="zh-CN"?"仪表板":"Dashboard",icon:"📊"},{path:"/trading",label:i()==="zh-CN"?"交易中心":"Trading",icon:"💼"},{path:"/strategy",label:i()==="zh-CN"?"策略管理":"Strategy",icon:"🤖"}].map(g=>(()=>{var h=Ar(),y=h.firstChild;return k(y,()=>g.icon),k(h,()=>g.label,null),C(f=>{var v=g.path,x=s({color:r()===g.path?"blue.400":"gray.300",textDecoration:"none",padding:"0.5rem 1rem",borderRadius:"0.375rem",display:"flex",alignItems:"center",gap:"0.5rem",_hover:{color:r()===g.path?"blue.400":"gray.100"}});return v!==f.e&&J(h,"href",f.e=v),x!==f.t&&c(h,f.t=x),f},{e:void 0,t:void 0}),h})())),p.$$click=()=>t(i()==="zh-CN"?"en-US":"zh-CN"),k(p,()=>i()==="zh-CN"?"EN":"中"),d.$$click=()=>n(e()==="light"?"dark":"light"),k(d,()=>e()==="dark"?"☀️":"🌙"),C(g=>{var h=s({background:e()==="dark"?"gray.900":"gray.800",padding:"1rem 0",borderBottom:"1px solid",borderColor:e()==="dark"?"gray.700":"gray.600"}),y=s({maxWidth:"1200px",margin:"0 auto",padding:"0 20px",display:"flex",justifyContent:"space-between",alignItems:"center"}),f=s({color:"white",fontSize:"20px",fontWeight:"bold"}),v=s({display:"flex",alignItems:"center",gap:"1rem"}),x=s({display:"flex",gap:"2rem"}),w=s({display:"flex",alignItems:"center",gap:"0.5rem"}),B=s({background:"transparent",border:"1px solid gray.600",color:"gray.300",padding:"0.5rem",borderRadius:"0.375rem"}),R=s({background:"transparent",border:"1px solid gray.600",color:"gray.300",padding:"0.5rem",borderRadius:"0.375rem"});return h!==g.e&&c(o,g.e=h),y!==g.t&&c(a,g.t=y),f!==g.a&&c(b,g.a=f),v!==g.o&&c(u,g.o=v),x!==g.i&&c(l,g.i=x),w!==g.n&&c(m,g.n=w),B!==g.s&&c(p,g.s=B),R!==g.h&&c(d,g.h=R),g},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0}),o})()},Fr=()=>(Q(()=>{console.log("🚀 量化交易平台已启动")}),S(ir,{defaultLanguage:"zh-CN",get children(){return S(tr,{get children(){var r=jr();return k(r,S(Z,{get children(){return[S($r,{}),(()=>{var e=Nr();return k(e,S(z,{path:"/",component:Mr}),null),k(e,S(z,{path:"/trading",component:Dr}),null),k(e,S(z,{path:"/strategy",component:Xr}),null),e})()]}})),C(()=>c(r,s({minHeight:"100vh",background:"gray.50",color:"gray.900",fontFamily:"Inter, system-ui, sans-serif"}))),r}})}}));U(["click"]);const j=document.getElementById("root");j&&rr(()=>S(Fr,{}),j);
