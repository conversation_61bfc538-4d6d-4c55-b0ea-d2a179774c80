import { createSignal } from 'solid-js';
import { css } from '../styled-system/css';
import Dashboard from './pages/Dashboard';
import ApiTest from './pages/ApiTest';
import Market from './pages/Market';

function App() {
  const [currentPage, setCurrentPage] = createSignal('dashboard');
  const [sidebarCollapsed, setSidebarCollapsed] = createSignal(false);

  const renderPage = () => {
    switch (currentPage()) {
      case 'dashboard':
        return <Dashboard />;
      case 'market':
        return <Market />;
      case 'strategy':
        return <div class={css({ padding: '24px' })}>策略管理页面开发中...</div>;
      case 'backtest':
        return <div class={css({ padding: '24px' })}>回测分析页面开发中...</div>;
      case 'trading':
        return <div class={css({ padding: '24px' })}>实盘交易页面开发中...</div>;
      case 'portfolio':
        return <div class={css({ padding: '24px' })}>投资组合页面开发中...</div>;
      case 'risk':
        return <div class={css({ padding: '24px' })}>风险管理页面开发中...</div>;
      case 'settings':
        return <div class={css({ padding: '24px' })}>系统设置页面开发中...</div>;
      case 'api-test':
        return <ApiTest />;
      default:
        return <Dashboard />;
    }
  };

  const menuItems = [
    {
      id: 'dashboard',
      label: '仪表板',
      icon: '📊',
      children: []
    },
    {
      id: 'market',
      label: '行情分析',
      icon: '📈',
      children: []
    },
    {
      id: 'strategy',
      label: '量化策略',
      icon: '🧠',
      children: [
        { id: 'strategy-list', label: '策略列表' },
        { id: 'strategy-create', label: '创建策略' }
      ]
    },
    {
      id: 'backtest',
      label: '回测分析',
      icon: '📊',
      children: []
    },
    {
      id: 'trading',
      label: '实盘交易',
      icon: '💰',
      children: []
    },
    {
      id: 'portfolio',
      label: '投资组合',
      icon: '📋',
      children: []
    },
    {
      id: 'risk',
      label: '风险管理',
      icon: '⚠️',
      children: []
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: '⚙️',
      children: []
    },
    {
      id: 'api-test',
      label: 'API测试',
      icon: '🔧',
      children: []
    }
  ];

  return (
    <div class={css({
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    })}>
      {/* 左侧边栏 */}
      <aside class={css({
        width: sidebarCollapsed() ? '64px' : '240px',
        backgroundColor: 'white',
        borderRight: '1px solid #e8e8e8',
        transition: 'width 0.3s ease',
        display: 'flex',
        flexDirection: 'column',
        position: 'fixed',
        height: '100vh',
        zIndex: 1000
      })}>
        {/* Logo区域 */}
        <div class={css({
          padding: '16px',
          borderBottom: '1px solid #e8e8e8',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        })}>
          <div class={css({
            width: '32px',
            height: '32px',
            backgroundColor: '#1890ff',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '16px',
            fontWeight: 'bold'
          })}>
            量
          </div>
          {!sidebarCollapsed() && (
            <span class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626'
            })}>
              量化交易平台
            </span>
          )}
        </div>

        {/* 导航菜单 */}
        <nav class={css({
          flex: 1,
          padding: '8px 0',
          overflowY: 'auto'
        })}>
          {menuItems.map((item) => (
            <div>
              <button
                onClick={() => setCurrentPage(item.id)}
                class={css({
                  width: '100%',
                  padding: sidebarCollapsed() ? '12px 20px' : '12px 16px',
                  border: 'none',
                  backgroundColor: currentPage() === item.id ? '#e6f7ff' : 'transparent',
                  color: currentPage() === item.id ? '#1890ff' : '#595959',
                  fontSize: '14px',
                  textAlign: 'left',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  borderLeft: currentPage() === item.id ? '3px solid #1890ff' : '3px solid transparent',
                  _hover: {
                    backgroundColor: '#f5f5f5',
                    color: '#1890ff'
                  }
                })}
              >
                <span class={css({ fontSize: '16px' })}>{item.icon}</span>
                {!sidebarCollapsed() && (
                  <span class={css({ fontWeight: currentPage() === item.id ? '500' : '400' })}>
                    {item.label}
                  </span>
                )}
              </button>
            </div>
          ))}
        </nav>

        {/* 折叠按钮 */}
        <div class={css({
          padding: '16px',
          borderTop: '1px solid #e8e8e8'
        })}>
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed())}
            class={css({
              width: '100%',
              padding: '8px',
              border: '1px solid #d9d9d9',
              backgroundColor: 'white',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px',
              color: '#595959',
              _hover: {
                borderColor: '#1890ff',
                color: '#1890ff'
              }
            })}
          >
            {sidebarCollapsed() ? '→' : '←'}
          </button>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main class={css({
        flex: 1,
        marginLeft: sidebarCollapsed() ? '64px' : '240px',
        transition: 'margin-left 0.3s ease',
        display: 'flex',
        flexDirection: 'column'
      })}>
        {/* 顶部导航栏 */}
        <header class={css({
          backgroundColor: 'white',
          borderBottom: '1px solid #e8e8e8',
          padding: '0 24px',
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          })}>
            <h1 class={css({
              fontSize: '18px',
              fontWeight: '500',
              color: '#262626',
              margin: 0
            })}>
              {menuItems.find(item => item.id === currentPage())?.label || '仪表板'}
            </h1>
          </div>

          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          })}>
            <button class={css({
              padding: '6px 12px',
              border: '1px solid #d9d9d9',
              backgroundColor: 'white',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              _hover: {
                borderColor: '#1890ff',
                color: '#1890ff'
              }
            })}>
              帮助
            </button>
            <button class={css({
              padding: '6px 12px',
              border: '1px solid #d9d9d9',
              backgroundColor: 'white',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              _hover: {
                borderColor: '#1890ff',
                color: '#1890ff'
              }
            })}>
              设置
            </button>
            <div class={css({
              width: '32px',
              height: '32px',
              backgroundColor: '#1890ff',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px',
              fontWeight: '500'
            })}>
              用
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <div class={css({
          flex: 1,
          backgroundColor: '#f5f5f5',
          overflow: 'auto'
        })}>
          {renderPage()}
        </div>
      </main>
    </div>
  );
}

export default App;
