// Temporarily removing router to isolate the issue
// import { Router, Route } from '@solidjs/router';

// Simple test component
function TestPage() {
  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: '#1890ff' }}>🚀 量化交易平台测试成功！</h1>
      <p>如果您看到这个消息，说明应用已经正常工作了！</p>
      <div style={{
        marginTop: '20px',
        padding: '16px',
        background: '#f0f9ff',
        borderRadius: '8px',
        border: '1px solid #0ea5e9'
      }}>
        <h3 style={{ margin: '0 0 8px 0', color: '#0ea5e9' }}>✅ 系统状态</h3>
        <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
          SolidJS 框架正常运行，路由系统已配置
        </p>
      </div>
    </div>
  );
}

export default function App() {
  return <TestPage />;
}
