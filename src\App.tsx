import { Router, Route } from '@solidjs/router';
import { Component, createSignal, onMount } from 'solid-js';
import { userStore, marketStore } from './stores/solidjs-atoms';
import { css } from '../styled-system/css';

// 简单的仪表板组件
const Dashboard: Component = () => {
  return (
    <div class={css({ padding: '24px', maxWidth: '1200px', margin: '0 auto' })}>
      <h2 class={css({ fontSize: '24px', fontWeight: 'bold', color: 'blue.600', marginBottom: '24px' })}>
        📊 量化交易仪表板
      </h2>
      
      <div class={css({ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
        gap: '16px',
        marginBottom: '24px'
      })}>
        <div class={css({
          padding: '16px',
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        })}>
          <h3 class={css({ margin: '0 0 8px 0', color: 'gray.700' })}>账户总资产</h3>
          <div class={css({ fontSize: '24px', fontWeight: 'bold', color: 'green.600' })}>
            ¥1,234,567.89
          </div>
        </div>
        
        <div class={css({
          padding: '16px',
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        })}>
          <h3 class={css({ margin: '0 0 8px 0', color: 'gray.700' })}>今日盈亏</h3>
          <div class={css({ fontSize: '24px', fontWeight: 'bold', color: 'red.600' })}>
            -¥2,345.67
          </div>
        </div>
        
        <div class={css({
          padding: '16px',
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        })}>
          <h3 class={css({ margin: '0 0 8px 0', color: 'gray.700' })}>持仓数量</h3>
          <div class={css({ fontSize: '24px', fontWeight: 'bold', color: 'blue.600' })}>
            8
          </div>
        </div>
      </div>
      
      <div class={css({
        padding: '20px',
        background: 'white',
        border: '1px solid #e2e8f0',
        borderRadius: '8px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      })}>
        <h3 class={css({ margin: '0 0 16px 0', color: 'gray.700' })}>📈 实时行情</h3>
        <div class={css({ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '12px'
        })}>
          <div class={css({ 
            padding: '12px', 
            background: 'blue.50', 
            borderRadius: '6px',
            border: '1px solid',
            borderColor: 'blue.200'
          })}>
            <div class={css({ fontWeight: 'bold', color: 'blue.800' })}>AAPL</div>
            <div class={css({ fontSize: '18px', color: 'green.600' })}>$173.25</div>
            <div class={css({ fontSize: '12px', color: 'green.600' })}>+1.25 (+0.72%)</div>
          </div>
          
          <div class={css({ 
            padding: '12px', 
            background: 'blue.50', 
            borderRadius: '6px',
            border: '1px solid',
            borderColor: 'blue.200'
          })}>
            <div class={css({ fontWeight: 'bold', color: 'blue.800' })}>TSLA</div>
            <div class={css({ fontSize: '18px', color: 'red.600' })}>$245.67</div>
            <div class={css({ fontSize: '12px', color: 'red.600' })}>-3.45 (-1.38%)</div>
          </div>
          
          <div class={css({ 
            padding: '12px', 
            background: 'blue.50', 
            borderRadius: '6px',
            border: '1px solid',
            borderColor: 'blue.200'
          })}>
            <div class={css({ fontWeight: 'bold', color: 'blue.800' })}>BTC-USD</div>
            <div class={css({ fontSize: '18px', color: 'green.600' })}>$43,250.00</div>
            <div class={css({ fontSize: '12px', color: 'green.600' })}>+1,250 (+2.98%)</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 交易中心组件
const Trading: Component = () => {
  return (
    <div class={css({ padding: '24px', maxWidth: '1200px', margin: '0 auto' })}>
      <h2 class={css({ fontSize: '24px', fontWeight: 'bold', color: 'blue.600', marginBottom: '24px' })}>
        💼 交易中心
      </h2>
      
      <div class={css({ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px'
      })}>
        <div class={css({
          padding: '20px',
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        })}>
          <h3 class={css({ margin: '0 0 16px 0', color: 'gray.700' })}>📋 委托订单</h3>
          <p class={css({ color: 'gray.500', margin: 0 })}>暂无委托订单</p>
        </div>
        
        <div class={css({
          padding: '20px',
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        })}>
          <h3 class={css({ margin: '0 0 16px 0', color: 'gray.700' })}>📊 持仓明细</h3>
          <p class={css({ color: 'gray.500', margin: 0 })}>暂无持仓</p>
        </div>
      </div>
    </div>
  );
};

// 策略管理组件
const Strategy: Component = () => {
  return (
    <div class={css({ padding: '24px', maxWidth: '1200px', margin: '0 auto' })}>
      <h2 class={css({ fontSize: '24px', fontWeight: 'bold', color: 'blue.600', marginBottom: '24px' })}>
        🤖 策略管理
      </h2>
      
      <div class={css({
        padding: '20px',
        background: 'white',
        border: '1px solid #e2e8f0',
        borderRadius: '8px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      })}>
        <h3 class={css({ margin: '0 0 16px 0', color: 'gray.700' })}>📈 策略列表</h3>
        <p class={css({ color: 'gray.500', margin: 0 })}>暂无策略</p>
      </div>
    </div>
  );
};

// 导航组件
const Navigation: Component = () => {
  const [currentPath, setCurrentPath] = createSignal(window.location.pathname);
  const [theme, setTheme] = createSignal<'light' | 'dark'>('light');
  const [language, setLanguage] = createSignal<'zh-CN' | 'en-US'>('zh-CN');

  const toggleTheme = () => {
    setTheme(theme() === 'light' ? 'dark' : 'light');
  };

  const navItems = [
    { path: '/', label: language() === 'zh-CN' ? '仪表板' : 'Dashboard', icon: '📊' },
    { path: '/trading', label: language() === 'zh-CN' ? '交易中心' : 'Trading', icon: '💼' },
    { path: '/strategy', label: language() === 'zh-CN' ? '策略管理' : 'Strategy', icon: '🤖' },
  ];

  return (
    <nav class={css({
      background: theme() === 'dark' ? 'gray.900' : 'gray.800',
      padding: '1rem 0',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      borderBottom: '1px solid',
      borderColor: theme() === 'dark' ? 'gray.700' : 'gray.600'
    })}>
      <div class={css({
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      })}>
        <div class={css({
          color: 'white',
          fontSize: '20px',
          fontWeight: 'bold'
        })}>
          🚀 {language() === 'zh-CN' ? '量化交易平台' : 'Quant Trading Platform'}
        </div>

        <div class={css({ display: 'flex', alignItems: 'center', gap: '1rem' })}>
          {/* 导航链接 */}
          <div class={css({ display: 'flex', gap: '2rem' })}>
            {navItems.map(item => (
              <a
                href={item.path}
                class={css({
                  color: currentPath() === item.path ? 'blue.400' : 'gray.300',
                  textDecoration: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  transition: 'color 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  _hover: {
                    color: currentPath() === item.path ? 'blue.400' : 'gray.100'
                  }
                })}
              >
                <span>{item.icon}</span>
                {item.label}
              </a>
            ))}
          </div>

          {/* 工具按钮 */}
          <div class={css({ display: 'flex', alignItems: 'center', gap: '0.5rem' })}>
            {/* 语言切换 */}
            <button
              onClick={() => setLanguage(language() === 'zh-CN' ? 'en-US' : 'zh-CN')}
              class={css({
                background: 'transparent',
                border: '1px solid gray.600',
                color: 'gray.300',
                padding: '0.5rem',
                borderRadius: '0.375rem',
                cursor: 'pointer',
                fontSize: '14px',
                transition: 'all 0.2s',
                _hover: {
                  background: 'gray.700',
                  color: 'white'
                }
              })}
            >
              {language() === 'zh-CN' ? 'EN' : '中'}
            </button>

            {/* 主题切换 */}
            <button
              onClick={toggleTheme}
              class={css({
                background: 'transparent',
                border: '1px solid gray.600',
                color: 'gray.300',
                padding: '0.5rem',
                borderRadius: '0.375rem',
                cursor: 'pointer',
                fontSize: '16px',
                transition: 'all 0.2s',
                _hover: {
                  background: 'gray.700',
                  color: 'white'
                }
              })}
            >
              {theme() === 'dark' ? '☀️' : '🌙'}
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

// 主应用组件
const App: Component = () => {
  onMount(() => {
    console.log('🚀 量化交易平台已启动');
    console.log('用户状态:', userStore);
    console.log('市场数据:', marketStore);
  });

  return (
    <div class={css({
      minHeight: '100vh',
      background: 'gray.50',
      color: 'gray.900',
      fontFamily: 'Inter, system-ui, sans-serif'
    })}>
      <Router>
        <Navigation />
        <main>
          <Route path="/" component={Dashboard} />
          <Route path="/trading" component={Trading} />
          <Route path="/strategy" component={Strategy} />
        </main>
      </Router>
    </div>
  );
};

export default App;