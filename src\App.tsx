import { createSignal } from 'solid-js';
import { css } from '../styled-system/css';

// 简单的测试页面
function TestDashboard() {
  const [count, setCount] = createSignal(0);

  return (
    <div class={css({
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    })}>
      {/* 页面标题 */}
      <div class={css({
        marginBottom: '32px'
      })}>
        <h1 class={css({
          fontSize: '32px',
          fontWeight: 'bold',
          color: 'gray.900',
          marginBottom: '8px'
        })}>
          🚀 量化交易平台
        </h1>
        <p class={css({
          fontSize: '16px',
          color: 'gray.600'
        })}>
          SolidJS 框架运行正常，点击计数器测试响应性
        </p>
      </div>

      {/* 测试计数器 */}
      <div class={css({
        backgroundColor: 'white',
        padding: '24px',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        marginBottom: '24px'
      })}>
        <h2 class={css({
          fontSize: '20px',
          fontWeight: 'bold',
          marginBottom: '16px'
        })}>
          响应性测试
        </h2>
        <p class={css({
          fontSize: '18px',
          marginBottom: '16px'
        })}>
          计数器: {count()}
        </p>
        <button
          onClick={() => setCount(count() + 1)}
          class={css({
            padding: '12px 24px',
            backgroundColor: 'blue.600',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.2s',
            _hover: {
              backgroundColor: 'blue.700'
            }
          })}
        >
          点击增加
        </button>
      </div>

      {/* 功能状态 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '24px'
      })}>
        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '8px',
            color: 'green.600'
          })}>
            ✅ SolidJS 框架
          </h3>
          <p class={css({
            fontSize: '14px',
            color: 'gray.600'
          })}>
            响应式框架正常运行
          </p>
        </div>

        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '8px',
            color: 'green.600'
          })}>
            ✅ Panda CSS
          </h3>
          <p class={css({
            fontSize: '14px',
            color: 'gray.600'
          })}>
            样式系统正常工作
          </p>
        </div>

        <div class={css({
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb'
        })}>
          <h3 class={css({
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '8px',
            color: 'orange.600'
          })}>
            ⚠️ 路由系统
          </h3>
          <p class={css({
            fontSize: '14px',
            color: 'gray.600'
          })}>
            暂时简化，待修复Context后启用
          </p>
        </div>
      </div>
    </div>
  );
}

export default function App() {
  return <TestDashboard />;
}
